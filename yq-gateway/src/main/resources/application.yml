# Tomcat
server:
  port: 8080
  servlet:
    context-path: /

# Spring
spring:
  application:
    # 应用名称
    name: yq-gateway
  profiles:
    # 环境配置
    active: @profiles.active@

---
# nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ${NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      discovery:
        # 注册组
        group: ${NACOS_DISCOVERY_GROUP}
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: ${NACOS_CONFIG_GROUP}
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
