<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>yq</groupId>
        <artifactId>yq-cloud</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>yq-monitor</module>
        <module>yq-sentinel-dashboard</module>
        <module>yq-seata-server</module>
        <module>yq-nacos</module>
        <module>yq-snailjob-server</module>
    </modules>

    <artifactId>yq-visual</artifactId>
    <packaging>pom</packaging>

    <description>
        yq-visual图形化管理模块
    </description>

    <dependencies>
        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-prometheus</artifactId>-->
<!--        </dependency>-->
    </dependencies>

</project>
