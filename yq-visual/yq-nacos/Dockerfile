#FROM ibm-semeru-runtimes:open-21-jre
FROM bellsoft/liberica-openjdk-debian:21.0.3-cds

MAINTAINER William <PERSON>

RUN mkdir -p /yq/nacos

WORKDIR /yq/nacos

EXPOSE 8848

ENV TZ=Asia/Shanghai \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    JAVA_OPTS="-Xms512m -Xmx1024m" \
    MYSQL_SERVICE_HOST=127.0.0.1 \
    MYSQL_SERVICE_PORT=3306 \
    MYSQL_SERVICE_DB_NAME=xbus-config \
    MYSQL_SERVICE_USER=root \
    MYSQL_SERVICE_PASSWORD=123456 \
    SPRING_ADMIN_CLIENT_URL="http://127.0.0.1:9100" \
    SPRING_ADMIN_CLIENT_USERNAME=yq \
    SPRING_ADMIN_CLIENT_PASSWORD=123456 \
    NACOS_AUTH_IDENTITY_KEY=serverIdentity \
    NACOS_AUTH_IDENTITY_VALUE=security \
    NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789

ADD ./target/yq-nacos.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom ${JAVA_OPTS} -jar app.jar

