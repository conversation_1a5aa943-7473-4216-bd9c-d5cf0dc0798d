/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * monaco-json version: 1.3.2(63d6dadc9cc5928c83a232dd95cdc31403f08974)
 * Released under the MIT license
 * https://github.com/Microsoft/monaco-json/blob/master/LICENSE.md
 *-----------------------------------------------------------------------------*/
!function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-languageserver-types/main",["require","exports"],e)}(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n;!function(e){function t(e,t){return{line:e,character:t}}function n(e){var t=e;return P.defined(t)&&P.number(t.line)&&P.number(t.character)}e.create=t,e.is=n}(n=t.Position||(t.Position={}));var r;!function(e){function t(e,t,r,i){if(P.number(e)&&P.number(t)&&P.number(r)&&P.number(i))return{start:n.create(e,t),end:n.create(r,i)};if(n.is(e)&&n.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+r+", "+i+"]")}function r(e){var t=e;return P.defined(t)&&n.is(t.start)&&n.is(t.end)}e.create=t,e.is=r}(r=t.Range||(t.Range={}));var i;!function(e){function t(e,t){return{uri:e,range:t}}function n(e){var t=e;return P.defined(t)&&r.is(t.range)&&(P.string(t.uri)||P.undefined(t.uri))}e.create=t,e.is=n}(i=t.Location||(t.Location={}));var o;!function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(o=t.DiagnosticSeverity||(t.DiagnosticSeverity={}));var a;!function(e){function t(e,t,n,r,i){var o={range:e,message:t};return P.defined(n)&&(o.severity=n),P.defined(r)&&(o.code=r),P.defined(i)&&(o.source=i),o}function n(e){var t=e;return P.defined(t)&&r.is(t.range)&&P.string(t.message)&&(P.number(t.severity)||P.undefined(t.severity))&&(P.number(t.code)||P.string(t.code)||P.undefined(t.code))&&(P.string(t.source)||P.undefined(t.source))}e.create=t,e.is=n}(a=t.Diagnostic||(t.Diagnostic={}));var s;!function(e){function t(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return P.defined(n)&&n.length>0&&(i.arguments=n),i}function n(e){var t=e;return P.defined(t)&&P.string(t.title)&&P.string(t.title)}e.create=t,e.is=n}(s=t.Command||(t.Command={}));var c;!function(e){function t(e,t){return{range:e,newText:t}}function n(e,t){return{range:{start:e,end:e},newText:t}}function r(e){return{range:e,newText:""}}e.replace=t,e.insert=n,e.del=r}(c=t.TextEdit||(t.TextEdit={}));var u;!function(e){function t(e,t){return{textDocument:e,edits:t}}function n(e){var t=e;return P.defined(t)&&p.is(t.textDocument)&&Array.isArray(t.edits)}e.create=t,e.is=n}(u=t.TextDocumentEdit||(t.TextDocumentEdit={}));var l=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(c.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(c.replace(e,t))},e.prototype["delete"]=function(e){this.edits.push(c.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}(),f=function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach(function(e){var n=new l(e.edits);t._textEditChanges[e.textDocument.uri]=n}):e.changes&&Object.keys(e.changes).forEach(function(n){var r=new l(e.changes[n]);t._textEditChanges[n]=r}))}return Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(p.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for versioned document changes.");var t=e,n=this._textEditChanges[t.uri];if(!n){var r=[],i={textDocument:t,edits:r};this._workspaceEdit.documentChanges.push(i),n=new l(r),this._textEditChanges[t.uri]=n}return n}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var n=this._textEditChanges[e];if(!n){var r=[];this._workspaceEdit.changes[e]=r,n=new l(r),this._textEditChanges[e]=n}return n},e}();t.WorkspaceChange=f;var d;!function(e){function t(e){return{uri:e}}function n(e){var t=e;return P.defined(t)&&P.string(t.uri)}e.create=t,e.is=n}(d=t.TextDocumentIdentifier||(t.TextDocumentIdentifier={}));var p;!function(e){function t(e,t){return{uri:e,version:t}}function n(e){var t=e;return P.defined(t)&&P.string(t.uri)&&P.number(t.version)}e.create=t,e.is=n}(p=t.VersionedTextDocumentIdentifier||(t.VersionedTextDocumentIdentifier={}));var h;!function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}function n(e){var t=e;return P.defined(t)&&P.string(t.uri)&&P.string(t.languageId)&&P.number(t.version)&&P.string(t.text)}e.create=t,e.is=n}(h=t.TextDocumentItem||(t.TextDocumentItem={}));var m;!function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18}(m=t.CompletionItemKind||(t.CompletionItemKind={}));var g;!function(e){e.PlainText=1,e.Snippet=2}(g=t.InsertTextFormat||(t.InsertTextFormat={}));var v;!function(e){function t(e){return{label:e}}e.create=t}(v=t.CompletionItem||(t.CompletionItem={}));var y;!function(e){function t(e,t){return{items:e?e:[],isIncomplete:!!t}}e.create=t}(y=t.CompletionList||(t.CompletionList={}));var x;!function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}e.fromPlainText=t}(x=t.MarkedString||(t.MarkedString={}));var b;!function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t}(b=t.ParameterInformation||(t.ParameterInformation={}));var S;!function(e){function t(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return P.defined(t)&&(i.documentation=t),P.defined(n)?i.parameters=n:i.parameters=[],i}e.create=t}(S=t.SignatureInformation||(t.SignatureInformation={}));var T;!function(e){e.Text=1,e.Read=2,e.Write=3}(T=t.DocumentHighlightKind||(t.DocumentHighlightKind={}));var C;!function(e){function t(e,t){var n={range:e};return P.number(t)&&(n.kind=t),n}e.create=t}(C=t.DocumentHighlight||(t.DocumentHighlight={}));var k;!function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18}(k=t.SymbolKind||(t.SymbolKind={}));var O;!function(e){function t(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}e.create=t}(O=t.SymbolInformation||(t.SymbolInformation={}));var E;!function(e){function t(e){return{diagnostics:e}}function n(e){var t=e;return P.defined(t)&&P.typedArray(t.diagnostics,a.is)}e.create=t,e.is=n}(E=t.CodeActionContext||(t.CodeActionContext={}));var j;!function(e){function t(e,t){var n={range:e};return P.defined(t)&&(n.data=t),n}function n(e){var t=e;return P.defined(t)&&r.is(t.range)&&(P.undefined(t.command)||s.is(t.command))}e.create=t,e.is=n}(j=t.CodeLens||(t.CodeLens={}));var I;!function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}function n(e){var t=e;return P.defined(t)&&P.number(t.tabSize)&&P["boolean"](t.insertSpaces)}e.create=t,e.is=n}(I=t.FormattingOptions||(t.FormattingOptions={}));var w=function(){function e(){}return e}();t.DocumentLink=w,function(e){function t(e,t){return{range:e,target:t}}function n(e){var t=e;return P.defined(t)&&r.is(t.range)&&(P.undefined(t.target)||P.string(t.target))}e.create=t,e.is=n}(w=t.DocumentLink||(t.DocumentLink={})),t.DocumentLink=w,t.EOL=["\n","\r\n","\r"];var A;!function(e){function t(e,t,n,r){return new V(e,t,n,r)}function n(e){var t=e;return!!(P.defined(t)&&P.string(t.uri)&&(P.undefined(t.languageId)||P.string(t.languageId))&&P.number(t.lineCount)&&P.func(t.getText)&&P.func(t.positionAt)&&P.func(t.offsetAt))}e.create=t,e.is=n}(A=t.TextDocument||(t.TextDocument={}));var _;!function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(_=t.TextDocumentSaveReason||(t.TextDocumentSaveReason={}));var P,V=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=null}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(){return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=null},e.prototype.getLineOffsets=function(){if(null===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),r=0,i=t.length;if(0===i)return n.create(0,e);for(;r<i;){var o=Math.floor((r+i)/2);t[o]>e?i=o:r=o+1}var a=r-1;return n.create(a,e-t[a])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();!function(e){function t(e){return"undefined"!=typeof e}function n(e){return"undefined"==typeof e}function r(e){return e===!0||e===!1}function i(e){return"[object String]"===c.call(e)}function o(e){return"[object Number]"===c.call(e)}function a(e){return"[object Function]"===c.call(e)}function s(e,t){return Array.isArray(e)&&e.every(t)}var c=Object.prototype.toString;e.defined=t,e.undefined=n,e["boolean"]=r,e.string=i,e.number=o,e.func=a,e.typedArray=s}(P||(P={}))}),define("vscode-languageserver-types",["vscode-languageserver-types/main"],function(e){return e}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-nls/vscode-nls",["require","exports"],e)}(function(e,t){function n(e,t){var n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,function(e,n){var r=n[0];return"undefined"!=typeof t[r]?t[r]:e})}function r(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return n(t,r)}function i(e){return r}function o(e){return i}Object.defineProperty(t,"__esModule",{value:!0}),t.loadMessageBundle=i,t.config=o}),define("vscode-nls",["vscode-nls/vscode-nls"],function(e){return e}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/main",["require","exports","vscode-nls"],e)}(function(e,t){function n(e,t){function n(t,n){for(var r=0,i=0;r<t||!n;){var o=e.charCodeAt(d);if(o>=48&&o<=57)i=16*i+o-48;else if(o>=65&&o<=70)i=16*i+o-65+10;else{if(!(o>=97&&o<=102))break;i=16*i+o-97+10}d++,r++}return r<t&&(i=-1),i}function a(e){d=e,h="",g=0,v=y.Unknown,x=m.None}function s(){var t=d;if(48===e.charCodeAt(d))d++;else for(d++;d<e.length&&o(e.charCodeAt(d));)d++;if(d<e.length&&46===e.charCodeAt(d)){if(d++,!(d<e.length&&o(e.charCodeAt(d))))return x=m.UnexpectedEndOfNumber,e.substring(t,d);for(d++;d<e.length&&o(e.charCodeAt(d));)d++}var n=d;if(d<e.length&&(69===e.charCodeAt(d)||101===e.charCodeAt(d)))if(d++,(d<e.length&&43===e.charCodeAt(d)||45===e.charCodeAt(d))&&d++,d<e.length&&o(e.charCodeAt(d))){for(d++;d<e.length&&o(e.charCodeAt(d));)d++;n=d}else x=m.UnexpectedEndOfNumber;return e.substring(t,n)}function c(){for(var t="",r=d;;){if(d>=p){t+=e.substring(r,d),x=m.UnexpectedEndOfString;break}var o=e.charCodeAt(d);if(34===o){t+=e.substring(r,d),d++;break}if(92!==o){if(o>=0&&o<=31){if(i(o)){t+=e.substring(r,d),x=m.UnexpectedEndOfString;break}x=m.InvalidCharacter}d++}else{if(t+=e.substring(r,d),d++,d>=p){x=m.UnexpectedEndOfString;break}switch(o=e.charCodeAt(d++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var a=n(4,!0);a>=0?t+=String.fromCharCode(a):x=m.InvalidUnicode;break;default:x=m.InvalidEscapeCharacter}r=d}}return t}function u(){if(h="",x=m.None,g=d,d>=p)return g=p,v=y.EOF;var t=e.charCodeAt(d);if(r(t)){do d++,h+=String.fromCharCode(t),t=e.charCodeAt(d);while(r(t));return v=y.Trivia}if(i(t))return d++,h+=String.fromCharCode(t),13===t&&10===e.charCodeAt(d)&&(d++,h+="\n"),v=y.LineBreakTrivia;switch(t){case 123:return d++,v=y.OpenBraceToken;case 125:return d++,v=y.CloseBraceToken;case 91:return d++,v=y.OpenBracketToken;case 93:return d++,v=y.CloseBracketToken;case 58:return d++,v=y.ColonToken;case 44:return d++,v=y.CommaToken;case 34:return d++,h=c(),v=y.StringLiteral;case 47:var n=d-1;if(47===e.charCodeAt(d+1)){for(d+=2;d<p&&!i(e.charCodeAt(d));)d++;return h=e.substring(n,d),v=y.LineCommentTrivia}if(42===e.charCodeAt(d+1)){d+=2;for(var a=p-1,u=!1;d<a;){var f=e.charCodeAt(d);if(42===f&&47===e.charCodeAt(d+1)){d+=2,u=!0;break}d++}return u||(d++,x=m.UnexpectedEndOfComment),h=e.substring(n,d),v=y.BlockCommentTrivia}return h+=String.fromCharCode(t),d++,v=y.Unknown;case 45:if(h+=String.fromCharCode(t),d++,d===p||!o(e.charCodeAt(d)))return v=y.Unknown;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return h+=s(),v=y.NumericLiteral;default:for(;d<p&&l(t);)d++,t=e.charCodeAt(d);if(g!==d){switch(h=e.substring(g,d)){case"true":return v=y.TrueKeyword;case"false":return v=y.FalseKeyword;case"null":return v=y.NullKeyword}return v=y.Unknown}return h+=String.fromCharCode(t),d++,v=y.Unknown}}function l(e){if(r(e)||i(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:return!1}return!0}function f(){var e;do e=u();while(e>=y.LineCommentTrivia&&e<=y.Trivia);return e}void 0===t&&(t=!1);var d=0,p=e.length,h="",g=0,v=y.Unknown,x=m.None;return{setPosition:a,getPosition:function(){return d},scan:t?f:u,getToken:function(){return v},getTokenValue:function(){return h},getTokenOffset:function(){return g},getTokenLength:function(){return d-g},getTokenError:function(){return x}}}function r(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function i(e){return 10===e||13===e||8232===e||8233===e}function o(e){return e>=48&&e<=57}function a(e,t){var r,i,o=n(e),a=[],s=0;do switch(i=o.getPosition(),r=o.scan()){case y.LineCommentTrivia:case y.BlockCommentTrivia:case y.EOF:s!==i&&a.push(e.substring(s,i)),void 0!==t&&a.push(o.getTokenValue().replace(/[^\r\n]/g,t)),s=o.getPosition()}while(r!==y.EOF);return a.join("")}function s(e){switch(e){case x.InvalidSymbol:return v("error.invalidSymbol","Invalid symbol");case x.InvalidNumberFormat:return v("error.invalidNumberFormat","Invalid number format");case x.PropertyNameExpected:return v("error.propertyNameExpected","Property name expected");case x.ValueExpected:return v("error.valueExpected","Value expected");case x.ColonExpected:return v("error.colonExpected","Colon expected");case x.CommaExpected:return v("error.commaExpected","Comma expected");case x.CloseBraceExpected:return v("error.closeBraceExpected","Closing brace expected");case x.CloseBracketExpected:return v("error.closeBracketExpected","Closing bracket expected");case x.EndOfFileExpected:return v("error.endOfFileExpected","End of file expected");default:return""}}function c(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";default:return"null"}}function u(e,t){function n(e,t,n,r){a.value=e,a.offset=t,a.length=n,a.type=r,a.columnOffset=void 0,o=a}var r=[],i=new Object,o=void 0,a={value:void 0,offset:void 0,length:void 0,type:void 0},s=!1;try{h(e,{onObjectBegin:function(e,n){if(t<=e)throw i;o=void 0,s=t>e,r.push("")},onObjectProperty:function(e,o,a){if(t<o)throw i;if(n(e,o,a,"property"),r[r.length-1]=e,t<=o+a)throw i},onObjectEnd:function(e,n){if(t<=e)throw i;o=void 0,r.pop()},onArrayBegin:function(e,n){if(t<=e)throw i;o=void 0,r.push(0)},onArrayEnd:function(e,n){if(t<=e)throw i;o=void 0,r.pop()},onLiteralValue:function(e,r,o){if(t<r)throw i;if(n(e,r,o,c(e)),t<=r+o)throw i},onSeparator:function(e,n,a){if(t<=n)throw i;if(":"===e&&"property"===o.type)o.columnOffset=n,s=!1,o=void 0;else if(","===e){var c=r[r.length-1];"number"==typeof c?r[r.length-1]=c+1:(s=!0,r[r.length-1]=""),o=void 0}}})}catch(u){if(u!==i)throw u}return{path:r,previousNode:o,isAtPropertyKey:s,matches:function(e){for(var t=0,n=0;t<e.length&&n<r.length;n++)if(e[t]===r[n]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}}function l(e,t,n){function r(e){Array.isArray(o)?o.push(e):i&&(o[i]=e)}void 0===t&&(t=[]);var i=null,o=[],a=[],s={onObjectBegin:function(){var e={};r(e),a.push(o),o=e,i=null},onObjectProperty:function(e){i=e},onObjectEnd:function(){o=a.pop()},onArrayBegin:function(){var e=[];r(e),a.push(o),o=e,i=null},onArrayEnd:function(){o=a.pop()},onLiteralValue:r,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}};return h(e,s,n),o[0]}function f(e,t,n){function r(e){"property"===o.type&&(o.length=e-o.offset,o=o.parent)}function i(e){return o.children.push(e),e}void 0===t&&(t=[]);var o={type:"array",offset:-1,length:-1,children:[]},a={onObjectBegin:function(e){o=i({type:"object",offset:e,length:-1,parent:o,children:[]})},onObjectProperty:function(e,t,n){o=i({type:"property",offset:t,length:-1,parent:o,children:[]}),o.children.push({type:"string",value:e,offset:t,length:n,parent:o})},onObjectEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,r(e+t)},onArrayBegin:function(e,t){o=i({type:"array",offset:e,length:-1,parent:o,children:[]})},onArrayEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,r(e+t)},onLiteralValue:function(e,t,n){i({type:c(e),offset:t,length:n,parent:o,value:e}),r(t+n)},onSeparator:function(e,t,n){"property"===o.type&&(":"===e?o.columnOffset=t:","===e&&r(t))},onError:function(e,n,r){t.push({error:e,offset:n,length:r})}};h(e,a,n);var s=o.children[0];return s&&delete s.parent,s}function d(e,t){if(e){for(var n=e,r=0,i=t;r<i.length;r++){var o=i[r];if("string"==typeof o){if("object"!==n.type)return;for(var a=!1,s=0,c=n.children;s<c.length;s++){var u=c[s];if(u.children[0].value===o){n=u.children[1],a=!0;break}}if(!a)return}else{var l=o;if("array"!==n.type||l<0||l>=n.children.length)return;n=n.children[l]}}return n}}function p(e){if("array"===e.type)return e.children.map(p);if("object"===e.type){for(var t={},n=0,r=e.children;n<r.length;n++){var i=r[n];t[i.children[0].value]=p(i.children[1])}return t}return e.value}function h(e,t,r){function i(e){return e?function(){return e(h.getTokenOffset(),h.getTokenLength())}:function(){return!0}}function o(e){return e?function(t){return e(t,h.getTokenOffset(),h.getTokenLength())}:function(){return!0}}function a(){for(;;){var e=h.scan();switch(e){case y.LineCommentTrivia:case y.BlockCommentTrivia:O&&s(x.InvalidSymbol);break;case y.Unknown:s(x.InvalidSymbol);break;case y.Trivia:case y.LineBreakTrivia:break;default:return e}}}function s(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),k(e),t.length+n.length>0)for(var r=h.getToken();r!==y.EOF;){if(t.indexOf(r)!==-1){a();break}if(n.indexOf(r)!==-1)break;r=a()}}function c(e){var t=h.getTokenValue();return e?T(t):g(t),a(),!0}function u(){switch(h.getToken()){case y.NumericLiteral:var e=0;try{e=JSON.parse(h.getTokenValue()),"number"!=typeof e&&(s(x.InvalidNumberFormat),e=0)}catch(t){s(x.InvalidNumberFormat)}T(e);break;case y.NullKeyword:T(null);break;case y.TrueKeyword:T(!0);break;case y.FalseKeyword:T(!1);break;default:return!1}return a(),!0}function l(){return h.getToken()!==y.StringLiteral?(s(x.PropertyNameExpected,[],[y.CloseBraceToken,y.CommaToken]),!1):(c(!1),h.getToken()===y.ColonToken?(C(":"),a(),p()||s(x.ValueExpected,[],[y.CloseBraceToken,y.CommaToken])):s(x.ColonExpected,[],[y.CloseBraceToken,y.CommaToken]),!0)}function f(){m(),a();for(var e=!1;h.getToken()!==y.CloseBraceToken&&h.getToken()!==y.EOF;){if(h.getToken()===y.CommaToken){if(e||s(x.ValueExpected,[],[]),C(","),a(),h.getToken()===y.CloseBraceToken&&E)break}else e&&s(x.CommaExpected,[],[]);l()||s(x.ValueExpected,[],[y.CloseBraceToken,y.CommaToken]),e=!0}return v(),h.getToken()!==y.CloseBraceToken?s(x.CloseBraceExpected,[y.CloseBraceToken],[]):a(),!0}function d(){b(),a();for(var e=!1;h.getToken()!==y.CloseBracketToken&&h.getToken()!==y.EOF;)h.getToken()===y.CommaToken?(e||s(x.ValueExpected,[],[]),C(","),a()):e&&s(x.CommaExpected,[],[]),p()||s(x.ValueExpected,[],[y.CloseBracketToken,y.CommaToken]),e=!0;return S(),h.getToken()!==y.CloseBracketToken?s(x.CloseBracketExpected,[y.CloseBracketToken],[]):a(),!0}function p(){switch(h.getToken()){case y.OpenBracketToken:return d();case y.OpenBraceToken:return f();case y.StringLiteral:return c(!0);default:return u()}}var h=n(e,!1),m=i(t.onObjectBegin),g=o(t.onObjectProperty),v=i(t.onObjectEnd),b=i(t.onArrayBegin),S=i(t.onArrayEnd),T=o(t.onLiteralValue),C=o(t.onSeparator),k=o(t.onError),O=r&&r.disallowComments,E=r&&r.allowTrailingComma;return a(),h.getToken()===y.EOF||(p()?(h.getToken()!==y.EOF&&s(x.EndOfFileExpected,[],[]),!0):(s(x.ValueExpected,[],[]),!1))}var m,g=e("vscode-nls"),v=g.loadMessageBundle();!function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"}(m=t.ScanError||(t.ScanError={}));var y;!function(e){e[e.Unknown=0]="Unknown",e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.EOF=16]="EOF"}(y=t.SyntaxKind||(t.SyntaxKind={})),t.createScanner=n,t.stripComments=a;var x;!function(e){e[e.InvalidSymbol=0]="InvalidSymbol",e[e.InvalidNumberFormat=1]="InvalidNumberFormat",e[e.PropertyNameExpected=2]="PropertyNameExpected",e[e.ValueExpected=3]="ValueExpected",e[e.ColonExpected=4]="ColonExpected",e[e.CommaExpected=5]="CommaExpected",e[e.CloseBraceExpected=6]="CloseBraceExpected",e[e.CloseBracketExpected=7]="CloseBracketExpected",e[e.EndOfFileExpected=8]="EndOfFileExpected"}(x=t.ParseErrorCode||(t.ParseErrorCode={})),t.getParseErrorMessage=s,t.getLocation=u,t.parse=l,t.parseTree=f,t.findNodeAtLocation=d,t.getNodeValue=p,t.visit=h}),define("jsonc-parser",["jsonc-parser/main"],function(e){return e}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/json",["require","exports"],e)}(function(e,t){function n(e,t,r){if(null!==e&&"object"==typeof e){var i=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";for(var o="[\n",a=0;a<e.length;a++)o+=i+n(e[a],i,r),a<e.length-1&&(o+=","),o+="\n";return o+=t+"]"}var s=Object.keys(e);if(0===s.length)return"{}";for(var o="{\n",a=0;a<s.length;a++){var c=s[a];o+=i+JSON.stringify(c)+": "+n(e[c],i,r),a<s.length-1&&(o+=","),o+="\n"}return o+=t+"}"}return r(e)}t.stringifyObject=n}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonCompletion",["require","exports","jsonc-parser","../utils/json","vscode-languageserver-types","vscode-nls"],e)}(function(e,t){var n=e("jsonc-parser"),r=e("../utils/json"),i=e("vscode-languageserver-types"),o=e("vscode-nls"),a=o.loadMessageBundle(),s=function(){function e(e,t,n){void 0===t&&(t=[]),this.templateVarIdCounter=0,this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--)if(this.contributions[t].resolveCompletion){var n=this.contributions[t].resolveCompletion(e);if(n)return n}return this.promise.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,o={items:[],isIncomplete:!1},a=e.offsetAt(t),s=n.getNodeFromOffsetEndInclusive(a);if(this.isInComment(e,s?s.start:0,a))return Promise.resolve(o);var c=this.getCurrentWord(e,a),u=null;if(!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type){var l=a-c.length;l>0&&'"'===e.getText()[l-1]&&l--,u=i.Range.create(e.positionAt(l),t)}else u=i.Range.create(e.positionAt(s.start),e.positionAt(s.end));var f={},d={add:function(e){var t=f[e.label];t?t.documentation||(t.documentation=e.documentation):(f[e.label]=e,u&&(e.textEdit=i.TextEdit.replace(u,e.insertText)),o.items.push(e))},setAsIncomplete:function(){o.isIncomplete=!0},error:function(e){console.error(e)},log:function(e){console.log(e)},getNumberOfProposals:function(){return o.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then(function(t){var l=[],p=!0,h="",m=null;if(s&&"string"===s.type){var g=s;g.isKey&&(p=!(s.parent&&s.parent.value),m=s.parent?s.parent:null,h=e.getText().substring(s.start+1,s.end-1),s.parent&&(s=s.parent.parent))}if(s&&"object"===s.type){if(s.start===a)return o;var v=s.properties;v.forEach(function(e){m&&m===e||(f[e.key.value]=i.CompletionItem.create("__"))});var y="";p&&(y=r.evaluateSeparatorAfter(e,e.offsetAt(u.end))),t?r.getPropertyCompletions(t,n,s,p,y,d):r.getSchemaLessPropertyCompletions(n,s,h,d);var x=s.getPath();r.contributions.forEach(function(t){var n=t.collectPropertyCompletions(e.uri,x,c,p,""===y,d);n&&l.push(n)}),!t&&c.length>0&&'"'!==e.getText().charAt(a-c.length-1)&&d.add({kind:i.CompletionItemKind.Property,label:r.getLabelForValue(c),insertText:r.getInsertTextForProperty(c,null,!1,y),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""})}var b=a;!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type||(b=s.end,s=s.parent);var S="";s&&(S=r.evaluateSeparatorAfter(e,b));var T={};if(t?r.getValueCompletions(t,n,s,a,S,d,T):r.getSchemaLessValueCompletions(n,s,a,S,e,d),s){if("property"===s.type&&a>s.colonOffset){var C=s.key.value,k=s.value;if(!k||a<=k.end){var O=s.parent.getPath();r.contributions.forEach(function(t){var n=t.collectValueCompletions(e.uri,O,C,d);n&&l.push(n)})}}}else r.contributions.forEach(function(t){var n=t.collectDefaultCompletions(e.uri,d);n&&l.push(n)});return r.promise.all(l).then(function(){return 0===d.getNumberOfProposals()&&r.addFillerValueCompletions(T,S,d),o})})},e.prototype.getPropertyCompletions=function(e,t,n,r,o,a){var s=this,c=[];t.validate(e.schema,c,n.start),c.forEach(function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach(function(e){var n=t[e];n.deprecationMessage||a.add({kind:i.CompletionItemKind.Property,label:e,insertText:s.getInsertTextForProperty(e,n,r,o),insertTextFormat:i.InsertTextFormat.Snippet,filterText:s.getFilterTextForValue(e),documentation:n.description||""})})}})},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var o=this,a=function(e){e.properties.forEach(function(e){var t=e.key.value;r.add({kind:i.CompletionItemKind.Property,label:t,insertText:o.getInsertTextForValue(t,""),insertTextFormat:i.InsertTextFormat.Snippet,filterText:o.getFilterTextForValue(t),documentation:""})})};if(t.parent)if("property"===t.parent.type){var s=t.parent.key.value;e.visit(function(e){var n=e;return"property"===e.type&&e!==t.parent&&n.key.value===s&&n.value&&"object"===n.value.type&&a(n.value),!0})}else"array"===t.parent.type&&t.parent.items.forEach(function(e){"object"===e.type&&e!==t&&a(e)});else"object"===t.type&&r.add({kind:i.CompletionItemKind.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",null,!0,""),insertTextFormat:i.InsertTextFormat.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,o,a){var s=this,c=function(e){e.parent.contains(n,!0)||a.add({kind:s.getSuggestionKind(e.type),label:s.getLabelTextForMatchingNode(e,o),insertText:s.getInsertTextForMatchingNode(e,o,r),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""}),"boolean"===e.type&&s.addBooleanValueCompletion(!e.getValue(),r,a)};if(t){if("property"===t.type){var u=t;if(n>u.colonOffset){var l=u.value;if(l&&(n>l.end||"object"===l.type||"array"===l.type))return;var f=u.key.value;e.visit(function(e){var t=e;return"property"===e.type&&t.key.value===f&&t.value&&c(t.value),!0}),"$schema"===f&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(r,a)}}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var d=t.parent.key.value;e.visit(function(e){var t=e;return"property"===e.type&&t.key.value===d&&t.value&&"array"===t.value.type&&t.value.items.forEach(function(e){c(e)}),!0})}else t.items.forEach(function(e){c(e)})}else a.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""}),a.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""})},e.prototype.getValueCompletions=function(e,t,n,r,i,o,a){var s=this;if(n){var c=null;if("property"===n.type&&r>n.colonOffset){var u=n.value;if(u&&r>u.end)return;c=n.key.value,n=n.parent}if(n&&(null!==c||"array"===n.type)){var l=[];t.validate(e.schema,l,n.start),l.forEach(function(e){if(e.node===n&&!e.inverted&&e.schema&&(e.schema.items&&s.addSchemaValueCompletions(e.schema.items,i,o,a),e.schema.properties)){var t=e.schema.properties[c];t&&s.addSchemaValueCompletions(t,i,o,a)}}),"$schema"!==c||n.parent||this.addDollarSchemaCompletions(i,o),a["boolean"]&&(this.addBooleanValueCompletion(!0,i,o),this.addBooleanValueCompletion(!1,i,o)),a["null"]&&this.addNullValueCompletion(i,o)}}else this.addSchemaValueCompletions(e.schema,"",o,a)},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;this.addDefaultValueCompletions(e,t,n),this.addEnumValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach(function(e){return i.addSchemaValueCompletions(e,t,n,r)}),Array.isArray(e.anyOf)&&e.anyOf.forEach(function(e){return i.addSchemaValueCompletions(e,t,n,r)}),Array.isArray(e.oneOf)&&e.oneOf.forEach(function(e){return i.addSchemaValueCompletions(e,t,n,r)})},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var o=this;void 0===r&&(r=0);var s=!1;if(e["default"]){for(var c=e.type,u=e["default"],l=r;l>0;l--)u=[u],c="array";n.add({kind:this.getSuggestionKind(c),label:this.getLabelForValue(u),insertText:this.getInsertTextForValue(u,t),insertTextFormat:i.InsertTextFormat.Snippet,detail:a("json.suggest.default","Default value")}),s=!0}Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach(function(a){var c,u=e.type,l=a.body,f=a.label;if(l){for(var d=e.type,p=r;p>0;p--)l=[l],d="array";c=o.getInsertTextForSnippetValue(l,t),f=f||o.getLabelForSnippetValue(l)}else if(a.bodyText){for(var h="",m="",g="",p=r;p>0;p--)h=h+g+"[\n",m=m+"\n"+g+"]",g+="\t",u="array";c=h+g+a.bodyText.split("\n").join("\n"+g)+m+t,f=f||c}n.add({kind:o.getSuggestionKind(u),label:f,documentation:a.description,insertText:c,insertTextFormat:i.InsertTextFormat.Snippet,filterText:c}),s=!0}),s||!e.items||Array.isArray(e.items)||this.addDefaultValueCompletions(e.items,t,n,r+1);
},e.prototype.addEnumValueCompletions=function(e,t,n){if(Array.isArray(e["enum"]))for(var r=0,o=e["enum"].length;r<o;r++){var a=e["enum"][r],s=e.description;e.enumDescriptions&&r<e.enumDescriptions.length&&(s=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(a),insertText:this.getInsertTextForValue(a,t),insertTextFormat:i.InsertTextFormat.Snippet,documentation:s})}},e.prototype.collectTypes=function(e,t){var n=e.type;Array.isArray(n)?n.forEach(function(e){return t[e]=!0}):t[n]=!0},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:i.InsertTextFormat.Snippet,detail:a("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:i.InsertTextFormat.Snippet,detail:a("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:i.InsertTextFormat.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this,r=this.schemaService.getRegisteredSchemaIds(function(e){return"http"===e||"https"===e});r.forEach(function(r){return t.add({kind:i.CompletionItemKind.Module,label:n.getLabelForValue(r),filterText:JSON.stringify(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:i.InsertTextFormat.Snippet,documentation:""})})},e.prototype.getLabelForValue=function(e){var t=JSON.stringify(e);return t.length>57?t.substr(0,57).trim()+"...":t},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getLabelForSnippetValue=function(e){var t=JSON.stringify(e);return t=t.replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1"),t.length>57?t.substr(0,57).trim()+"...":t},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{\n\t$1\n}"+t:"[]"===n?"[\n\t$1\n]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){var n=function(e){return"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)};return r.stringifyObject(e,"",n)+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),n=this.getInsertTextForPlainText(n),'"${1:'+n+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:null}if(!e)return i.CompletionItemKind.Value;switch(e){case"string":return i.CompletionItemKind.Value;case"object":return i.CompletionItemKind.Module;case"property":return i.CompletionItemKind.Property;default:return i.CompletionItemKind.Value}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:var n=t.getText().substr(e.start,e.end-e.start);return n}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.start,e.end-e.start)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var o=i+": ";if(t){var a=t["default"];if("undefined"!=typeof a)o+=this.getInsertTextForGuessedValue(a,"");else if(t["enum"]&&t["enum"].length>0)o+=this.getInsertTextForGuessedValue(t["enum"][0],"");else{var s=Array.isArray(t.type)?t.type[0]:t.type;switch(s||(t.properties?s="object":t.items&&(s="array")),s){case"boolean":o+="${1:false}";break;case"string":o+='"$1"';break;case"object":o+="{\n\t$1\n}";break;case"array":o+="[\n\t$1\n]";break;case"number":case"integer":o+="${1:0}";break;case"null":o+="${1:null}";break;default:return i}}}else o+="$1";return o+=r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&' \t\n\r\x0B":{[,]}'.indexOf(r.charAt(n))===-1;)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var r=n.createScanner(e.getText(),!0);r.setPosition(t);var i=r.scan();switch(i){case n.SyntaxKind.CommaToken:case n.SyntaxKind.CloseBraceToken:case n.SyntaxKind.CloseBracketToken:case n.SyntaxKind.EOF:return"";default:return","}},e.prototype.isInComment=function(e,t,r){var i=n.createScanner(e.getText(),!1);i.setPosition(t);for(var o=i.scan();o!==n.SyntaxKind.EOF&&i.getTokenOffset()+i.getTokenLength()<r;)o=i.scan();return(o===n.SyntaxKind.LineCommentTrivia||o===n.SyntaxKind.BlockCommentTrivia)&&i.getTokenOffset()<=r},e}();t.JSONCompletion=s}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonHover",["require","exports","vscode-languageserver-types"],e)}(function(e,t){function n(e){var t=e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3");return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}var r=e("vscode-languageserver-types"),i=function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doHover=function(e,t,i){var o=e.offsetAt(t),a=i.getNodeFromOffset(o);if(!a||("object"===a.type||"array"===a.type)&&o>a.start+1&&o<a.end-1)return this.promise.resolve(void 0);var s=a;if("string"===a.type){var c=a;if(c.isKey){var u=a.parent;if(a=u.value,!a)return this.promise.resolve(void 0)}}for(var l=r.Range.create(e.positionAt(s.start),e.positionAt(s.end)),f=function(e){var t={contents:e,range:l};return t},d=a.getPath(),p=this.contributions.length-1;p>=0;p--){var h=this.contributions[p],m=h.getInfoContribution(e.uri,d);if(m)return m.then(function(e){return f(e)})}return this.schemaService.getSchemaForResource(e.uri,i).then(function(e){if(e){var t=[];i.validate(e.schema,t,a.start);var r=null,o=null,s=null,c=null;t.every(function(e){if(e.node===a&&!e.inverted&&e.schema&&(r=r||e.schema.title,o=o||e.schema.description,e.schema["enum"]&&e.schema.enumDescriptions)){var t=e.schema["enum"].indexOf(a.getValue());s=e.schema.enumDescriptions[t],c=e.schema["enum"][t],"string"!=typeof c&&(c=JSON.stringify(c))}return!0});var u="";return r&&(u=n(r)),o&&(u.length>0&&(u+="\n\n"),u+=n(o)),s&&(u.length>0&&(u+="\n\n"),u+="`"+n(c)+"`: "+n(s)),f([u])}})},e}();t.JSONHover=i}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonValidation",["require","exports","vscode-languageserver-types"],e)}(function(e,t){var n=e("vscode-languageserver-types"),r=function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}return e.prototype.configure=function(e){e&&(this.validationEnabled=e.validate)},e.prototype.doValidation=function(e,t){return this.validationEnabled?this.jsonSchemaService.getSchemaForResource(e.uri,t).then(function(r){if(r)if(r.errors.length&&t.root){var i=t.root,o="object"===i.type?i.getFirstProperty("$schema"):null;if(o){var a=o.value||o;t.warnings.push({location:{start:a.start,end:a.end},message:r.errors[0]})}else t.warnings.push({location:{start:i.start,end:i.start+1},message:r.errors[0]})}else t.validate(r.schema);var s=[],c={};return t.errors.concat(t.warnings).forEach(function(r,i){var o=r.location.start+" "+r.location.end+" "+r.message;if(!c[o]){c[o]=!0;var a={start:e.positionAt(r.location.start),end:e.positionAt(r.location.end)};s.push({severity:i>=t.errors.length?n.DiagnosticSeverity.Warning:n.DiagnosticSeverity.Error,range:a,message:r.message})}}),s}):this.promise.resolve([])},e}();t.JSONValidation=r}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/strings",["require","exports"],e)}(function(e,t){function n(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function r(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function i(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}function o(e,t){for(var n="";t>0;)1===(1&t)&&(n+=e),e+=e,t>>>=1;return n}t.startsWith=n,t.endsWith=r,t.convertSimple2RegExpPattern=i,t.repeat=o}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonDocumentSymbols",["require","exports","../utils/strings","vscode-languageserver-types"],e)}(function(e,t){var n=e("../utils/strings"),r=e("vscode-languageserver-types"),i=function(){function e(e){this.schemaService=e}return e.prototype.findDocumentSymbols=function(e,t){var i=this,o=t.root;if(!o)return null;var a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||n.endsWith(a.toLowerCase(),"/user/keybindings.json"))&&"array"===o.type){var s=[];return o.items.forEach(function(t){if("object"===t.type){var n=t.getFirstProperty("key");if(n&&n.value){var i=r.Location.create(e.uri,r.Range.create(e.positionAt(t.start),e.positionAt(t.end)));s.push({name:n.value.getValue(),kind:r.SymbolKind.Function,location:i})}}}),s}var c=function(t,n,o){if("array"===n.type)n.items.forEach(function(e){c(t,e,o)});else if("object"===n.type){var a=n;a.properties.forEach(function(n){var a=r.Location.create(e.uri,r.Range.create(e.positionAt(n.start),e.positionAt(n.end))),s=n.value;if(s){var u=o?o+"."+n.key.value:n.key.value;t.push({name:n.key.getValue(),kind:i.getSymbolKind(s.type),location:a,containerName:o}),c(t,s,u)}})}return t},u=c([],o,void 0);return u},e.prototype.getSymbolKind=function(e){switch(e){case"object":return r.SymbolKind.Module;case"string":return r.SymbolKind.String;case"number":return r.SymbolKind.Number;case"array":return r.SymbolKind.Array;case"boolean":return r.SymbolKind.Boolean;default:return r.SymbolKind.Variable}},e.prototype.findColorSymbols=function(e,t){return this.schemaService.getSchemaForResource(e.uri,t).then(function(n){var i=[];if(n){var o=[];t.validate(n.schema,o);for(var a={},s=0,c=o;s<c.length;s++){var u=c[s];if(!u.inverted&&u.schema&&"color"===u.schema.format&&u.node&&"string"===u.node.type){var l=String(u.node.start);a[l]||(i.push(r.Range.create(e.positionAt(u.node.start),e.positionAt(u.node.end))),a[l]=!0)}}}return i})},e}();t.JSONDocumentSymbols=i}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/objects",["require","exports"],e)}(function(e,t){function n(e,t){if(e===t)return!0;if(null===e||void 0===e||null===t||void 0===t)return!1;if(typeof e!=typeof t)return!1;if("object"!=typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var r,i;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(r=0;r<e.length;r++)if(!n(e[r],t[r]))return!1}else{var o=[];for(i in e)o.push(i);o.sort();var a=[];for(i in t)a.push(i);if(a.sort(),!n(o,a))return!1;for(r=0;r<o.length;r++)if(!n(e[o[r]],t[o[r]]))return!1}return!0}t.equals=n});var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)};!function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/parser/jsonParser",["require","exports","jsonc-parser","../utils/objects","vscode-nls"],e)}(function(e,t){function n(e,t){function n(){for(;;){var e=E.scan();switch(e){case i.SyntaxKind.LineCommentTrivia:case i.SyntaxKind.BlockCommentTrivia:j&&a(s("InvalidCommentTokem","Comments are not allowed"),r.CommentsNotAllowed);break;case i.SyntaxKind.Trivia:case i.SyntaxKind.LineBreakTrivia:break;default:return e}}}function o(e){return E.getToken()===e&&(n(),!0)}function a(t,r,o,a,s){if(void 0===o&&(o=null),void 0===a&&(a=[]),void 0===s&&(s=[]),0===O.errors.length||O.errors[0].location.start!==E.getTokenOffset()){var c=E.getTokenOffset(),u=E.getTokenOffset()+E.getTokenLength();if(c===u&&c>0){for(c--;c>0&&/\s/.test(e.charAt(c));)c--;u=c+1}O.errors.push({message:t,location:{start:c,end:u},code:r})}if(o&&g(o,!1),a.length+s.length>0)for(var l=E.getToken();l!==i.SyntaxKind.EOF;){if(a.indexOf(l)!==-1){n();break}if(s.indexOf(l)!==-1)break;l=n()}return o}function c(){switch(E.getTokenError()){case i.ScanError.InvalidUnicode:return a(s("InvalidUnicode","Invalid unicode sequence in string"),r.InvalidUnicode),!0;case i.ScanError.InvalidEscapeCharacter:return a(s("InvalidEscapeCharacter","Invalid escape character in string"),r.InvalidEscapeCharacter),!0;case i.ScanError.UnexpectedEndOfNumber:return a(s("UnexpectedEndOfNumber","Unexpected end of number"),r.UnexpectedEndOfNumber),!0;case i.ScanError.UnexpectedEndOfComment:return a(s("UnexpectedEndOfComment","Unexpected end of comment"),r.UnexpectedEndOfComment),!0;case i.ScanError.UnexpectedEndOfString:return a(s("UnexpectedEndOfString","Unexpected end of string"),r.UnexpectedEndOfString),!0;case i.ScanError.InvalidCharacter:return a(s("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),r.InvalidCharacter),!0}return!1}function g(e,t){return e.end=E.getTokenOffset()+E.getTokenLength(),t&&n(),e}function y(e,t){if(E.getToken()!==i.SyntaxKind.OpenBracketToken)return null;var c=new f(e,t,E.getTokenOffset());n();var u=0;if(c.addItem(k(c,u++)))for(;o(i.SyntaxKind.CommaToken);)c.addItem(k(c,u++))||I||a(s("ValueExpected","Value expected"),r.Undefined);return E.getToken()!==i.SyntaxKind.CloseBracketToken?a(s("ExpectedCloseBracket","Expected comma or closing bracket"),r.Undefined,c):g(c,!0)}function x(e,t){var o=S(null,null,!0);if(!o){if(E.getToken()===i.SyntaxKind.Unknown){var c=E.getTokenValue();c.match(/^['\w]/)&&a(s("DoubleQuotesExpected","Property keys must be doublequoted"),r.Undefined)}return null}var u=new h(e,o);return t[o.value]&&O.warnings.push({location:{start:u.key.start,end:u.key.end},message:s("DuplicateKeyWarning","Duplicate object key"),code:r.Undefined}),t[o.value]=!0,E.getToken()!==i.SyntaxKind.ColonToken?a(s("ColonExpected","Colon expected"),r.Undefined,u,[],[i.SyntaxKind.CloseBraceToken,i.SyntaxKind.CommaToken]):(u.colonOffset=E.getTokenOffset(),n(),u.setValue(k(u,o.value))?(u.end=u.value.end,u):a(s("ValueExpected","Value expected"),r.Undefined,u,[],[i.SyntaxKind.CloseBraceToken,i.SyntaxKind.CommaToken]))}function b(e,t){if(E.getToken()!==i.SyntaxKind.OpenBraceToken)return null;var c=new m(e,t,E.getTokenOffset());n();var u=Object.create(null);if(c.addProperty(x(c,u)))for(;o(i.SyntaxKind.CommaToken);)c.addProperty(x(c,u))||I||a(s("PropertyExpected","Property expected"),r.Undefined);return E.getToken()!==i.SyntaxKind.CloseBraceToken?a(s("ExpectedCloseBrace","Expected comma or closing brace"),r.Undefined,c):g(c,!0)}function S(e,t,n){if(E.getToken()!==i.SyntaxKind.StringLiteral)return null;var r=new p(e,t,n,E.getTokenOffset());return r.value=E.getTokenValue(),c(),g(r,!0)}function T(e,t){if(E.getToken()!==i.SyntaxKind.NumericLiteral)return null;var n=new d(e,t,E.getTokenOffset());if(!c()){var o=E.getTokenValue();try{var u=JSON.parse(o);if("number"!=typeof u)return a(s("InvalidNumberFormat","Invalid number format"),r.Undefined,n);n.value=u}catch(l){return a(s("InvalidNumberFormat","Invalid number format"),r.Undefined,n)}n.isInteger=o.indexOf(".")===-1}return g(n,!0)}function C(e,t){var n;switch(E.getToken()){case i.SyntaxKind.NullKeyword:n=new u(e,t,E.getTokenOffset());break;case i.SyntaxKind.TrueKeyword:n=new l(e,t,(!0),E.getTokenOffset());break;case i.SyntaxKind.FalseKeyword:n=new l(e,t,(!1),E.getTokenOffset());break;default:return null}return g(n,!0)}function k(e,t){return y(e,t)||b(e,t)||S(e,t,!1)||T(e,t)||C(e,t)}var O=new v(t),E=i.createScanner(e,!1),j=t&&t.disallowComments,I=t&&t.ignoreDanglingComma;return n(),O.root=k(null,null),O.root?E.getToken()!==i.SyntaxKind.EOF&&a(s("End of file expected","End of file expected"),r.Undefined):a(s("Invalid symbol","Expected a JSON object, array or literal"),r.Undefined),O}var r,i=e("jsonc-parser"),o=e("../utils/objects"),a=e("vscode-nls"),s=a.loadMessageBundle();!function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.CommentsNotAllowed=2]="CommentsNotAllowed",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter"}(r=t.ErrorCode||(t.ErrorCode={}));var c=function(){function e(e,t,n,r,i){this.type=t,this.location=n,this.start=r,this.end=i,this.parent=e}return e.prototype.getPath=function(){var e=this.parent?this.parent.getPath():[];return null!==this.location&&e.push(this.location),e},e.prototype.getChildNodes=function(){return[]},e.prototype.getLastChild=function(){return null},e.prototype.getValue=function(){},e.prototype.contains=function(e,t){return void 0===t&&(t=!1),e>=this.start&&e<this.end||t&&e===this.end},e.prototype.toString=function(){return"type: "+this.type+" ("+this.start+"/"+this.end+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e.prototype.visit=function(e){return e(this)},e.prototype.getNodeFromOffset=function(e){var t=function(n){if(e>=n.start&&e<n.end){for(var r=n.getChildNodes(),i=0;i<r.length&&r[i].start<=e;i++){var o=t(r[i]);if(o)return o}return n}return null};return t(this)},e.prototype.getNodeFromOffsetEndInclusive=function(e){var t=function(n){if(e>=n.start&&e<=n.end){for(var r=n.getChildNodes(),i=0;i<r.length&&r[i].start<=e;i++){var o=t(r[i]);if(o)return o}return n}return null};return t(this)},e.prototype.validate=function(e,t,n,i){var a=this;if(void 0===i&&(i=-1),i===-1||this.contains(i)){if(Array.isArray(e.type)?e.type.indexOf(this.type)===-1&&t.warnings.push({location:{start:this.start,end:this.end},message:e.errorMessage||s("typeArrayMismatchWarning","Incorrect type. Expected one of {0}",e.type.join(", "))}):e.type&&this.type!==e.type&&t.warnings.push({location:{start:this.start,end:this.end},message:e.errorMessage||s("typeMismatchWarning",'Incorrect type. Expected "{0}"',e.type)}),Array.isArray(e.allOf)&&e.allOf.forEach(function(e){a.validate(e,t,n,i)}),e.not){var c=new g,u=[];this.validate(e.not,c,u,i),c.hasErrors()||t.warnings.push({location:{start:this.start,end:this.end},message:s("notSchemaWarning","Matches a schema that is not allowed.")}),n&&u.forEach(function(e){e.inverted=!e.inverted,n.push(e)})}var l=function(e,r){var i=[],o=null;return e.forEach(function(e){var t=new g,n=[];if(a.validate(e,t,n),t.hasErrors()||i.push(e),o)if(r||t.hasErrors()||o.validationResult.hasErrors()){var s=t.compare(o.validationResult);s>0?o={schema:e,validationResult:t,matchingSchemas:n}:0===s&&(o.matchingSchemas.push.apply(o.matchingSchemas,n),o.validationResult.mergeEnumValueMismatch(t))}else o.matchingSchemas.push.apply(o.matchingSchemas,n),o.validationResult.propertiesMatches+=t.propertiesMatches,o.validationResult.propertiesValueMatches+=t.propertiesValueMatches;else o={schema:e,validationResult:t,matchingSchemas:n}}),i.length>1&&r&&t.warnings.push({location:{start:a.start,end:a.start+1},message:s("oneOfWarning","Matches multiple schemas when only one must validate.")}),null!==o&&(t.merge(o.validationResult),t.propertiesMatches+=o.validationResult.propertiesMatches,t.propertiesValueMatches+=o.validationResult.propertiesValueMatches,n&&n.push.apply(n,o.matchingSchemas)),i.length};if(Array.isArray(e.anyOf)&&l(e.anyOf,!1),Array.isArray(e.oneOf)&&l(e.oneOf,!0),Array.isArray(e["enum"])){for(var f=this.getValue(),d=!1,p=0,h=e["enum"];p<h.length;p++){var m=h[p];if(o.equals(f,m)){d=!0;break}}d?t.enumValueMatch=!0:(t.warnings.push({location:{start:this.start,end:this.end},code:r.EnumValueMismatch,message:e.errorMessage||s("enumWarning","Value is not accepted. Valid values: {0}",JSON.stringify(e["enum"]))}),t.mismatchedEnumValues=e["enum"])}e.deprecationMessage&&this.parent&&t.warnings.push({location:{start:this.parent.start,end:this.parent.end},message:e.deprecationMessage}),null!==n&&n.push({node:this,schema:e})}},e}();t.ASTNode=c;var u=function(e){function t(t,n,r,i){return e.call(this,t,"null",n,r,i)||this}return __extends(t,e),t.prototype.getValue=function(){return null},t}(c);t.NullASTNode=u;var l=function(e){function t(t,n,r,i,o){var a=e.call(this,t,"boolean",n,i,o)||this;return a.value=r,a}return __extends(t,e),t.prototype.getValue=function(){return this.value},t}(c);t.BooleanASTNode=l;var f=function(e){function t(t,n,r,i){var o=e.call(this,t,"array",n,r,i)||this;return o.items=[],o}return __extends(t,e),t.prototype.getChildNodes=function(){return this.items},t.prototype.getLastChild=function(){return this.items[this.items.length-1]},t.prototype.getValue=function(){return this.items.map(function(e){return e.getValue()})},t.prototype.addItem=function(e){return!!e&&(this.items.push(e),!0)},t.prototype.visit=function(e){for(var t=e(this),n=0;n<this.items.length&&t;n++)t=this.items[n].visit(e);return t},t.prototype.validate=function(t,n,r,i){var o=this;if(void 0===i&&(i=-1),i===-1||this.contains(i)){if(e.prototype.validate.call(this,t,n,r,i),Array.isArray(t.items)){var a=t.items;a.forEach(function(e,t){var s=new g,c=o.items[t];c?(c.validate(e,s,r,i),n.mergePropertyMatch(s)):o.items.length>=a.length&&n.propertiesValueMatches++}),t.additionalItems===!1&&this.items.length>a.length?n.warnings.push({location:{start:this.start,end:this.end},message:s("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer",a.length)}):this.items.length>=a.length&&(n.propertiesValueMatches+=this.items.length-a.length)}else t.items&&this.items.forEach(function(e){var o=new g;e.validate(t.items,o,r,i),n.mergePropertyMatch(o)});if(t.minItems&&this.items.length<t.minItems&&n.warnings.push({location:{start:this.start,end:this.end},message:s("minItemsWarning","Array has too few items. Expected {0} or more",t.minItems)}),t.maxItems&&this.items.length>t.maxItems&&n.warnings.push({location:{start:this.start,end:this.end},message:s("maxItemsWarning","Array has too many items. Expected {0} or fewer",t.minItems)}),t.uniqueItems===!0){var c=this.items.map(function(e){return e.getValue()}),u=c.some(function(e,t){return t!==c.lastIndexOf(e)});u&&n.warnings.push({location:{start:this.start,end:this.end},message:s("uniqueItemsWarning","Array has duplicate items")})}}},t}(c);t.ArrayASTNode=f;var d=function(e){function t(t,n,r,i){var o=e.call(this,t,"number",n,r,i)||this;return o.isInteger=!0,o.value=Number.NaN,o}return __extends(t,e),t.prototype.getValue=function(){return this.value},t.prototype.validate=function(t,n,r,i){if(void 0===i&&(i=-1),i===-1||this.contains(i)){var o=!1;("integer"===t.type||Array.isArray(t.type)&&t.type.indexOf("integer")!==-1)&&(o=!0),o&&this.isInteger===!0&&(this.type="integer"),e.prototype.validate.call(this,t,n,r,i),this.type="number";var a=this.getValue();"number"==typeof t.multipleOf&&a%t.multipleOf!==0&&n.warnings.push({location:{start:this.start,end:this.end},message:s("multipleOfWarning","Value is not divisible by {0}",t.multipleOf)}),"number"==typeof t.minimum&&(t.exclusiveMinimum&&a<=t.minimum&&n.warnings.push({location:{start:this.start,end:this.end},message:s("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}",t.minimum)}),!t.exclusiveMinimum&&a<t.minimum&&n.warnings.push({location:{start:this.start,end:this.end},message:s("minimumWarning","Value is below the minimum of {0}",t.minimum)})),"number"==typeof t.maximum&&(t.exclusiveMaximum&&a>=t.maximum&&n.warnings.push({location:{start:this.start,end:this.end},message:s("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}",t.maximum)}),!t.exclusiveMaximum&&a>t.maximum&&n.warnings.push({location:{start:this.start,end:this.end},message:s("maximumWarning","Value is above the maximum of {0}",t.maximum)}))}},t}(c);t.NumberASTNode=d;var p=function(e){function t(t,n,r,i,o){var a=e.call(this,t,"string",n,i,o)||this;return a.isKey=r,a.value="",a}return __extends(t,e),t.prototype.getValue=function(){return this.value},t.prototype.validate=function(t,n,r,i){if(void 0===i&&(i=-1),(i===-1||this.contains(i))&&(e.prototype.validate.call(this,t,n,r,i),t.minLength&&this.value.length<t.minLength&&n.warnings.push({location:{start:this.start,end:this.end},message:s("minLengthWarning","String is shorter than the minimum length of {0}",t.minLength)}),t.maxLength&&this.value.length>t.maxLength&&n.warnings.push({location:{start:this.start,end:this.end},message:s("maxLengthWarning","String is longer than the maximum length of {0}",t.maxLength)}),t.pattern)){var o=new RegExp(t.pattern);o.test(this.value)||n.warnings.push({location:{start:this.start,end:this.end},message:t.patternErrorMessage||t.errorMessage||s("patternWarning",'String does not match the pattern of "{0}"',t.pattern)})}},t}(c);t.StringASTNode=p;var h=function(e){function t(t,n){var r=e.call(this,t,"property",null,n.start)||this;return r.key=n,n.parent=r,n.location=n.value,r.colonOffset=-1,r}return __extends(t,e),t.prototype.getChildNodes=function(){return this.value?[this.key,this.value]:[this.key]},t.prototype.getLastChild=function(){return this.value},t.prototype.setValue=function(e){return this.value=e,null!==e},t.prototype.visit=function(e){return e(this)&&this.key.visit(e)&&this.value&&this.value.visit(e)},t.prototype.validate=function(e,t,n,r){void 0===r&&(r=-1),(r===-1||this.contains(r))&&this.value&&this.value.validate(e,t,n,r)},t}(c);t.PropertyASTNode=h;var m=function(e){function t(t,n,r,i){var o=e.call(this,t,"object",n,r,i)||this;return o.properties=[],o}return __extends(t,e),t.prototype.getChildNodes=function(){return this.properties},t.prototype.getLastChild=function(){return this.properties[this.properties.length-1]},t.prototype.addProperty=function(e){return!!e&&(this.properties.push(e),!0)},t.prototype.getFirstProperty=function(e){for(var t=0;t<this.properties.length;t++)if(this.properties[t].key.value===e)return this.properties[t];return null},t.prototype.getKeyList=function(){return this.properties.map(function(e){return e.key.getValue()})},t.prototype.getValue=function(){var e=Object.create(null);return this.properties.forEach(function(t){var n=t.value&&t.value.getValue();"undefined"!=typeof n&&(e[t.key.getValue()]=n)}),e},t.prototype.visit=function(e){for(var t=e(this),n=0;n<this.properties.length&&t;n++)t=this.properties[n].visit(e);return t},t.prototype.validate=function(t,n,r,i){var o=this;if(void 0===i&&(i=-1),i===-1||this.contains(i)){e.prototype.validate.call(this,t,n,r,i);var a=Object.create(null),c=[];this.properties.forEach(function(e){var t=e.key.value;a[t]=e.value,c.push(t)}),Array.isArray(t.required)&&t.required.forEach(function(e){if(!a[e]){var t=o.parent&&o.parent&&o.parent.key,r=t?{start:t.start,end:t.end}:{start:o.start,end:o.start+1};n.warnings.push({location:r,message:s("MissingRequiredPropWarning",'Missing property "{0}"',e)})}});var u=function(e){for(var t=c.indexOf(e);t>=0;)c.splice(t,1),t=c.indexOf(e)};t.properties&&Object.keys(t.properties).forEach(function(e){u(e);var o=t.properties[e],s=a[e];if(s){var c=new g;s.validate(o,c,r,i),n.mergePropertyMatch(c)}}),t.patternProperties&&Object.keys(t.patternProperties).forEach(function(e){var o=new RegExp(e);c.slice(0).forEach(function(s){if(o.test(s)){u(s);var c=a[s];if(c){var l=new g;c.validate(t.patternProperties[e],l,r,i),n.mergePropertyMatch(l)}}})}),"object"==typeof t.additionalProperties?c.forEach(function(e){var o=a[e];if(o){var s=new g;o.validate(t.additionalProperties,s,r,i),n.mergePropertyMatch(s)}}):t.additionalProperties===!1&&c.length>0&&c.forEach(function(e){var r=a[e];if(r){var i=r.parent;n.warnings.push({location:{start:i.key.start,end:i.key.end},message:t.errorMessage||s("DisallowedExtraPropWarning","Property {0} is not allowed",e)})}}),t.maxProperties&&this.properties.length>t.maxProperties&&n.warnings.push({location:{start:this.start,end:this.end},message:s("MaxPropWarning","Object has more properties than limit of {0}",t.maxProperties)}),t.minProperties&&this.properties.length<t.minProperties&&n.warnings.push({location:{start:this.start,end:this.end},message:s("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)}),t.dependencies&&Object.keys(t.dependencies).forEach(function(e){var c=a[e];if(c)if(Array.isArray(t.dependencies[e])){var u=t.dependencies[e];u.forEach(function(t){a[t]?n.propertiesValueMatches++:n.warnings.push({location:{start:o.start,end:o.end},message:s("RequiredDependentPropWarning","Object is missing property {0} required by property {1}",t,e)})})}else if(t.dependencies[e]){var l=t.dependencies[e],f=new g;o.validate(l,f,r,i),n.mergePropertyMatch(f)}})}},t}(c);t.ObjectASTNode=m;var g=function(){function e(){this.errors=[],this.warnings=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.enumValueMatch=!1,this.mismatchedEnumValues=null}return e.prototype.hasErrors=function(){return!!this.errors.length||!!this.warnings.length},e.prototype.mergeAll=function(e){var t=this;e.forEach(function(e){t.merge(e)})},e.prototype.merge=function(e){this.errors=this.errors.concat(e.errors),this.warnings=this.warnings.concat(e.warnings)},e.prototype.mergeEnumValueMismatch=function(e){if(this.mismatchedEnumValues&&e.mismatchedEnumValues){this.mismatchedEnumValues=this.mismatchedEnumValues.concat(e.mismatchedEnumValues);for(var t=0,n=this.warnings;t<n.length;t++){var i=n[t];i.code===r.EnumValueMismatch&&(i.message=s("enumWarning","Value is not accepted. Valid values: {0}",JSON.stringify(this.mismatchedEnumValues)))}}else this.mismatchedEnumValues=null},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasErrors()&&e.propertiesMatches)&&this.propertiesValueMatches++},e.prototype.compare=function(e){var t=this.hasErrors();return t!==e.hasErrors()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();t.ValidationResult=g;var v=function(){function e(e){this.validationResult=new g}return Object.defineProperty(e.prototype,"errors",{get:function(){return this.validationResult.errors},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"warnings",{get:function(){return this.validationResult.warnings},enumerable:!0,configurable:!0}),e.prototype.getNodeFromOffset=function(e){return this.root&&this.root.getNodeFromOffset(e)},e.prototype.getNodeFromOffsetEndInclusive=function(e){return this.root&&this.root.getNodeFromOffsetEndInclusive(e)},e.prototype.visit=function(e){this.root&&this.root.visit(e)},e.prototype.validate=function(e,t,n){void 0===t&&(t=null),void 0===n&&(n=-1),this.root&&this.root.validate(e,this.validationResult,t,n)},e}();t.JSONDocument=v,t.parse=n}),function(e){if("object"==typeof module&&"object"==typeof module.exports){
var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/configuration",["require","exports","vscode-nls"],e)}(function(e,t){var n=e("vscode-nls"),r=n.loadMessageBundle();t.schemaContributions={schemaAssociations:{},schemas:{"http://json-schema.org/draft-04/schema#":{title:r("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{"default":0}]},simpleTypes:{type:"string","enum":["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri",description:r("schema.json.id","A unique identifier for the schema.")},$schema:{type:"string",format:"uri",description:r("schema.json.$schema","The schema to verify this document against ")},title:{type:"string",description:r("schema.json.title","A descriptive title of the element")},description:{type:"string",description:r("schema.json.description","A long description of the element. Used in hover menus and suggestions.")},"default":{description:r("schema.json.default","A default value. Used by suggestions.")},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0,description:r("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)")},maximum:{type:"number",description:r("schema.json.maximum","The maximum numerical value, inclusive by default.")},exclusiveMaximum:{type:"boolean","default":!1,description:r("schema.json.exclusiveMaximum","Makes the maximum property exclusive.")},minimum:{type:"number",description:r("schema.json.minimum","The minimum numerical value, inclusive by default.")},exclusiveMinimum:{type:"boolean","default":!1,description:r("schema.json.exclusiveMininum","Makes the minimum property exclusive.")},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}],description:r("schema.json.maxLength","The maximum length of a string.")},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}],description:r("schema.json.minLength","The minimum length of a string.")},pattern:{type:"string",format:"regex",description:r("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored.")},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],"default":{},description:r("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail.")},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],"default":{},description:r("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on.")},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}],description:r("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive.")},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}],description:r("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive.")},uniqueItems:{type:"boolean","default":!1,description:r("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false.")},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}],description:r("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive.")},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}],description:r("schema.json.minProperties","The minimum number of properties an object can have. Inclusive.")},required:{allOf:[{$ref:"#/definitions/stringArray"}],description:r("schema.json.required","An array of strings that lists the names of all properties required on this object.")},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],"default":{},description:r("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail.")},definitions:{type:"object",additionalProperties:{$ref:"#"},"default":{},description:r("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref")},properties:{type:"object",additionalProperties:{$ref:"#"},"default":{},description:r("schema.json.properties","A map of property names to schemas for each property.")},patternProperties:{type:"object",additionalProperties:{$ref:"#"},"default":{},description:r("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties.")},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]},description:r("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object.")},"enum":{type:"array",minItems:1,uniqueItems:!0,description:r("schema.json.enum","The set of literal values that are valid")},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}],description:r("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types.")},format:{anyOf:[{type:"string",description:r("schema.json.format","Describes the format expected for the value."),"enum":["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}],description:r("schema.json.allOf","An array of schemas, all of which must match.")},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}],description:r("schema.json.anyOf","An array of schemas, where at least one must match.")},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}],description:r("schema.json.oneOf","An array of schemas, exactly one of which must match.")},not:{allOf:[{$ref:"#"}],description:r("schema.json.not","A schema which must not match.")}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},"default":{}}}}}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-uri/index",["require","exports"],e)}(function(e,t){function n(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}function r(e){return encodeURIComponent(e).replace(/[!'()*]/g,n)}function i(e){return e.replace(/[#?]/,n)}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(){this._scheme=e._empty,this._authority=e._empty,this._path=e._empty,this._query=e._empty,this._fragment=e._empty,this._formatted=null,this._fsPath=null}return e.isUri=function(t){return t instanceof e||!!t&&("string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme)},Object.defineProperty(e.prototype,"scheme",{get:function(){return this._scheme},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"authority",{get:function(){return this._authority},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"path",{get:function(){return this._path},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"query",{get:function(){return this._query},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fragment",{get:function(){return this._fragment},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fsPath",{get:function(){if(!this._fsPath){var t;t=this._authority&&this._path&&"file"===this.scheme?"//"+this._authority+this._path:e._driveLetterPath.test(this._path)?this._path[1].toLowerCase()+this._path.substr(2):this._path,a&&(t=t.replace(/\//g,"\\")),this._fsPath=t}return this._fsPath},enumerable:!0,configurable:!0}),e.prototype["with"]=function(t){if(!t)return this;var n=t.scheme,r=t.authority,i=t.path,o=t.query,a=t.fragment;if(void 0===n?n=this.scheme:null===n&&(n=""),void 0===r?r=this.authority:null===r&&(r=""),void 0===i?i=this.path:null===i&&(i=""),void 0===o?o=this.query:null===o&&(o=""),void 0===a?a=this.fragment:null===a&&(a=""),n===this.scheme&&r===this.authority&&i===this.path&&o===this.query&&a===this.fragment)return this;var s=new e;return s._scheme=n,s._authority=r,s._path=i,s._query=o,s._fragment=a,e._validate(s),s},e.parse=function(t){var n=new e,r=e._parseComponents(t);return n._scheme=r.scheme,n._authority=decodeURIComponent(r.authority),n._path=decodeURIComponent(r.path),n._query=decodeURIComponent(r.query),n._fragment=decodeURIComponent(r.fragment),e._validate(n),n},e.file=function(t){var n=new e;if(n._scheme="file",a&&(t=t.replace(/\\/g,e._slash)),t[0]===e._slash&&t[0]===t[1]){var r=t.indexOf(e._slash,2);r===-1?n._authority=t.substring(2):(n._authority=t.substring(2,r),n._path=t.substring(r))}else n._path=t;return n._path[0]!==e._slash&&(n._path=e._slash+n._path),e._validate(n),n},e._parseComponents=function(t){var n={scheme:e._empty,authority:e._empty,path:e._empty,query:e._empty,fragment:e._empty},r=e._regexp.exec(t);return r&&(n.scheme=r[2]||n.scheme,n.authority=r[4]||n.authority,n.path=r[5]||n.path,n.query=r[7]||n.query,n.fragment=r[9]||n.fragment),n},e.from=function(t){return(new e)["with"](t)},e._validate=function(t){if(t.scheme&&!e._schemePattern.test(t.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(t.path)if(t.authority){if(!e._singleSlashStart.test(t.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(e._doubleSlashStart.test(t.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')},e.prototype.toString=function(t){return void 0===t&&(t=!1),t?e._asFormatted(this,!0):(this._formatted||(this._formatted=e._asFormatted(this,!1)),this._formatted)},e._asFormatted=function(t,n){var o=n?i:r,a=[],s=t.scheme,c=t.authority,u=t.path,l=t.query,f=t.fragment;if(s&&a.push(s,":"),(c||"file"===s)&&a.push("//"),c){c=c.toLowerCase();var d=c.indexOf(":");d===-1?a.push(o(c)):a.push(o(c.substr(0,d)),c.substr(d))}if(u){var p=e._upperCaseDrive.exec(u);p&&(u=p[1]?"/"+p[2].toLowerCase()+u.substr(3):p[2].toLowerCase()+u.substr(2));for(var h=0;;){var d=u.indexOf(e._slash,h);if(d===-1){a.push(o(u.substring(h)));break}a.push(o(u.substring(h,d)),e._slash),h=d+1}}return l&&a.push("?",o(l)),f&&a.push("#",o(f)),a.join(e._empty)},e.prototype.toJSON=function(){var e={fsPath:this.fsPath,external:this.toString(),$mid:1};return this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},e.revive=function(t){var n=new e;return n._scheme=t.scheme||e._empty,n._authority=t.authority||e._empty,n._path=t.path||e._empty,n._query=t.query||e._empty,n._fragment=t.fragment||e._empty,n._fsPath=t.fsPath,n._formatted=t.external,e._validate(n),n},e}();o._empty="",o._slash="/",o._regexp=/^(([^:\/?#]+?):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,o._driveLetterPath=/^\/[a-zA-z]:/,o._upperCaseDrive=/^(\/)?([A-Z]:)/,o._schemePattern=/^\w[\w\d+.-]*$/,o._singleSlashStart=/^\//,o._doubleSlashStart=/^\/\//,t["default"]=o;var a;if("object"==typeof process)a="win32"===process.platform;else if("object"==typeof navigator){var s=navigator.userAgent;a=s.indexOf("Windows")>=0}}),define("vscode-uri",["vscode-uri/index"],function(e){return e}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonSchemaService",["require","exports","jsonc-parser","vscode-uri","../utils/strings","vscode-nls"],e)}(function(e,t){function n(e){try{var t=i["default"].parse(e);if("file"===t.scheme)return t.fsPath}catch(n){}return e}var r=e("jsonc-parser"),i=e("vscode-uri"),o=e("../utils/strings"),a=e("vscode-nls"),s=a.loadMessageBundle(),c=function(){function e(e){this.combinedSchemaId="schemaservice://combinedSchema/"+encodeURIComponent(e);try{this.patternRegExp=new RegExp(o.convertSimple2RegExpPattern(e)+"$")}catch(t){this.patternRegExp=null}this.schemas=[],this.combinedSchema=null}return e.prototype.addSchema=function(e){this.schemas.push(e),this.combinedSchema=null},e.prototype.matchesPattern=function(e){return this.patternRegExp&&this.patternRegExp.test(e)},e.prototype.getCombinedSchema=function(e){return this.combinedSchema||(this.combinedSchema=e.createCombinedSchema(this.combinedSchemaId,this.schemas)),this.combinedSchema},e}(),u=function(){function e(e,t,n){this.service=e,this.url=t,n&&(this.unresolvedSchema=this.service.promise.resolve(new l(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then(function(t){return e.service.resolveSchemaContent(t,e.url)})),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=null,this.unresolvedSchema=null},e}(),l=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e}();t.UnresolvedSchema=l;var f=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){return this.getSectionRecursive(e,this.schema)},e.prototype.getSectionRecursive=function(e,t){var n=this;if(!t||0===e.length)return t;var r=e.shift();if(t.properties&&t.properties[r])return this.getSectionRecursive(e,t.properties[r]);if(t.patternProperties)Object.keys(t.patternProperties).forEach(function(i){var o=new RegExp(i);if(o.test(r))return n.getSectionRecursive(e,t.patternProperties[i])});else{if(t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(r.match("[0-9]+")){if(t.items)return this.getSectionRecursive(e,t.items);if(Array.isArray(t.items))try{var i=parseInt(r,10);return t.items[i]?this.getSectionRecursive(e,t.items[i]):null}catch(o){return null}}}return null},e}();t.ResolvedSchema=f;var d=function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations={},this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter(function(t){var n=i["default"].parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))})},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){e=this.normalizeId(e);var t=this.schemasById[e];return!!t&&(t.clearSchema(),!0)},e.prototype.normalizeId=function(e){return i["default"].parse(e).toString()},e.prototype.setSchemaContributions=function(e){var t=this;if(e.schemas){var n=e.schemas;for(var r in n){var i=this.normalizeId(r);this.contributionSchemas[i]=this.addSchemaHandle(i,n[r])}}if(e.schemaAssociations){var o=e.schemaAssociations;for(var a in o){var s=o[a];this.contributionAssociations[a]=s;var c=this.getOrAddFilePatternAssociation(a);s.forEach(function(e){var n=t.normalizeId(e);c.addSchema(n)})}}},e.prototype.addSchemaHandle=function(e,t){var n=new u(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.getOrAddFilePatternAssociation=function(e){var t=this.filePatternAssociationById[e];return t||(t=new c(e),this.filePatternAssociationById[e]=t,this.filePatternAssociations.push(t)),t},e.prototype.registerExternalSchema=function(e,t,n){var r=this;void 0===t&&(t=null);var i=this.normalizeId(e);return this.registeredSchemasIds[i]=!0,t&&t.forEach(function(e){r.getOrAddFilePatternAssociation(e).addSchema(i)}),n?this.addSchemaHandle(i,n):this.getOrAddSchemaHandle(i)},e.prototype.clearExternalSchemas=function(){var e=this;this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={};for(var t in this.contributionSchemas)this.schemasById[t]=this.contributionSchemas[t],this.registeredSchemasIds[t]=!0;for(var n in this.contributionAssociations){var r=this.getOrAddFilePatternAssociation(n);this.contributionAssociations[n].forEach(function(t){var n=e.normalizeId(t);r.addSchema(n)})}},e.prototype.getResolvedSchema=function(e){var t=this.normalizeId(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(null)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=s("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",n(e));return this.promise.resolve(new l({},[t]))}return this.requestService(e).then(function(t){if(!t){var i=s("json.schema.nocontent","Unable to load schema from '{0}': No content.",n(e));return new l({},[i])}var o={},a=[];o=r.parse(t,a);var c=a.length?[s("json.schema.invalidFormat","Unable to parse content from '{0}': {1}.",n(e),r.getParseErrorMessage(a[0]))]:[];return new l(o,c)},function(t){var r=s("json.schema.unabletoload","Unable to load schema from '{0}': {1}",n(e),t.toString());return new l({},[r])})},e.prototype.resolveSchemaContent=function(e,t){var n=this,r=e.errors.slice(0),i=e.schema,o=this.contextService,a=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some(function(e){return n=n[e],!n}),n},c=function(e,t,n){var i=a(t,n);if(i)for(var o in i)i.hasOwnProperty(o)&&!e.hasOwnProperty(o)&&(e[o]=i[o]);else r.push(s("json.schema.invalidref","$ref '{0}' in {1} can not be resolved.",n,t.id));delete e.$ref},u=function(e,t,i,a){return o&&!/^\w+:\/\/.*/.test(t)&&(t=o.resolveRelativePath(t,a)),t=n.normalizeId(t),n.getOrAddSchemaHandle(t).getUnresolvedSchema().then(function(n){if(n.errors.length){var o=i?t+"#"+i:t;r.push(s("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,n.errors[0]))}return c(e,n.schema,i),l(e,n.schema,t)})},l=function(e,t,r){if(!e)return Promise.resolve(null);for(var i=[e],o=[],a=[],s=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];"object"==typeof o&&i.push(o)}},l=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];if("object"==typeof o)for(var a in o){var s=o[a];i.push(s)}}},f=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];Array.isArray(o)&&i.push.apply(i,o)}};i.length;){var d=i.pop();if(!(o.indexOf(d)>=0)){if(o.push(d),d.$ref){var p=d.$ref.split("#",2);if(p[0].length>0){a.push(u(d,p[0],p[1],r));continue}c(d,t,p[1])}s(d.items,d.additionalProperties,d.not),l(d.definitions,d.properties,d.patternProperties,d.dependencies),f(d.anyOf,d.allOf,d.oneOf,d.items)}}return n.promise.all(a)};return l(i,i,t).then(function(e){return new f(i,r)})},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter(function(e){return"$schema"===e.key.value&&!!e.value});if(n.length>0){var r=n[0].value.getValue();if(r&&o.startsWith(r,".")&&this.contextService&&(r=this.contextService.resolveRelativePath(r,e)),r){var i=this.normalizeId(r);return this.getOrAddSchemaHandle(i).getResolvedSchema()}}}for(var a=this.filePatternAssociations.length-1;a>=0;a--){var s=this.filePatternAssociations[a];if(s.matchesPattern(e))return s.getCombinedSchema(this).getResolvedSchema()}return this.promise.resolve(null)},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n={allOf:t.map(function(e){return{$ref:e}})};return this.addSchemaHandle(e,n)},e}();t.JSONSchemaService=d}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonFormatter",["require","exports","jsonc-parser","vscode-languageserver-types","../utils/strings"],e)}(function(e,t){function n(e,t,n){function u(){return T+c.repeat(S,d+k)}function l(){var e=O.scan();for(C=!1;e===a.SyntaxKind.Trivia||e===a.SyntaxKind.LineBreakTrivia;)C=C||e===a.SyntaxKind.LineBreakTrivia,e=O.scan();return E=e===a.SyntaxKind.Unknown||O.getTokenError()!==a.ScanError.None,e}function f(t,n,r){if(!E&&n<g&&r>m&&v.substring(n,r)!==t){var i=s.Range.create(e.positionAt(n),e.positionAt(r));j.push(s.TextEdit.replace(i,t))}}var d,p,h,m,g,v=e.getText();if(t){m=e.offsetAt(t.start),g=e.offsetAt(t.end);var y=s.Position.create(t.start.line,0);h=e.offsetAt(y);for(var x=e.offsetAt(s.Position.create(t.end.line+1,0)),b=e.offsetAt(s.Position.create(t.end.line,0));x>b&&o(v,x-1);)x--;p=v.substring(h,x),d=r(p,0,n)}else p=v,d=0,h=0,m=0,g=v.length;var S,T=i(e),C=!1,k=0;S=n.insertSpaces?c.repeat(" ",n.tabSize):"\t";var O=a.createScanner(p,!1),E=!1,j=[],I=l();if(I!==a.SyntaxKind.EOF){var w=O.getTokenOffset()+h,A=c.repeat(S,d);f(A,h,w)}for(;I!==a.SyntaxKind.EOF;){for(var _=O.getTokenOffset()+O.getTokenLength()+h,P=l(),V="";!C&&(P===a.SyntaxKind.LineCommentTrivia||P===a.SyntaxKind.BlockCommentTrivia);){var F=O.getTokenOffset()+h;f(" ",_,F),_=O.getTokenOffset()+O.getTokenLength()+h,V=P===a.SyntaxKind.LineCommentTrivia?u():"",P=l()}if(P===a.SyntaxKind.CloseBraceToken)I!==a.SyntaxKind.OpenBraceToken&&(k--,V=u());else if(P===a.SyntaxKind.CloseBracketToken)I!==a.SyntaxKind.OpenBracketToken&&(k--,V=u());else{switch(I){case a.SyntaxKind.OpenBracketToken:case a.SyntaxKind.OpenBraceToken:k++,V=u();break;case a.SyntaxKind.CommaToken:case a.SyntaxKind.LineCommentTrivia:V=u();break;case a.SyntaxKind.BlockCommentTrivia:V=C?u():" ";break;case a.SyntaxKind.ColonToken:V=" ";break;case a.SyntaxKind.StringLiteral:if(P===a.SyntaxKind.ColonToken){V="";break}case a.SyntaxKind.NullKeyword:case a.SyntaxKind.TrueKeyword:case a.SyntaxKind.FalseKeyword:case a.SyntaxKind.NumericLiteral:case a.SyntaxKind.CloseBraceToken:case a.SyntaxKind.CloseBracketToken:P===a.SyntaxKind.LineCommentTrivia||P===a.SyntaxKind.BlockCommentTrivia?V=" ":P!==a.SyntaxKind.CommaToken&&P!==a.SyntaxKind.EOF&&(E=!0)}!C||P!==a.SyntaxKind.LineCommentTrivia&&P!==a.SyntaxKind.BlockCommentTrivia||(V=u())}var K=O.getTokenOffset()+h;f(V,_,K),I=P}return j}function r(e,t,n){for(var r=0,i=0,o=n.tabSize||4;r<e.length;){var a=e.charAt(r);if(" "===a)i++;else{if("\t"!==a)break;i+=o}r++}return Math.floor(i/o)}function i(e){var t=e.getText();if(e.lineCount>1){for(var n=e.offsetAt(s.Position.create(1,0)),r=n;r>0&&o(t,r-1);)r--;return t.substr(r,n-r)}return"\n"}function o(e,t){return"\r\n".indexOf(e.charAt(t))!==-1}var a=e("jsonc-parser"),s=e("vscode-languageserver-types"),c=e("../utils/strings");t.format=n;[a.SyntaxKind.LineCommentTrivia,a.SyntaxKind.BlockCommentTrivia,a.SyntaxKind.CommaToken]}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/jsonLanguageService",["require","exports","vscode-languageserver-types","./services/jsonCompletion","./services/jsonHover","./services/jsonValidation","./services/jsonDocumentSymbols","./parser/jsonParser","./services/configuration","./services/jsonSchemaService","./services/jsonFormatter"],e)}(function(e,t){function n(e){var t=e.promiseConstructor||Promise,n=new l.JSONSchemaService(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(u.schemaContributions);var r=new i.JSONCompletion(n,e.contributions,t),d=new o.JSONHover(n,e.contributions,t),p=new s.JSONDocumentSymbols(n),h=new a.JSONValidation(n,t),m=!1;return{configure:function(e){n.clearExternalSchemas(),e.schemas&&e.schemas.forEach(function(e){n.registerExternalSchema(e.uri,e.fileMatch,e.schema)}),h.configure(e),m=e&&!e.allowComments},resetSchema:function(e){return n.onResourceChange(e)},doValidation:h.doValidation.bind(h),parseJSONDocument:function(e){return c.parse(e.getText(),{disallowComments:m})},doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:p.findDocumentSymbols.bind(p),findColorSymbols:p.findColorSymbols.bind(p),doHover:d.doHover.bind(d),format:f.format}}var r=e("vscode-languageserver-types");t.TextDocument=r.TextDocument,t.Position=r.Position,t.CompletionItem=r.CompletionItem,t.CompletionList=r.CompletionList,t.Range=r.Range,t.SymbolInformation=r.SymbolInformation,t.Diagnostic=r.Diagnostic,t.TextEdit=r.TextEdit,t.FormattingOptions=r.FormattingOptions,t.MarkedString=r.MarkedString;var i=e("./services/jsonCompletion"),o=e("./services/jsonHover"),a=e("./services/jsonValidation"),s=e("./services/jsonDocumentSymbols"),c=e("./parser/jsonParser"),u=e("./services/configuration"),l=e("./services/jsonSchemaService"),f=e("./services/jsonFormatter");t.getLanguageService=n}),define("vscode-json-languageservice",["vscode-json-languageservice/jsonLanguageService"],function(e){return e}),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vs/language/json/jsonWorker",["require","exports","vscode-json-languageservice","vscode-languageserver-types"],e)}(function(e,t){function n(e,t){return new s(e,t)}Object.defineProperty(t,"__esModule",{value:!0});var r=monaco.Promise,i=e("vscode-json-languageservice"),o=e("vscode-languageserver-types"),a=function(){function e(e){this.wrapped=new monaco.Promise(e)}return e.prototype.then=function(e,t){return this.wrapped.then(e,t)},e.prototype.getWrapped=function(){return this.wrapped},e.prototype.cancel=function(){this.wrapped.cancel()},e.resolve=function(e){return monaco.Promise.as(e)},e.reject=function(e){return monaco.Promise.wrapError(e)},e.all=function(e){return monaco.Promise.join(e)},e}(),s=function(){function e(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=i.getLanguageService({promiseConstructor:a}),this._languageService.configure(this._languageSettings)}return e.prototype.doValidation=function(e){var t=this._getTextDocument(e);if(t){var n=this._languageService.parseJSONDocument(t);return this._languageService.doValidation(t,n)}return r.as([])},e.prototype.doComplete=function(e,t){var n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n);return this._languageService.doComplete(n,t,r)},e.prototype.doResolve=function(e){return this._languageService.doResolve(e)},e.prototype.doHover=function(e,t){var n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n);return this._languageService.doHover(n,t,r)},e.prototype.format=function(e,t,n){var i=this._getTextDocument(e),o=this._languageService.format(i,t,n);return r.as(o)},e.prototype.resetSchema=function(e){return r.as(this._languageService.resetSchema(e))},e.prototype.findDocumentSymbols=function(e){var t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),i=this._languageService.findDocumentSymbols(t,n);return r.as(i)},e.prototype._getTextDocument=function(e){for(var t=this._ctx.getMirrorModels(),n=0,r=t;n<r.length;n++){var i=r[n];if(i.uri.toString()===e)return o.TextDocument.create(e,this._languageId,i.version,i.getValue())}return null},e}();t.JSONWorker=s,t.create=n});