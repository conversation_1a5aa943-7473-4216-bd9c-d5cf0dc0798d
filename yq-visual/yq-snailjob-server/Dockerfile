#FROM ibm-semeru-runtimes:open-21-jre
FROM bellsoft/liberica-openjdk-debian:21.0.3-cds

MAINTAINER Lion Li

RUN mkdir -p /yq/snailjob/logs

WORKDIR /yq/easyretry

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Xms512m -Xmx1024m"

EXPOSE 8800
EXPOSE 1788

ADD ./target/yq-snailjob-server.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           -XX:+HeapDumpOnOutOfMemoryError -XX:+UseZGC ${JAVA_OPTS} \
           -jar app.jar
