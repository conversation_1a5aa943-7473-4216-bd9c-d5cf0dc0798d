#FROM ibm-semeru-runtimes:open-21-jre
FROM bellsoft/liberica-openjdk-debian:21.0.3-cds

MAINTAINER Lion Li

RUN mkdir -p /yq/sentinel-dashboard/logs \
    /yq/skywalking/agent

WORKDIR /yq/sentinel-dashboard

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS=""

EXPOSE 8718

ADD ./target/yq-sentinel-dashboard.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           #-Dskywalking.agent.service_name=yq-sentinel-dashboard \
           #-javaagent:/yq/skywalking/agent/skywalking-agent.jar \
           ${JAVA_OPTS} -jar app.jar
