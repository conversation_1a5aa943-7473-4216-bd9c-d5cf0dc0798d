<div>
	<span class="brand" style="font-weight:bold;">{{gatewayFlowRuleDialog.title}}</span>
	<div class="card" style="margin-top: 20px;margin-bottom: 10px;">
		<div class="panel-body">
			<div class="row">
				<form role="form" class="form-horizontal">
					<div class="form-group">
						<label class="col-sm-2 control-label">API 类型</label>
						<div class="col-sm-9">
							<div class="form-control highlight-border" align="center">
								<input type="radio" ng-if="gatewayFlowRuleDialog.type == 'edit'" value="0" checked ng-model='currentRule.resourceMode'
											 disabled="" title="Route ID" /><span ng-if="gatewayFlowRuleDialog.type == 'edit'">&nbsp;Route ID&nbsp;&nbsp;</span>
								<input type="radio" ng-if="gatewayFlowRuleDialog.type == 'add'" value="0" checked ng-model='currentRule.resourceMode'
											 title="Route ID" ng-click="useRouteID()"/><span ng-if="gatewayFlowRuleDialog.type == 'add'">&nbsp;Route ID&nbsp;&nbsp;</span>
								<input type="radio" ng-if="gatewayFlowRuleDialog.type == 'edit'" value="1" ng-model='currentRule.resourceMode'
											 disabled="" title="API分组" /><span ng-if="gatewayFlowRuleDialog.type == 'edit'">&nbsp;API 分组&nbsp&nbsp;</span>
								<input type="radio" ng-if="gatewayFlowRuleDialog.type == 'add'" value="1" ng-model='currentRule.resourceMode'
											 title="API分组" ng-click="useCustormAPI()"/><span ng-if="gatewayFlowRuleDialog.type == 'add'">&nbsp;API 分组&nbsp&nbsp;</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-2 control-label">API 名称</label>
						<div class="col-sm-9">
							<input type="text" ng-if="currentRule.resourceMode == 0 && gatewayFlowRuleDialog.type == 'edit'" class="form-control" placeholder="请输入 Route ID" ng-model='currentRule.resource'
							 disabled="" />
							<input type="text" ng-if="currentRule.resourceMode == 0 && gatewayFlowRuleDialog.type == 'add'" class="form-control highlight-border" placeholder="请输入 Route ID" ng-model='currentRule.resource' />

							<select ng-if="currentRule.resourceMode == 1 && gatewayFlowRuleDialog.type == 'edit'" ng-model="currentRule.resource" ng-init="selectedName = currentRule.resource"
										disabled=""	ng-options="name for name in apiNames" class="form-control">
							</select>
							<select ng-if="currentRule.resourceMode == 1 && gatewayFlowRuleDialog.type == 'add'" ng-model="currentRule.resource" ng-init="currentRule.resource"
											ng-options="name for name in apiNames" class="form-control">
							</select>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-2 control-label">针对请求属性</label>
						<div class="col-sm-2">
							<label class="checkbox-inline">
								<input type="checkbox" ng-if="currentRule.paramItem != null" checked ng-click="notUseParamItem()" />
								<input type="checkbox" ng-if="currentRule.paramItem == null" ng-click="useParamItem()" />
							</label>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.paramItem != null">
						<label class="col-sm-2 control-label">参数属性</label>
						<div class="col-sm-9">
							<div class="form-control highlight-border" align="center">
								<input type="radio" name="parseStrategy" value="0" checked ng-model='currentRule.paramItem.parseStrategy' title="Client IP" />&nbsp;Client IP&nbsp;&nbsp;
								<input type="radio" name="parseStrategy" value="1" ng-model='currentRule.paramItem.parseStrategy' title="Remote Host" />&nbsp;Remote Host&nbsp;&nbsp;
								<input type="radio" name="parseStrategy" value="2" ng-model='currentRule.paramItem.parseStrategy' title="Header" />&nbsp;Header&nbsp;&nbsp;
								<input type="radio" name="parseStrategy" value="3" ng-model='currentRule.paramItem.parseStrategy' title="URL 参数" />&nbsp;URL 参数&nbsp;&nbsp;
								<input type="radio" name="parseStrategy" value="4" ng-model='currentRule.paramItem.parseStrategy' title="Cookie" />&nbsp;Cookie&nbsp;&nbsp;
							</div>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.paramItem != null && (currentRule.paramItem.parseStrategy == 2 || currentRule.paramItem.parseStrategy == 3 || currentRule.paramItem.parseStrategy == 4)">
						<label class="col-sm-2 control-label">
							<span ng-if="currentRule.paramItem.parseStrategy==2">Header 名称</span>
							<span ng-if="currentRule.paramItem.parseStrategy==3">URL 参数名称</span>
							<span ng-if="currentRule.paramItem.parseStrategy==4">Cookie 名称</span>
						</label>
						<div class="col-sm-9">
							<input type="text" name="fieldName" class="form-control highlight-border" placeholder="请输入" ng-model='currentRule.paramItem.fieldName' />
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.paramItem != null">
						<label class="col-sm-2 control-label">属性值匹配</label>
						<div class="col-sm-2">
							<label class="checkbox-inline">
								<input type="checkbox" ng-if="currentRule.paramItem.pattern != null" checked ng-click="notUseParamItemVal()"/>
								<input type="checkbox" ng-if="currentRule.paramItem.pattern == null" ng-click="useParamItemVal()"/>
							</label>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.paramItem.pattern != null">
						<label class="col-sm-2 control-label">匹配模式</label>
						<div class="col-sm-4 control-label">
							<div class="form-control highlight-border" align="center">
								<input type="radio" value="0" checked ng-model="currentRule.paramItem.matchStrategy" title="精确" />&nbsp;精确&nbsp;&nbsp;
								<input type="radio" value="3" ng-model="currentRule.paramItem.matchStrategy" title="子串" />&nbsp;子串&nbsp;&nbsp;
								<input type="radio" value="2" ng-model="currentRule.paramItem.matchStrategy" title="正则" />&nbsp;正则&nbsp;&nbsp;
							</div>
						</div>
						<label class="col-sm-2 control-label">匹配串</label>
						<div class="col-sm-3 control-label">
							<input type='text' ng-model="currentRule.paramItem.pattern" class="form-control highlight-border" placeholder="匹配串" />
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-2 control-label">阈值类型</label>
						<div class="col-sm-9">
							<div class="form-control highlight-border" align="center">
								<input type="radio" name="grade" value="1" checked ng-model="currentRule.grade" title="QPS" />&nbsp;QPS&nbsp;&nbsp;
								<input type="radio" name="grade" value="0" ng-model="currentRule.grade" title="线程数" />&nbsp;线程数&nbsp;&nbsp;
							</div>
						</div>
					</div>

					<div class="form-group">
						<div>
							<label class="col-sm-2 control-label">
								<span ng-if="currentRule.grade==1">QPS 阈值</span>
								<span ng-if="currentRule.grade==0">线程数</span>
							</label>
							<div class="col-sm-3">
								<input type='number' min="0" class="form-control highlight-border" ng-model='currentRule.count' placeholder="阈值" />
							</div>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.grade==1">
						<div>
							<label class="col-sm-2 control-label">间隔</label>
							<div class="col-sm-3">
								<input type='number' id="txtInterval" min="1" class="form-control highlight-border" ng-model='currentRule.interval' placeholder="统计窗口时间长度" />
							</div>
							<div class="col-sm-2">
								<select ng-model="currentRule.intervalUnit" ng-init="currentRule.intervalUnit"
												ng-options="intervalUnit.val as intervalUnit.desc for intervalUnit in intervalUnits" class="form-control" ng-click="changeIntervalUnit()">
								</select>
							</div>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.grade==1">
						<label class="col-sm-2 control-label">流控方式</label>
						<div class="col-sm-9">
							<div class="form-control highlight-border" align="center">
								<input type="radio" name="controlBehavior" value="0" checked ng-model='currentRule.controlBehavior' title="快速失败" />&nbsp;快速失败&nbsp;&nbsp;
								<input type="radio" name="controlBehavior" value="2" ng-model='currentRule.controlBehavior' title="匀速排队" />&nbsp;匀速排队&nbsp&nbsp;
							</div>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.grade==1 && currentRule.controlBehavior==0">
						<div>
							<label class="col-sm-2 control-label">Burst size</label>
							<div class="col-sm-3">
								<input type='number' min="0" class="form-control highlight-border" ng-model='currentRule.burst' placeholder="突发请求额外允许数" />
							</div>
						</div>
					</div>

					<div class="form-group" ng-if="currentRule.grade==1 && currentRule.controlBehavior==2">
						<div>
							<label class="col-sm-2 control-label">超时时间</label>
							<div class="col-sm-3">
								<input type='number' min="0" class="form-control highlight-border" ng-model='currentRule.maxQueueingTimeoutMs' placeholder="排队等待时间(ms)" />
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="separator"></div>
			<div clss="row" style="margin-top: 20px;">
				<button class="btn btn-outline-danger" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="closeThisDialog()">取消</button>
				<button class="btn btn-outline-success" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="saveRule()">{{gatewayFlowRuleDialog.confirmBtnText}}</button>
				<button ng-if="gatewayFlowRuleDialog.saveAndContinueBtnText" class="btn btn-default" style="float:right; height: 30px;font-size: 12px;"
				 ng-click="saveRuleAndContinue()">{{gatewayFlowRuleDialog.saveAndContinueBtnText}}</button>
			</div>
		</div>
	</div>
</div>
