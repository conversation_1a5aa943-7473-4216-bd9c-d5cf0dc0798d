<div class="navbar-default sidebar" role="navigation" style="overflow-y: auto;">
  <div class="sidebar-nav navbar-collapse">
    <ul class="nav in" id="side-menu">
      <li class="sidebar-search">
        <div class="input-group" style="">
          <input type="text" class="form-control highlight-border" placeholder="应用名" ng-model="searchApp">
          <span class="input-group-btn">
            <button class="btn btn-secondary btn-default-inverse" type="button">搜索</button>
          </span>
        </div>
      </li>
      <li ui-sref-active="active">
        <a ui-sref="dashboard.home" style="font-size:16px;">
          <span class="glyphicon glyphicon-dashboard"></span>
          &nbsp;&nbsp;首页</a>
      </li>

      <li ng-class="{active: true}" ng-repeat="entry in apps | filter: { app: searchApp }">{{dropDown}}
        <a href="javascript:void(0);" ng-click="click($event)" collapse="{{collpaseall == 1}}" style="font-size: 16px;word-break: break-word;">
          &nbsp;{{entry.app}}
          <span class="fa arrow"></span>
          <span class="arrow">({{entry.healthyCount}}/{{entry.machines.length}})</span>
        </a>

        <!--<ul class="nav nav-second-level" collapse="{{entry.active}}" style="display: none;">-->
        <ul class="nav nav-second-level" ng-show="entry.active">
          <li ui-sref-active="active">
            <a ui-sref="dashboard.metric({app: entry.app})">
              <i class="fa fa-bar-chart"></i>&nbsp;&nbsp;实时监控</a>
          </li>

          <li ui-sref-active="active" ng-if="!entry.isGateway">
            <a ui-sref="dashboard.identity({app: entry.app})">
              <i class="glyphicon glyphicon-list-alt"></i>&nbsp;&nbsp;簇点链路</a>
          </li>

          <li ui-sref-active="active" ng-if="entry.isGateway">
            <a ui-sref="dashboard.gatewayIdentity({app: entry.app})">
              <i class="glyphicon glyphicon-filter"></i>&nbsp;&nbsp;请求链路</a>
          </li>

          <!--<li ui-sref-active="active" ng-if="entry.appType==0">-->
            <!--<a ui-sref="dashboard.flow({app: entry.app})">-->
              <!--<i class="glyphicon glyphicon-filter"></i>&nbsp;&nbsp;流控规则 V1</a>-->
          <!--</li>-->

          <li ui-sref-active="active" ng-if="entry.isGateway">
            <a ui-sref="dashboard.gatewayApi({app: entry.app})">
              <i class="glyphicon glyphicon-tags"></i>&nbsp;&nbsp;&nbsp;API 管理</a>
          </li>
          <li ui-sref-active="active" ng-if="entry.isGateway">
            <a ui-sref="dashboard.gatewayFlow({app: entry.app})">
              <i class="glyphicon glyphicon-filter"></i>&nbsp;&nbsp;流控规则</a>
          </li>

          <li ui-sref-active="active" ng-if="!entry.isGateway">
            <a ui-sref="dashboard.flowV1({app: entry.app})">
              <i class="glyphicon glyphicon-filter"></i>&nbsp;&nbsp;流控规则</a>
          </li>

          <li ui-sref-active="active">
            <a ui-sref="dashboard.degrade({app: entry.app})">
              <i class="glyphicon glyphicon-flash"></i>&nbsp;&nbsp;熔断规则</a>
          </li>
          <li ui-sref-active="active" ng-if="!entry.isGateway">
            <a ui-sref="dashboard.paramFlow({app: entry.app})">
              <i class="glyphicon glyphicon-fire"></i>&nbsp;&nbsp;热点规则</a>
          </li>
          <li ui-sref-active="active">
            <a ui-sref="dashboard.system({app: entry.app})">
              <i class="glyphicon glyphicon-lock"></i>&nbsp;&nbsp;系统规则</a>
          </li>
          <li ui-sref-active="active" ng-if="!entry.isGateway">
            <a ui-sref="dashboard.authority({app: entry.app})">
              <i class="glyphicon glyphicon-check"></i>&nbsp;&nbsp;授权规则</a>
          </li>
          <li ui-sref-active="active" ng-if="!entry.isGateway">
            <a ui-sref="dashboard.clusterAppServerList({app: entry.app})">
              <i class="glyphicon glyphicon-cloud"></i>&nbsp;&nbsp;集群流控</a>
          </li>

          <li ui-sref-active="active">
            <a ui-sref="dashboard.machine({app: entry.app})">
              <i class="glyphicon glyphicon-th-list"></i>&nbsp;&nbsp;机器列表</a>
          </li>
        </ul>
        <!-- /.nav-second-level -->
      </li>
    </ul>
  </div>
</div>