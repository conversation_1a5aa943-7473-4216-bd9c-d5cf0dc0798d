# Tomcat
server:
  port: 8718

# Spring
spring:
  application:
    # 应用名称
    name: yq-sentinel-dashboard
  profiles:
    # 环境配置
    active: @profiles.active@

sentinel:
  dashboard:
    version: @sentinel.version@

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ${NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      discovery:
        # 注册组
        group: ${NACOS_DISCOVERY_GROUP}
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: ${NACOS_CONFIG_GROUP}
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
