package yq.mall.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import yq.mall.config.properties.WxMaProperties;

import javax.annotation.Resource;

/**
 * 微信小程序配置
 * <AUTHOR>
 */
@Configuration
public class WxMaConfig {

    @Resource
    private WxMaProperties wxMaProperties;

    @Bean
    public WxMaService wxMpService() {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        // 设置微信公众号的appid
        config.setAppid(wxMaProperties.getAppId());
        // 设置微信公众号的app corpSecret
        config.setSecret(wxMaProperties.getSecret());
        // 设置微信公众号的token
        config.setToken(wxMaProperties.getToken());
        // 设置消息加解密密钥
        config.setAesKey(wxMaProperties.getAesKey());

        WxMaService wxMpService = new WxMaServiceImpl();
        wxMpService.setWxMaConfig(config);
        return wxMpService;
    }

}
