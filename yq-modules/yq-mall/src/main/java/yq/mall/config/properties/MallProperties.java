package yq.mall.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 商城配置属性
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "yq.mall")
public class MallProperties {

    /**
     * 是否允许下单 true:允许 false:不允许
     */
    private Boolean enable = true;

    /**
     * 不许下单时，可下单的用户ID
     */
    private List<Long> excludeUserIds = new ArrayList<>();

    private List<Integer> retryInterval = new ArrayList<>();

    //根据重试次数获取间隔
    public int getRetryIntervalByIndex(int retryCount) {
        if (retryCount < retryInterval.size()) {
            return retryInterval.get(retryCount);
        }
        // 如果重试次数超过最大次数，返回一个默认值
        return 0;
    }

}
