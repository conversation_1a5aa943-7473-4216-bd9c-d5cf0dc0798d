package yq.mall.config.properties;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 微信支付配置
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayV3Properties {

    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户V3密钥
     */
    private String apiV3Key;
    /**
     * 证书序列号
     */
    private String certSerialNo;

    /**
     * apiclient_key.pem证书文件的绝对路径或者以classpath:开头的类路径
     */
    private String privateKeyPath;
    /**
     * apiclient_cert.pem证书文件的绝对路径或者以classpath:开头的类路径
     */
    private String privateCertPath;

    /**
     * 费率
     */
    private List<Rate> rates;

    @Data
    public static class Rate {
        /**
         * 微信费率
         */
        private BigDecimal rate;

        /**
         * 费率生效日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date startDate;

        /**
         * 费率失效日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date endDate;
    }

    /**
     * 通知地址
     */
    private NotifyUrl notifyUrl;


    @Data
    public static class NotifyUrl {
        /**
         * 订单支付成功通知地址
         */
        private String order;
        /**
         * 退款成功通知地址
         */
        private String refund;
    }


    public BigDecimal getApplicableRate() {
        if (rates.size() == 1) {
            return rates.getFirst().getRate();
        } else if (rates.size() > 1) {
            Date currentDate = new Date();
            for (Rate rate : rates) {
                if (DateUtil.isIn(currentDate, rate.getStartDate(), rate.getEndDate())) {
                    return rate.getRate();
                }
            }
        }
        return BigDecimal.ZERO; // 默认返回0
    }


}
