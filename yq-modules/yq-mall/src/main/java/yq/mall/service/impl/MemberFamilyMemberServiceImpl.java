package yq.mall.service.impl;

import cn.dev33.satoken.temp.SaTempUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.common.redis.utils.CacheUtils;
import yq.mall.constant.MemberCacheConstant;
import yq.mall.domain.bo.FamilyShareBo;
import yq.mall.domain.bo.MemberFamilyBo;
import yq.mall.domain.bo.MemberFamilyMemberBo;
import yq.mall.domain.vo.MemberFamilyMemberVo;
import yq.mall.domain.MemberFamilyMember;
import yq.mall.domain.vo.MemberFamilyVo;
import yq.mall.mapper.MemberFamilyMemberMapper;
import yq.mall.service.IMemberFamilyMemberService;
import yq.mall.service.IMemberFamilyService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 家庭成员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RequiredArgsConstructor
@Service
public class MemberFamilyMemberServiceImpl implements IMemberFamilyMemberService {

    @Resource
    private final MemberFamilyMemberMapper baseMapper;

    @Lazy
    @Resource
    private IMemberFamilyMemberService self;

    private final IMemberFamilyService familyService;

    /**
     * 查询家庭成员
     */
    @Override
    public MemberFamilyMemberVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询家庭成员列表
     */
    @Override
    public TableDataInfo<MemberFamilyMemberVo> queryPageList(MemberFamilyMemberBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberFamilyMember> lqw = buildQueryWrapper(bo);
        Page<MemberFamilyMemberVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询家庭成员列表
     */
    @Override
    public List<MemberFamilyMemberVo> queryList(MemberFamilyMemberBo bo) {
        LambdaQueryWrapper<MemberFamilyMember> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MemberFamilyMember> buildQueryWrapper(MemberFamilyMemberBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberFamilyMember> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, MemberFamilyMember::getUserId, bo.getUserId());
        lqw.eq(bo.getFamilyId() != null, MemberFamilyMember::getFamilyId, bo.getFamilyId());
        lqw.eq(StringUtils.isNotBlank(bo.getMemo()), MemberFamilyMember::getMemo, bo.getMemo());
        return lqw;
    }

    /**
     * 新增家庭成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MemberFamilyMemberBo bo) {
        MemberFamilyMember add = MapstructUtils.convert(bo, MemberFamilyMember.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改家庭成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MemberFamilyMemberBo bo) {
        MemberFamilyMember update = MapstructUtils.convert(bo, MemberFamilyMember.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberFamilyMember entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除家庭成员
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 返回分享的临时token, 用于邀请家庭成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getShareToken(Long userId) {
        Long familyId = createFamily(userId);
        // 邀请码24小时内有效，单位秒
        return SaTempUtil.createToken(familyId, 60 * 60 * 24);
    }

    /**
     * 判断是否需要创建新家庭
     */
    private Long createFamily(Long userId) {

        // 查询是否已存在家庭
        LambdaQueryWrapper<MemberFamilyMember> lqw = Wrappers.lambdaQuery();
        lqw.eq(MemberFamilyMember::getUserId, userId);
        lqw.last("limit 1");
        MemberFamilyMemberVo familyMemberVo = baseMapper.selectVoOne(lqw);

        if (familyMemberVo == null) {
            // 新建家庭
            MemberFamilyBo familyBo = new MemberFamilyBo();
            familyBo.setOwnerId(userId);
            familyService.insertByBo(familyBo);

            //将自己也加入家庭
            MemberFamilyMember member = new MemberFamilyMember();
            member.setUserId(userId);
            member.setFamilyId(familyBo.getId());
            member.setOwnerFlag("1");
            baseMapper.insert(member);

            // 更新家庭成员缓存
            CacheUtils.evict(MemberCacheConstant.FAMILY_MEMBER, familyBo.getId());
            // 更新用户家庭缓存
            CacheUtils.evict(MemberCacheConstant.USER_FAMILY_ID, userId);

            return familyBo.getId();
        } else {
            return familyMemberVo.getFamilyId();
        }
    }

    /**
     * 解析生成的token加入对应的家庭
     *
     * @param bo 被邀请人携带的信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void parseShareToken(FamilyShareBo bo) {
        //判断token是否有效
        checkTokenValid(bo.getShareToken(), bo.getUserId());

        Long familyId = SaTempUtil.parseToken(bo.getShareToken(), Long.class);

        // 将被邀请人加入家庭
        MemberFamilyMember member = new MemberFamilyMember();
        member.setUserId(bo.getUserId());
        member.setFamilyId(familyId);
        member.setMemo(bo.getMemo());
        baseMapper.insert(member);

        // 更新家庭成员缓存
        CacheUtils.evict(MemberCacheConstant.FAMILY_MEMBER, familyId);
        // 更新用户家庭缓存
        CacheUtils.evict(MemberCacheConstant.USER_FAMILY_ID, bo.getUserId());
    }

    /**
     * 校验token邀请是否有效
     */
    @Override
    public Boolean checkTokenValid(String token, Long userId) {
        Long familyId = SaTempUtil.parseToken(token, Long.class);
        ValidateUtils.isFalse((familyId == null || familyId == 0), "无效的邀请码");

        //家庭解散的校验
        MemberFamilyVo familyVo = familyService.queryById(familyId);
        ValidateUtils.notNull(familyVo, "信息已过期，无效的邀请码");

        // 根据userId查询对应的familyId,看被邀请人是否已经加入过了家庭
        MemberFamilyMember familyMember = baseMapper.selectOne(Wrappers.<MemberFamilyMember>lambdaQuery().
            eq(MemberFamilyMember::getUserId, userId));
        ValidateUtils.isNull(familyMember, "当前已有家庭,无法加入新家庭");

        return true;
    }

    /**
     * 退出当前家庭
     *
     * @param userId     退出家庭的用户id
     * @param operatorId 操作人id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void quitFamily(Long userId, Long operatorId) {
        // 获取家庭成员列表
        List<MemberFamilyMemberVo> familyMemberVoList = self.listFamilyMemberByUserId(userId);

        // 根据userId找到对应家庭成员中的主键id
        Long id = familyMemberVoList.stream().filter(familyMemberVo -> familyMemberVo.getUserId().equals(userId))
            .map(MemberFamilyMemberVo::getId).findFirst().orElse(null);
        ValidateUtils.notNull(id, "当前用户没有加入家庭");

        // 获取群主
        MemberFamilyMemberVo owner = familyMemberVoList.stream()
            .filter(member -> StringUtils.equals("1", member.getOwnerFlag()))
            .findFirst().orElse(null);

        // 获取familyId
        Long familyId = familyMemberVoList.getFirst().getFamilyId();

        // 本人或群主操作
        if (operatorId.equals(userId)
            || (owner != null && operatorId.equals(owner.getUserId()))
        ) {
            // 移除家庭成员
            baseMapper.deleteById(id);

            // 更新家庭成员缓存
            CacheUtils.evict(MemberCacheConstant.FAMILY_MEMBER, familyId);

            // 更新用户家庭缓存
            CacheUtils.evict(MemberCacheConstant.USER_FAMILY_ID, userId);
        }

    }

    /**
     * 群主解散家庭
     *
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dissolveFamily(Long userId) {
        // 查询创建人对应的家庭
        MemberFamilyVo memberFamilyVo = familyService.queryByOwnerId(userId);
        ValidateUtils.notNull(memberFamilyVo, "没有权限解散家庭");

        // 解散家庭
        familyService.dissolveFamily(memberFamilyVo.getId());
        // 解散家庭成员
        baseMapper.delete(Wrappers.<MemberFamilyMember>lambdaQuery()
            .eq(MemberFamilyMember::getFamilyId, memberFamilyVo.getId()));

        // 更新家庭成员缓存
        CacheUtils.evict(MemberCacheConstant.FAMILY_MEMBER, memberFamilyVo.getId());

        // 获取家庭成员列表
        List<MemberFamilyMemberVo> familyMemberVoList = self.listFamilyMember(memberFamilyVo.getId());
        // 更新用户家庭缓存
        familyMemberVoList.forEach(familyMemberVo -> {
            CacheUtils.evict(MemberCacheConstant.USER_FAMILY_ID, familyMemberVo.getUserId());
        });
    }

    /**
     * 根据id修改家庭成员备注
     *
     * @param userId     用户id
     * @param operatorId 操作人id
     * @param memo       备注
     */
    @Override
    public void updateMemoById(Long userId, Long operatorId, String memo) {
        // 获取家庭成员列表
        List<MemberFamilyMemberVo> familyMemberVoList = self.listFamilyMemberByUserId(userId);

        // 根据userId找到对应家庭成员中的主键id
        Long id = familyMemberVoList.stream().filter(familyMemberVo -> familyMemberVo.getUserId().equals(userId))
            .map(MemberFamilyMemberVo::getId).findFirst().orElse(null);
        ValidateUtils.notNull(id, "当前用户没有加入家庭");

        // 获取群主
        MemberFamilyMemberVo owner = familyMemberVoList.stream()
            .filter(member -> StringUtils.equals("1", member.getOwnerFlag()))
            .findFirst().orElse(null);

        // 获取familyId
        Long familyId = familyMemberVoList.getFirst().getFamilyId();

        if (operatorId.equals(userId)
            || (owner != null && operatorId.equals(owner.getUserId()))
        ) {
            LambdaUpdateWrapper<MemberFamilyMember> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(MemberFamilyMember::getId, id);
            updateWrapper.set(MemberFamilyMember::getMemo, memo);
            baseMapper.update(null, updateWrapper);

            // 更新家庭成员缓存
            CacheUtils.evict(MemberCacheConstant.FAMILY_MEMBER, familyId);
        }

    }


    /**
     * 查询家庭成员列表
     */
    @Override
    @Cacheable(value = MemberCacheConstant.FAMILY_MEMBER, key = "#familyId")
    public List<MemberFamilyMemberVo> listFamilyMember(Long familyId) {
        return baseMapper.selectVoList(Wrappers.<MemberFamilyMember>lambdaQuery()
            .eq(MemberFamilyMember::getFamilyId, familyId));
    }

    /**
     * 通过userId查询家庭成员列表
     */
    @Override
    public List<MemberFamilyMemberVo> listFamilyMemberByUserId(Long userId) {
        Long userFamilyId = self.getFamilyIdByUserId(userId);
        if (userFamilyId == null) {
            return null;
        }
        return self.listFamilyMember(userFamilyId);
    }

    /**
     * 根据用户id获取家庭id
     *
     * @return
     */
    @Cacheable(value = MemberCacheConstant.USER_FAMILY_ID, key = "#userId")
    @Override
    public Long getFamilyIdByUserId(Long userId) {
        MemberFamilyMember member = baseMapper.selectOne(Wrappers.<MemberFamilyMember>lambdaQuery()
            .select(MemberFamilyMember::getFamilyId)
            .eq(MemberFamilyMember::getUserId, userId)
            .last("limit 1"));
        return member == null ? null : member.getFamilyId();
    }


}
