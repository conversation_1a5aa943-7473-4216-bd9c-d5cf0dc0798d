package yq.mall.service;

import yq.mall.domain.MemberFamily;
import yq.mall.domain.vo.MemberFamilyVo;
import yq.mall.domain.bo.MemberFamilyBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 家庭Service接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IMemberFamilyService {

    /**
     * 查询家庭
     */
    MemberFamilyVo queryById(Long id);

    /**
     * 查询家庭列表
     */
    TableDataInfo<MemberFamilyVo> queryPageList(MemberFamilyBo bo, PageQuery pageQuery);

    /**
     * 查询家庭列表
     */
    List<MemberFamilyVo> queryList(MemberFamilyBo bo);

    /**
     * 新增家庭
     */
    Boolean insertByBo(MemberFamilyBo bo);

    /**
     * 修改家庭
     */
    Boolean updateByBo(MemberFamilyBo bo);

    /**
     * 校验并批量删除家庭信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据所属人id查询家庭
     */
    MemberFamilyVo queryByOwnerId(Long ownerId);

    /**
     * 解散家庭
     */
    Boolean dissolveFamily(Long id);
}
