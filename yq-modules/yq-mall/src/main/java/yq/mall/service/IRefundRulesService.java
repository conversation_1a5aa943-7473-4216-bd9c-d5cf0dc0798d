package yq.mall.service;


import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.domain.bo.RefundRulesBo;
import yq.mall.domain.vo.RefundRulesVo;

import java.util.Collection;
import java.util.List;

/**
 * 退款规则Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IRefundRulesService {

    /**
     * 查询退款规则
     */
    RefundRulesVo queryById(Long id);

    /**
     * 查询退款规则列表
     */
    TableDataInfo<RefundRulesVo> queryPageList(RefundRulesBo bo, PageQuery pageQuery);

    /**
     * 查询退款规则列表
     */
    List<RefundRulesVo> queryList(RefundRulesBo bo);

    /**
     * 新增退款规则
     */
    Boolean insertByBo(RefundRulesBo bo);

    /**
     * 修改退款规则
     */
    Boolean updateByBo(RefundRulesBo bo);

    /**
     * 校验并批量删除退款规则信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取退款规则列表
     * @return
     */
    List<RefundRulesVo> listRefundRule();
}
