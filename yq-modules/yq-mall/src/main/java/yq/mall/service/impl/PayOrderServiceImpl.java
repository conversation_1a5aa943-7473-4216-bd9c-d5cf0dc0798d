package yq.mall.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.request.WxPayOrderCloseV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.exception.AlertException;
import yq.common.core.exception.ServiceException;
import yq.common.core.utils.DateUtils;
import yq.common.core.utils.SpringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.config.properties.PayProfitSharingProperties;
import yq.mall.config.properties.WxPayV3Properties;
import yq.mall.constant.MallLockConstant;
import yq.mall.controller.pay.bo.PayOrderNotifyBo;
import yq.mall.domain.Order;
import yq.mall.domain.PayOrder;
import yq.mall.domain.Refund;
import yq.mall.domain.bo.pay.PayOrderBo;
import yq.mall.domain.vo.pay.PayOrderVo;
import yq.mall.enums.order.OrderTypeEnum;
import yq.mall.enums.pay.PayChannelEnum;
import yq.mall.enums.pay.PayOrderProfitSharingStatusEnum;
import yq.mall.enums.pay.PayOrderStatusEnum;
import yq.mall.mapper.PayOrderMapper;
import yq.mall.service.IMemberUserService;
import yq.mall.service.IOrderService;
import yq.mall.service.IPayOrderService;
import yq.mall.service.IPayProfitSharingService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 支付单服务实现类
 *
 * <AUTHOR> Suen
 */
@Slf4j
@Service
public class PayOrderServiceImpl implements IPayOrderService {

    @Resource
    private PayProfitSharingProperties payProfitSharingProperties;

    @Resource
    private PayOrderMapper baseMapper;

    @Resource
    private IMemberUserService memberUserService;

    @Resource
    private WxPayV3Properties wxPayProperties;

    @Resource
    private WxPayService wxPayService;

    @Lazy
    @Resource
    private IPayProfitSharingService payProfitSharingService;

    @Lazy
    @Resource
    private IOrderService orderService;


    /**
     * 自依赖，解决AOP问题
     */
    private PayOrderServiceImpl getSelf() {
        return SpringUtils.getBean(PayOrderServiceImpl.class);
    }

    /**
     * 根据ID获取支付单
     *
     * @return 支付单
     */
    @Override
    public PayOrder getById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 创建支付单
     *
     * @param order 订单信息
     * @return 支付单ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createOrder(Order order) {
        // 检查交易订单是否已存在，已存在则直接返回
        var payOrder = PayOrder.builder()
            // 订单类型 + id
            .subject(OrderTypeEnum.TICKET.getName() + "-" + order.getId())
            .merchantOrderId(order.getId())
            .totalAmount(order.getPayPrice())
            .status(PayOrderStatusEnum.WAITING.getType())
            .userIp(order.getUserIp())
            .channelCode(order.getPayChannelCode())
            .createTime(new Date())
            .refundAmount(BigDecimal.ZERO)
            .profitSharing(false)
            .build();

        // 微信小程序支付
        if (PayChannelEnum.WX_LITE.getCode().equals(order.getPayChannelCode())) {
            var openId = memberUserService.getOpenIdByUserId(order.getUserId());
            payOrder.setChannelUserId(openId);
            payOrder.setNotifyUrl(wxPayProperties.getNotifyUrl().getOrder());

            // 填充费率
            BigDecimal rate = wxPayProperties.getApplicableRate();
            payOrder.setChannelFeeRate(rate);
            // 结果小数位保留2位，四舍五入
            BigDecimal fee = payOrder.getTotalAmount().multiply(rate).setScale(2, RoundingMode.HALF_UP);
            payOrder.setChannelFeeAmount(fee);
        }

        // 只做记录，分账由定时器去执行
        if (payProfitSharingProperties.getEnable()) {
            payOrder.setProfitSharing(true);
            payOrder.setProfitSharingStatus(PayOrderProfitSharingStatusEnum.WAITING.getType());
        }

        // 创建支付订单
        baseMapper.insert(payOrder);
        order.setPayOrderId(payOrder.getId());

        return payOrder.getId();
    }

    /**
     * 支付成功通知
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#notify.outTradeNo"}, expire = 10000, acquireTimeout = 200)
    public void updateOrderSuccess(PayOrderNotifyBo notify) {
        var payOrder = getSelf().getById(notify.getOutTradeNo());

        ValidateUtils.notNull(payOrder, "支付单不存在");
        ValidateUtils.equals(PayOrderStatusEnum.WAITING.getType(), payOrder.getStatus(), "支付单不是待支付状态");

        var zdt = ZonedDateTime.parse(notify.getSuccessTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZZZZZ"));
        // 1. 更新支付单状态
        baseMapper.update(
            Wrappers.lambdaUpdate(PayOrder.class)
                .set(PayOrder::getStatus, PayOrderStatusEnum.SUCCESS.getType())
                .set(PayOrder::getChannelOrderNo, notify.getChannelOrderNo())
                .set(PayOrder::getSuccessTime, Date.from(zdt.toInstant()))
                .eq(PayOrder::getId, payOrder.getId())
        );

        // 2. 更新订单状态
        orderService.updateOrderPaid(payOrder.getMerchantOrderId(), payOrder);

    }

    /**
     * 取消支付订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#id"}, expire = 10000, acquireTimeout = 200)
    public void updateOrderCancel(Long id) {
        // 查询订单实际是否已经支付，如果已支付，则不允许取消
        var payOrder = getSelf().getById(id);
        ValidateUtils.notNull(payOrder, "支付单不存在");

        // 只有未支付的订单才能取消
        ValidateUtils.equals(PayOrderStatusEnum.WAITING.getType(), payOrder.getStatus(), "当前订单状态不允许取消");

        // 请求支付关单
        this.requestChannelClose(payOrder);

        baseMapper.update(
            Wrappers.lambdaUpdate(PayOrder.class)
                .set(PayOrder::getStatus, PayOrderStatusEnum.CLOSED.getType())
                .eq(PayOrder::getId, id)
        );
    }

    /**
     * 请求支付关单
     */
    private void requestChannelClose(PayOrder payOrder) {
        if (PayChannelEnum.WX_LITE.getCode().equals(payOrder.getChannelCode())) {
            try {
                // 先检查订单是否已经支付，已经支付的，则不调用关单接口。
                var res = wxPayService.queryOrderV3(new WxPayOrderQueryV3Request()
                    .setOutTradeNo(String.valueOf(payOrder.getId())));
                if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(res.getTradeState())) {

                    getSelf().updateOrderSuccess(PayOrderNotifyBo.builder()
                        .outTradeNo(payOrder.getId())
                        .successTime(res.getSuccessTime())
                        .channelUserId(res.getPayer().getOpenid())
                        .channelOrderNo(res.getTransactionId())
                        .build());

                    // 先抛出业务异常，等待支付成功回调由payOder同步订单支付状态
                     throw new AlertException("支付状态变更，请稍后重试");
                }
                wxPayService.closeOrderV3(new WxPayOrderCloseV3Request()
                    .setOutTradeNo(String.valueOf(payOrder.getId())));
            } catch (WxPayException e) {
                log.warn("请求微信支付关单失败", e);
            }
        }
    }

    /**
     * 同步支付订单状态
     */
    @Override
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#payOrderId"}, expire = 10000, acquireTimeout = 200)
    public void syncPayStatus(Long payOrderId) {
        var request = new WxPayOrderQueryV3Request()
            .setOutTradeNo(String.valueOf(payOrderId));
        WxPayOrderQueryV3Result res;
        try {
            res = wxPayService.queryOrderV3(request);
        } catch (WxPayException e) {
            log.error("查询支付订单失败", e);
            throw new ServiceException("查询支付订单失败");
        }
        if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(res.getTradeState())) {
            var notify = PayOrderNotifyBo.builder()
            .successTime(res.getSuccessTime())
            .outTradeNo(Long.valueOf(res.getOutTradeNo()))
            .channelUserId(res.getPayer().getOpenid())
            .channelOrderNo(res.getTransactionId())
            .build();

            getSelf().updateOrderSuccess(notify);
        }
    }

    /**
     * 更新支付单退款状态
     *
     * @param id    支付单ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#id"}, expire = 10000, acquireTimeout = 200)
    public void updateOrderRefund(Long id, Refund refund) {
        var payOrder = getSelf().getById(id);
        ValidateUtils.notNull(payOrder, "支付单不存在");

        // 更新支付单退款金额
        BigDecimal refundAmount = payOrder.getRefundAmount().add(refund.getRefundAmount());

        var status = refundAmount.compareTo(payOrder.getTotalAmount()) >= 0
            ? PayOrderStatusEnum.REFUND.getType()
            : PayOrderStatusEnum.REFUND_PART.getType();

        // 更新支付单退款金额
        var count = baseMapper.update(
            Wrappers.lambdaUpdate(PayOrder.class)
                .set(PayOrder::getRefundAmount, refundAmount)
                // 只有退款金额大于等于支付金额时，才更新支付单状态为退款
                .set(PayOrder::getStatus, status)
                // 主键
                .eq(PayOrder::getId, id)
        );
        ValidateUtils.isTrue(count == 1, "更新支付单退款金额失败");
    }

    /**
     * 查询支付单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页信息
     * @return 支付单列表
     */
    @Override
    public TableDataInfo<PayOrderVo> queryPageList(PayOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getId() != null, PayOrder::getId, bo.getId());
        lqw.eq(bo.getMerchantOrderId() != null, PayOrder::getMerchantOrderId, bo.getMerchantOrderId());
        lqw.orderByDesc(PayOrder::getSuccessTime);
        Page<PayOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 更新支付单分账状态
     *
     * @param id     支付单ID
     * @param status 支付单状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#id"}, expire = 10000, acquireTimeout = 200)
    public void updateProfitSharingStatus(Long id, PayOrderProfitSharingStatusEnum status) {
        var updateCount = baseMapper.update(
            Wrappers.lambdaUpdate(PayOrder.class)
                .set(PayOrder::getProfitSharingStatus, status.getType())
                .eq(PayOrder::getId, id)
        );
        ValidateUtils.isTrue(updateCount == 1, "更新支付单分账状态失败");
    }

    /**
     * 查询待分账的支付单
     */
    @Override
    public List<PayOrder> listWaitingProfitSharing() {
        // 1. 支付单需要分账
        // 2. 支付单状态为
        //  成功
        //  退款（方便后续退回识别，退款的应该设置为忽略）
        //  部分退款（发起支付后，未分账前，发起部分退款）
        // 3. 分账状态为待分账
        var statutList = Arrays.asList(
            PayOrderStatusEnum.SUCCESS.getType(),
            PayOrderStatusEnum.REFUND.getType(),
            PayOrderStatusEnum.REFUND_PART.getType()
        );

        var now = new Date();

        return baseMapper.selectList(
            Wrappers.lambdaQuery(PayOrder.class)
                .select(
                    PayOrder::getId,
                    PayOrder::getChannelOrderNo,
                    PayOrder::getChannelCode,
                    PayOrder::getTotalAmount,
                    PayOrder::getRefundAmount,
                    PayOrder::getSubject,
                    PayOrder::getStatus
                )
                .eq(PayOrder::getProfitSharing, true)
                .eq(PayOrder::getProfitSharingStatus, PayOrderProfitSharingStatusEnum.WAITING.getType())
                // 不处理超过30天的单据，可以认为是异常单据，需要人工处理
                // 因为现场补票的场景会导致超时支付，替换为支付成功时间来处理分账
                .ge(PayOrder::getSuccessTime, DateUtils.addDays(now, -30))
                // 支付成功时间要比现在早1分钟以上的，如果是刚支付成功的就分账可能会出现 【订单处理中，请稍后重试】 的问题
                .lt(PayOrder::getSuccessTime, DateUtil.offsetSecond(now, -60))
                .in(PayOrder::getStatus, statutList)
        );
    }


}
