package yq.mall.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.SpringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.constant.PromotionCacheConstant;
import yq.mall.controller.client.promotion.bo.ClientPromotionTempQueryBo;
import yq.mall.domain.PromotionCouponTemplate;
import yq.mall.domain.bo.promotion.PromotionCouponTemplateBo;
import yq.mall.domain.vo.promotion.PromotionCouponTemplateVo;
import yq.mall.enums.promotion.CouponScopeEnum;
import yq.mall.enums.promotion.CouponTakeTypeEnum;
import yq.mall.enums.promotion.CouponValidTypeEnum;
import yq.mall.mapper.PromotionCouponTemplateMapper;
import yq.mall.service.IPromotionCouponTemplateService;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static yq.mall.constant.MallLockConstant.LOCK_PROMOTION_COUPON_TAKE_COUNT;

/**
 * 优惠券模板服务实现
 *
 * <AUTHOR> Suen
 */
@Service
public class PromotionCouponTemplateServiceImpl implements IPromotionCouponTemplateService {

    @Resource
    private PromotionCouponTemplateMapper baseMapper;

    /**
     * 创建优惠券模板
     *
     * @param bo 优惠券模板
     * @return 优惠券模板id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createTemp(PromotionCouponTemplateBo bo) {
        var temp = MapstructUtils.convert(bo, PromotionCouponTemplate.class);
        ValidateUtils.notNull(temp, "保存优惠券模板失败");

        validTempSave(temp);
        baseMapper.insert(temp);
        return temp.getId();
    }

    /**
     * 更新优惠券模板
     *
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#bo.id")
    @Override
    public void updateTemp(PromotionCouponTemplateBo bo) {
        var temp = getExists(bo.getId());

        // 检验发放数量不能小于已领取数量
        if (bo.getQuantityLimit() < temp.getTakeCount()) {
            ValidateUtils.isTrue(false, "发放数量不能小于已领取数量");
        }

        temp = MapstructUtils.convert(bo, PromotionCouponTemplate.class);
        ValidateUtils.notNull(temp, "保存优惠券模板失败");

        validTempSave(temp);
        var count = baseMapper.updateById(temp);
        ValidateUtils.isTrue(count == 1, "更新优惠券模板失败");
    }

    /**
     * 更新优惠券模板状态
     *
     * @param id     优惠券模板id
     * @param enable 优惠券模板状态
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#id")
    @Override
    public void updateTempStatus(Long id, boolean enable) {
        getExists(id);
        baseMapper.update(Wrappers.<PromotionCouponTemplate>lambdaUpdate()
            .eq(PromotionCouponTemplate::getId, id)
            .set(PromotionCouponTemplate::getEnable, enable));
    }

    /**
     * 删除优惠券模板
     *
     * @param id 优惠券模板id
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#id")
    @Override
    public void deleteTemp(Long id) {
        var temp = getExists(id);
        // 如果已有用户领取优惠券，则不能删除
        ValidateUtils.isTrue(temp.getTakeCount() == 0, "已有用户领取优惠券，不能删除");
        baseMapper.deleteById(temp.getId());
    }

    /**
     * 获取优惠券模板
     *
     * @param id 优惠券模板id
     * @return 优惠券模板
     */
    @Cacheable(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#id")
    @Override
    public PromotionCouponTemplate getById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 【后台】查询优惠券模板分页
     *
     * @return 优惠券模板分页
     */
    @Override
    public TableDataInfo<PromotionCouponTemplateVo> page(PromotionCouponTemplateBo bo, PageQuery page) {
        var queryWrapper = Wrappers.<PromotionCouponTemplate>lambdaQuery()
            .eq(bo.getEnable() != null, PromotionCouponTemplate::getEnable, bo.getEnable())
            .eq(bo.getUseScope() != null, PromotionCouponTemplate::getUseScope, bo.getUseScope())
            .eq(bo.getTakeType() != null, PromotionCouponTemplate::getTakeType, bo.getTakeType())
            .like(bo.getName() != null, PromotionCouponTemplate::getName, bo.getName())
            .orderByDesc(PromotionCouponTemplate::getCreateTime);

        var pages = baseMapper.selectVoPage(page.build(), queryWrapper);
        return TableDataInfo.build(pages);
    }


    /**
     * 更新优惠券模板的领取数量
     *
     * @param id        优惠券模板id
     * @param incrCount 增加数量
     */
    @Lock4j(name = LOCK_PROMOTION_COUPON_TAKE_COUNT, keys = "#id")
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#id")
    public void updateTempTakeCount(Long id, Integer incrCount) {
        getExists(id);
        baseMapper.update(Wrappers.<PromotionCouponTemplate>lambdaUpdate()
            .eq(PromotionCouponTemplate::getId, id)
            .setSql("take_count = take_count + " + incrCount));
    }

    /**
     * 更新优惠券模板的使用数量
     *
     * @param id        优惠券模板id
     * @param incrCount 增加数量
     */
    @Lock4j(name = LOCK_PROMOTION_COUPON_TAKE_COUNT, keys = "#id")
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PromotionCacheConstant.CouponTemplate.BASE, key = "#id")
    public void updateTempUseCount(Long id, Integer incrCount) {
        getExists(id);
        baseMapper.update(Wrappers.<PromotionCouponTemplate>lambdaUpdate()
            .eq(PromotionCouponTemplate::getId, id)
            .setSql("use_count = use_count + " + incrCount));
    }

    @Override
    public List<PromotionCouponTemplate> getTempByTakeType(CouponTakeTypeEnum takeType) {
        return baseMapper.selectList(Wrappers.<PromotionCouponTemplate>lambdaQuery()
            .eq(PromotionCouponTemplate::getTakeType, takeType.getType())
            .eq(PromotionCouponTemplate::getEnable, true));
    }

    @Override
    public List<PromotionCouponTemplate> getList(ClientPromotionTempQueryBo bo) {
        var ids = baseMapper.selectList(Wrappers.<PromotionCouponTemplate>lambdaQuery()
            .select(PromotionCouponTemplate::getId)
            .eq(PromotionCouponTemplate::getTakeType, bo.getType())
            .eq(PromotionCouponTemplate::getEnable, true)
            .eq(PromotionCouponTemplate::getDelFlag, "0")
            .and(wrapper -> wrapper
                .eq(PromotionCouponTemplate::getUseScope, CouponScopeEnum.ALL.getType())
                .or()
                .apply(bo.getSchoolId() != null, "JSON_CONTAINS(school_ids, JSON_ARRAY({0}))", bo.getSchoolId())
            )
            .and(wrapper -> wrapper
                .ne(PromotionCouponTemplate::getValidType, CouponValidTypeEnum.DATE.getType())
                .or()
                .ge(PromotionCouponTemplate::getValidDateTo, LocalDateTime.now())
            )
        ).stream().map(PromotionCouponTemplate::getId).toList();
        var res = new ArrayList<PromotionCouponTemplate>();
        if (CollUtil.isNotEmpty(ids)) {
            var self = getSelf();
            ids.forEach(id -> {
                var temp = self.getById(id);
                if (temp != null) {
                    res.add(temp);
                }
            });
        }
        return res;
    }


    /**
     * 获取优惠券模板
     *
     * @param id 优惠券模板id
     * @return 优惠券模板
     */
    private PromotionCouponTemplate getExists(Long id) {
        var temp = getSelf().getById(id);
        ValidateUtils.notNull(temp, "优惠券模板不存在");
        return temp;
    }

    /**
     * 校验优惠券模板 - 保存
     * @param temp 优惠券模板
     */
    private void validTempSave(PromotionCouponTemplate temp) {

        ValidateUtils.isTrue(ArrayUtil.contains(CouponTakeTypeEnum.ARRAYS, temp.getTakeType()), "优惠券领取方式不正确");
        ValidateUtils.isTrue(ArrayUtil.contains(CouponValidTypeEnum.ARRAYS, temp.getValidType()), "优惠券有效期类型不正确");
        if (CouponValidTypeEnum.DATE.getType().equals(temp.getValidType())) {
            ValidateUtils.notNull(temp.getValidDateFrom(), "优惠券有效期开始时间不能为空");
            ValidateUtils.notNull(temp.getValidDateTo(), "优惠券有效期结束时间不能为空");

            ValidateUtils.isTrue(temp.getValidDateFrom().isBefore(temp.getValidDateTo()), "优惠券有效期开始时间不能大于结束时间");

            // 置空有效期天数
            temp.setValidTermStart(null);
            temp.setValidTermEnd(null);
        } else if (CouponValidTypeEnum.TERM.getType().equals(temp.getValidType())) {
            ValidateUtils.isTrue(
                temp.getValidTermStart() != null && temp.getValidTermStart() >= 0,
                "请设置正确的固定天数"
            );
            ValidateUtils.isTrue(
                temp.getValidTermEnd() != null && temp.getValidTermEnd() >= 0,
                "请设置正确的固定天数"
            );

            // 置空日期
            temp.setValidDateFrom(null);
            temp.setValidDateTo(null);
        }

        ValidateUtils.isTrue(ArrayUtil.contains(CouponScopeEnum.ARRAYS, temp.getUseScope()), "优惠券使用范围不正确");
        // 如果是学校券，那么必须选择学校
        if (CouponScopeEnum.SCHOOL.getType().equals(temp.getUseScope())) {
            ValidateUtils.notNull(CollUtil.isNotEmpty(temp.getSchoolIds()), "学校id不能为空");
        }


    }

    private PromotionCouponTemplateServiceImpl getSelf() {
        return SpringUtils.getBean(getClass());
    }

}
