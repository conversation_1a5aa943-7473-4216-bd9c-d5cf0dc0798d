package yq.mall.service.impl;

import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.mall.domain.bo.MemberFamilyBo;
import yq.mall.domain.vo.MemberFamilyVo;
import yq.mall.domain.MemberFamily;
import yq.mall.mapper.MemberFamilyMapper;
import yq.mall.service.IMemberFamilyService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 家庭Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RequiredArgsConstructor
@Service
public class MemberFamilyServiceImpl implements IMemberFamilyService {

    @Resource
    private final MemberFamilyMapper baseMapper;

    /**
     * 查询家庭
     */
    @Override
    public MemberFamilyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询家庭列表
     */
    @Override
    public TableDataInfo<MemberFamilyVo> queryPageList(MemberFamilyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberFamily> lqw = buildQueryWrapper(bo);
        Page<MemberFamilyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询家庭列表
     */
    @Override
    public List<MemberFamilyVo> queryList(MemberFamilyBo bo) {
        LambdaQueryWrapper<MemberFamily> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MemberFamily> buildQueryWrapper(MemberFamilyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberFamily> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), MemberFamily::getName, bo.getName());
        lqw.eq(bo.getOwnerId() != null, MemberFamily::getOwnerId, bo.getOwnerId());
        return lqw;
    }

    /**
     * 新增家庭
     */
    @Override
    public Boolean insertByBo(MemberFamilyBo bo) {
        MemberFamily add = MapstructUtils.convert(bo, MemberFamily.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改家庭
     */
    @Override
    public Boolean updateByBo(MemberFamilyBo bo) {
        MemberFamily update = MapstructUtils.convert(bo, MemberFamily.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberFamily entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除家庭
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public MemberFamilyVo queryByOwnerId(Long ownerId) {
        LambdaQueryWrapper<MemberFamily> lqw = Wrappers.lambdaQuery();
        lqw.eq(MemberFamily::getOwnerId, ownerId);
        lqw.last("limit 1");

        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public Boolean dissolveFamily(Long id) {
        return baseMapper.deleteById(id) > 0;
    }


}
