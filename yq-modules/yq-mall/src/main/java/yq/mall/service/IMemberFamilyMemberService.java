package yq.mall.service;

import yq.mall.domain.bo.FamilyShareBo;
import yq.mall.domain.vo.MemberFamilyMemberVo;
import yq.mall.domain.bo.MemberFamilyMemberBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 家庭成员Service接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IMemberFamilyMemberService {

    /**
     * 查询家庭成员
     */
    MemberFamilyMemberVo queryById(Long id);

    /**
     * 查询家庭成员列表
     */
    TableDataInfo<MemberFamilyMemberVo> queryPageList(MemberFamilyMemberBo bo, PageQuery pageQuery);

    /**
     * 查询家庭成员列表
     */
    List<MemberFamilyMemberVo> queryList(MemberFamilyMemberBo bo);

    /**
     * 新增家庭成员
     */
    Boolean insertByBo(MemberFamilyMemberBo bo);

    /**
     * 修改家庭成员
     */
    Boolean updateByBo(MemberFamilyMemberBo bo);

    /**
     * 校验并批量删除家庭成员信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     *  返回分享的临时token, 用于邀请家庭成员
     */
    String getShareToken(Long userId);

    /**
     * 解析生成的token加入对应的家庭
     */
    void parseShareToken(FamilyShareBo bo);

    /**
     * 退出当前家庭
     */
    void quitFamily(Long userId, Long operatorId);

    /**
     * 查询家庭成员列表
     */
    List<MemberFamilyMemberVo> listFamilyMember(Long familyId);

    /**
     * 通过userId查询家庭成员列表
     */
    List<MemberFamilyMemberVo> listFamilyMemberByUserId(Long userId);

    /**
     * 根据用户id获取家庭id
     */
    Long getFamilyIdByUserId(Long userId);

    /**
     * 解散家庭
     * @param userId
     */
    void dissolveFamily(Long userId);

    /**
     * 根据id修改家庭成员备注
     */
    void updateMemoById(Long userId ,Long operatorId, String memo);

    /**
     * 校验token邀请是否有效
     */
    Boolean checkTokenValid(String token,Long userId);
}
