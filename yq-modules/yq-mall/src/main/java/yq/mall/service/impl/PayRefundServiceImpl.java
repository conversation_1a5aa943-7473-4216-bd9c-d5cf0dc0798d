package yq.mall.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.exception.ServiceException;
import yq.common.core.utils.DateUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.config.properties.MallProperties;
import yq.mall.config.properties.WxPayV3Properties;
import yq.mall.controller.pay.bo.PayRefundNotifyBo;
import yq.mall.domain.PayRefund;
import yq.mall.domain.bo.pay.PayRefundBo;
import yq.mall.domain.vo.pay.PayRefundVo;
import yq.mall.enums.pay.*;
import yq.mall.mapper.PayRefundMapper;
import yq.mall.service.IPayOrderService;
import yq.mall.service.IPayRefundService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 退款单服务实现类
 *
 * <AUTHOR> Suen
 */
@Slf4j
@Service
public class PayRefundServiceImpl implements IPayRefundService {

    @Resource
    private MallProperties mallProperties;

    @Resource
    private PayRefundMapper baseMapper;

    @Resource
    private WxPayService wxPayService;

    @Resource
    private WxPayV3Properties wxPayProperties;

    @Resource
    private IPayOrderService payOrderService;

    @Lazy
    @Resource
    private PayRefundServiceImpl self;

    @Override
    public PayRefund getById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRefund(PayRefund refund) {
        // 检查退款
        refund.setStatus(PayRefundStatusEnum.WAITING.getType());
        refund.setNotifyUrl(wxPayProperties.getNotifyUrl().getRefund());
        refund.setRetryCount(0);

        var payOrder = payOrderService.getById(refund.getPayOrderId());
        // 检查支付单是否需要分账
        refund.setProfitSharingReturn(payOrder.getProfitSharing());
        if (refund.getProfitSharingReturn()) {
            refund.setProfitSharingReturnStatus(PayRefundProfitSharingStatusEnum.WAITING.getType());
        }

        // 填充费率
        refund.setChannelFeeRate(payOrder.getChannelFeeRate());

        // round( ( 本次退款金额 / (支付总金额 - 已退金额) ) * (支付手续费 - 已退手续费), 2)
        List<PayRefundVo> payRefundList = self.getList(refund.getMerchantOrderId(), null);
        var sumChannelFeeAmount = payRefundList.stream()
            .map(PayRefundVo::getChannelFeeAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);


        // 合计已退金额
        var sumRefundAmount = payRefundList.stream()
            .map(PayRefundVo::getRefundAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 剩余可退金额：支付总金额 - 已退金额
        var remainingRefundAmount = payOrder.getTotalAmount().subtract(sumRefundAmount);
        // 可退手续费：支付手续费 - 已退手续费
        var remainingFeeAmount = payOrder.getChannelFeeAmount().subtract(sumChannelFeeAmount);
        var feeAmount = refund.getRefundAmount()
            .multiply(remainingFeeAmount)
            .divide(remainingRefundAmount, 2, RoundingMode.HALF_UP);

        refund.setChannelFeeAmount(feeAmount);

        if (feeAmount.compareTo(remainingFeeAmount) > 0) {
           refund.setChannelFeeAmount(remainingFeeAmount);
        }


        refund.setCreateTime(new Date());
        baseMapper.insert(refund);

        return refund.getId();
    }

    /**
     * 退款成功回调
     *
     * @param bo 退款通知业务对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRefundSuccess(PayRefundNotifyBo bo) {

        var zdt = ZonedDateTime.parse(bo.getSuccessTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZZZZZ"));
        var successTime = Date.from(zdt.toInstant());

        // FIXME 处理未找到退款单的情况
        LambdaUpdateWrapper<PayRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PayRefund::getId, bo.getOutRefundNo())
            .set(PayRefund::getChannelOrderNo, bo.getTransactionId())
            .set(PayRefund::getChannelRefundNo, bo.getRefundId())
            .set(PayRefund::getSuccessTime, successTime)
            .set(PayRefund::getStatus, PayRefundStatusEnum.SUCCESS.getType())
            .set(PayRefund::getUserReceivedAccount, bo.getUserReceivedAccount())
            .set(PayRefund::getChannelErrorMsg, "")
            .set(PayRefund::getRawData, bo.getRawData());


        baseMapper.update(null, wrapper);

    }

    /**
     * 记录退款失败原因
     *
     * @param message 失败原因
     */
    @Override
    public void updateRefundFail(Long refundId, String message, PayRefund refund) {
        LambdaUpdateWrapper<PayRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PayRefund::getId, refundId)
            .set(PayRefund::getChannelErrorMsg, message);

        // 根据 refund 的重试次数，判断是否需要重试
        int retryCount = refund.getRetryCount();
        // 找到下一次重试的间隔
        int interval = mallProperties.getRetryIntervalByIndex(retryCount);

        if (interval == 0) {
            // 到达最大重试次数，更新状态为失败
            wrapper.set(PayRefund::getStatus, PayRefundStatusEnum.FAILURE.getType());
        } else {
            // 更新重试次数
            wrapper.set(PayRefund::getRetryCount, retryCount + 1);
            // 更新状态为重试
            wrapper.set(PayRefund::getStatus, PayRefundStatusEnum.RETRY.getType());
            // 更新下一次重试时间
            wrapper.set(PayRefund::getNextRetryTime, LocalDateTime.now().plusSeconds(interval));
        }

        int count = baseMapper.update(null, wrapper);
        ValidateUtils.isTrue(count == 1, "更新退款状态失败");
    }

    /**
     * 申请退款
     * @param id payRefundId
     */
    @Override
    public void applyRefundById(Long id) {
        var payRefund = self.getById(id);
        ValidateUtils.isTrue(payRefund != null, "退款异常，请联系客服处理");
        ValidateUtils.isFalse(PayRefundStatusEnum.SUCCESS.getType().equals(payRefund.getStatus()), "支付退款单已处理");
        if ( PayRefundStatusEnum.RETRY.getType().equals(payRefund.getStatus())) {
            this.payRefundRetry(payRefund);
        }

        if (PayChannelEnum.WX_LITE.getCode().equals(payRefund.getChannelCode())) {
            self.requestRefundWx(payRefund);
            // 更新一下apply_success_time
            baseMapper.update(new LambdaUpdateWrapper<PayRefund>()
                .set(PayRefund::getApplyTime, payRefund.getApplyTime())
                .set(PayRefund::getChannelRefundNo,payRefund.getChannelRefundNo())
                .eq(PayRefund::getId, id));

        } else {
            throw new ServiceException("暂不支持该渠道退款");
        }
    }

    /**
     * 请求微信退款
     *
     * @param refund 退款单
     */
    @Override
    public void requestRefundWx(PayRefund refund) {
        var request = new WxPayRefundV3Request()
            .setOutTradeNo(String.valueOf(refund.getPayOrderId()))
            .setOutRefundNo(String.valueOf(refund.getId()))
            .setAmount(new WxPayRefundV3Request.Amount()
                .setRefund(refund.getRefundAmount().multiply(BigDecimal.valueOf(100)).intValue())
                .setTotal(refund.getPayPrice().multiply(BigDecimal.valueOf(100)).intValue())
                .setCurrency(WxPayConstants.CurrencyType.CNY)
            )
            .setNotifyUrl(wxPayProperties.getNotifyUrl().getRefund());

        WxPayRefundV3Result refundRes;
        try {
            refundRes = wxPayService.refundV3(request);
            refund.setChannelRefundNo(refundRes.getRefundId());

            var zdt = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZZZZZ");
            var applyTime = Date.from(ZonedDateTime.parse(refundRes.getCreateTime(), zdt).toInstant());
            refund.setApplyTime(applyTime);
        } catch (WxPayException e) {
            log.error("微信退款失败", e);
            updateRefundFail(refund.getId(), e.getMessage(), refund);
            throw new ServiceException("微信退款失败");
        } catch (Exception e) {
            log.error("退款失败", e);
            throw new ServiceException("退款失败");
        }

    }

    /**
     * 根据orderId查询退款单记录
     *
     * @param orderId 订单ID
     * @param status  退款状态
     * @return 退款单列表
     */
    @Override
    public List<PayRefundVo> getList(Long orderId, Integer status) {
        LambdaUpdateWrapper<PayRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PayRefund::getMerchantOrderId, orderId)
            .eq(status != null, PayRefund::getStatus, status);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 重试时清除错误信息
     * @param payRefund 退款单
     */
    private void payRefundRetry(PayRefund payRefund) {
        // 将状态设置为退款中并清空错误信息
        LambdaUpdateWrapper<PayRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PayRefund::getId, payRefund.getId())
            .set(PayRefund::getChannelErrorMsg, "")
            .set(PayRefund::getStatus, PayRefundStatusEnum.WAITING.getType());
        baseMapper.update(null, wrapper);
    }

    /**
     * 查询支付退款单重试列表
     * @return
     */
    @Override
    public List<PayRefund> payRefundRetryList() {
        //找到需要重试的支付退款单
        LambdaUpdateWrapper<PayRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PayRefund::getStatus, PayRefundStatusEnum.RETRY.getType());
        wrapper.le(PayRefund::getNextRetryTime, LocalDateTime.now());
        return baseMapper.selectList(wrapper);
    }

    /**
     * 分页查询退款单
     */
    @Override
    public TableDataInfo<PayRefundVo> queryPageList(PayRefundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayRefund> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getMerchantOrderId() != null, PayRefund::getMerchantOrderId, bo.getMerchantOrderId());
        lqw.eq(bo.getStatus() != null, PayRefund::getStatus, bo.getStatus());
        lqw.orderByDesc(PayRefund::getSuccessTime);
        Page<PayRefundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 更新支付单分账状态
     *
     * @param id     支付单ID
     * @param status 支付单状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProfitSharingReturnStatus(Long id, PayRefundProfitSharingStatusEnum status) {
        var updateCount = baseMapper.update(
            Wrappers.lambdaUpdate(PayRefund.class)
                .set(PayRefund::getProfitSharingReturnStatus, status.getType())
                // 主键
                .eq(PayRefund::getId, id)
        );
        ValidateUtils.isTrue(updateCount == 1, "更新支付退款单分账状态失败");
    }

    /**
     * 查询待分账退回的退款单
     *
     * @return 待分账退回的退款单
     */
    @Override
    public List<PayRefund> listWaitingProfitSharingReturn() {
        // 分账退回无需等待退款状态
        return baseMapper.selectList(Wrappers.<PayRefund>lambdaQuery()
            .select(
                PayRefund::getId,
                PayRefund::getPayOrderId,
                PayRefund::getChannelCode,
                PayRefund::getRefundAmount,
                PayRefund::getReason
            )
            .eq(PayRefund::getProfitSharingReturn, true)
            .eq(PayRefund::getProfitSharingReturnStatus, PayRefundProfitSharingStatusEnum.WAITING.getType())

            // 只查找创建时间是最近30天的，否则认为是异常数据，需要手动处理
            .ge(PayRefund::getCreateTime, DateUtils.addDays(new Date(), -30))
        );
    }
}
