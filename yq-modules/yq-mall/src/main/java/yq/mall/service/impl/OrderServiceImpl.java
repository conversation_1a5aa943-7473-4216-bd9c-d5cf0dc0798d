package yq.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.api.*;
import yq.bus.api.domain.vo.*;
import yq.common.core.utils.DateUtils;
import yq.common.core.utils.SpringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
//import yq.common.redis.utils.CacheUtils;
import yq.mall.aop.OrderLog;
//import yq.mall.constant.OrderCacheConstant;
import yq.mall.api.constant.MallSystemConfigConstant;
import yq.mall.constant.ClientConfigConstant;
import yq.mall.constant.MallLockConstant;
import yq.mall.controller.admin.mall.bo.AdminOrderBo;
import yq.mall.controller.admin.mall.bo.CancelPostPayOrderBo;
import yq.mall.controller.client.order.bo.ClientOrderTicketBo;
import yq.mall.controller.client.order.vo.ClientHasPostPayVo;
import yq.mall.controller.client.order.vo.ClientOrderVo;
import yq.mall.controller.client.order.vo.ClientRefundTicketInfoVo;
import yq.mall.controller.client.order.vo.TicketInfoVo;
import yq.mall.domain.Order;
import yq.mall.domain.OrderWaitlistTicket;
import yq.mall.domain.PayOrder;
import yq.mall.domain.Refund;
import yq.mall.domain.bo.OrderTicketBo;
import yq.mall.domain.bo.PostPayOrderTicketBo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.domain.vo.OrderVo;
import yq.mall.domain.vo.OrderWaitlistTicketVo;
import yq.mall.domain.vo.RefundRulesVo;
import yq.mall.domain.vo.order.OrderPriceVo;
import yq.mall.enums.TicketStatusEnum;
import yq.mall.enums.order.*;
import yq.mall.enums.pay.PayChannelEnum;
import yq.mall.enums.promotion.CouponScopeEnum;
import yq.mall.enums.refund.RefundStatusEnum;
import yq.mall.mapper.OrderMapper;
import yq.mall.service.*;
import yq.system.api.RemoteConfigService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 *
 * <AUTHOR> Suen
 */
@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {

    @Resource
    private OrderMapper baseMapper;

    @Resource
    private IOrderTicketService orderTicketService;

    @Lazy
    @Resource
    private IPayOrderService payOrderService;

    @Resource
    private IPromotionCouponService couponService;
    @Resource
    private IPromotionCouponTemplateService couponTemplateService;

    @DubboReference
    private RemoteDepartPlanService remoteDepartPlanService;

    @DubboReference
    private RemoteDepartPlanStationService remoteDepartPlanStationService;

    @DubboReference
    private RemoteLineTemplateService remoteLineTemplateService;

    @DubboReference
    private RemoteLineScheduleService remoteLineScheduleService;

    @DubboReference
    private RemoteVehicleService remoteVehicleService;

    @Resource
    @Lazy
    private IRefundRulesService refundRulesService;

    @DubboReference
    @Resource
    private  RemoteConfigService remoteConfigService;

    @Resource
    @DubboReference
    private RemoteStationService remoteStationService;

    @Resource
    private IOrderWaitlistTicketService orderWaitlistTicketService;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     */
    private OrderServiceImpl getSelf() {
        return SpringUtils.getBean(getClass());
    }


    /**
     * 计算车票价格
     *
     * @param bo    订单信息
     * @param plans 发车计划
     */
    @Override
    public OrderPriceVo calcTicketPrice(ClientOrderTicketBo bo, List<RemoteBusDepartPlan> plans) {
        var ticketCount = bo.getPassengers() == null
            ? BigDecimal.ONE
            : BigDecimal.valueOf(bo.getPassengers().size());

        var price = OrderPriceVo.builder()
            .totalPrice(BigDecimal.ZERO)
            .payPrice(BigDecimal.ZERO)
            .couponPrice(BigDecimal.ZERO)
            .discountPrice(BigDecimal.ZERO)
            .build();
        // 查询行程价格
        plans.forEach(plan -> {
            price.setTotalPrice(price.getTotalPrice().add(plan.getPrice().multiply(ticketCount)));
            price.setPayPrice(price.getPayPrice().add(plan.getDiscountPrice().multiply(ticketCount)));
        });
        price.setDiscountPrice(price.getTotalPrice().subtract(price.getPayPrice()));

        if (bo.getCouponId() != null) {
            // 优惠券校验
            var coupon = couponService.validCoupon(bo.getCouponId(), bo.getUserId());
            var couponTemp = couponTemplateService.getById(coupon.getTemplateId());
            ValidateUtils.notNull(couponTemp, "优惠券已过期");

            // 优惠券金额是否一致
            ValidateUtils.isTrue(couponTemp.getMaxDiscount().compareTo(bo.getCouponPrice()) == 0, "优惠券状态已过期, 请重新下单");
            price.setCouponPrice(bo.getCouponPrice());

            // 优惠券金额兜底，如果优惠券金额大于payPrice，那么payPrice为0.01
            if (price.getPayPrice().compareTo(bo.getCouponPrice()) > 0) {
                price.setPayPrice(price.getPayPrice().subtract(bo.getCouponPrice()));
            } else {
                price.setPayPrice(BigDecimal.valueOf(0.01));
            }

            // 如果是学校券，那么plans所有的schoolId必须在优惠券的schoolIds中
            if (CouponScopeEnum.SCHOOL.getType().equals(couponTemp.getUseScope())) {
                var planSchoolIds = CollUtil.newArrayList();
                plans.forEach(plan -> planSchoolIds.add(plan.getSchoolId()));
                ValidateUtils.isTrue(CollUtil.containsAll(couponTemp.getSchoolIds(), planSchoolIds), "优惠券不适用于当前行程");
            }

            // TODO 价格校验，是否满足使用门槛
        }

        // 订单金额必须大于0
        ValidateUtils.isTrue(price.getPayPrice().compareTo(BigDecimal.ZERO) > 0, "订单金额必须大于0");
        // 前后端下单金额必须一致
        ValidateUtils.isTrue(price.getTotalPrice().compareTo(bo.getTotalPrice()) == 0, "票价已过期, 请重新下单");
        ValidateUtils.isTrue(price.getPayPrice().compareTo(bo.getPayPrice()) == 0, "票价已过期, 请重新下单");

        return price;
    }

    /**
     * 更新订单支付成功
     *
     * @param id       订单编号
     * @param payOrder 支付订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = OrderCacheConstant.ID, key = "#id")
    public void updateOrderPaid(Long id, PayOrder payOrder) {
        // 校验订单是否可支付
        var order = getSelf().getOrder(id);
        validOrderPayable(order, payOrder);

        // 更新订单状态
        var wrapper = Wrappers.lambdaUpdate(Order.class)
            .set(Order::getStatus, OrderStatusEnum.PAYED.getType())
            .set(Order::getPayStatus, true)
            .set(Order::getPayTime, LocalDateTime.now())
            .set(Order::getPayChannelCode, payOrder.getChannelCode())
            .eq(Order::getId, id);
        ValidateUtils.isTrue(baseMapper.update(wrapper) > 0, "更新订单状态失败");

        // 判断订单是否为候补车票类型，并根据不同情况进行处理
        if (isWaitlistOrder(order)) {
            // 更新候补车票状态
            orderWaitlistTicketService.updateWaitlistTicketPaid(id);
            // 尝试候补
            orderWaitlistTicketService.queryListByOrderId(order.getId())
                .stream().map(OrderWaitlistTicket::getDepartPlanId)
                .distinct().forEach(departPlanId -> orderTicketService.waitlistTicket(departPlanId));
        } else {
            // 将车票状态更新为已支付
            orderTicketService.updateTicketPaid(id);
        }


    }

    /**
     * 【会员】创建订单 - 车票
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @OrderLog(operateType = OrderOperateTypeEnum.MEMBER_CREATE)
    public Long createOrderTicket(ClientOrderTicketBo bo) {
        // 0. 校验订单
        // 检查是否有待支付订单
        var orderCount = baseMapper.selectCount(Wrappers.lambdaQuery(Order.class)
            .select(Order::getId)
            .eq(Order::getUserId, bo.getUserId())
            .eq(Order::getStatus, OrderStatusEnum.UNPAID.getType())
        );
        ValidateUtils.isTrue(orderCount == 0, "您有待支付订单，请先支付");

        // TODO 购买多个行程考虑拆单处理
        var planList = validateOrder(bo);

        // 1. 计算价格
        var price = calcTicketPrice(bo, planList);

        // 获取第一个 plan 的 waitListFlag
        Boolean waitListFlag = bo.getPlans().getFirst().getWaitListFlag();

        // 2. 创建订单
        var order = createOrder(bo, price, waitListFlag);

        // 3. 创建订单前置处理
        // 4. 保存订单
        var orderRes = baseMapper.insert(order);
        ValidateUtils.isFalse(orderRes == 0, "创建订单失败");

        // 5. 创建行程
        var ticketList = buildOrderTicketItem(order, planList, bo, waitListFlag);
        if (waitListFlag) {
            var orderWaitlistTickets = BeanUtil.copyToList(ticketList, OrderWaitlistTicket.class);
            String timeout = remoteConfigService.selectConfigByKey(MallSystemConfigConstant.ORDER_WAITLIST_TIMEOUT);
            Integer deadLine = Convert.toInt(timeout);
            orderWaitlistTickets.forEach(waitlistTicket -> waitlistTicket.setDeadline(deadLine));
            orderWaitlistTicketService.createOrderWaitlistTicket(orderWaitlistTickets);
        }else {
            orderTicketService.createOrderTickets(ticketList);
        }


        // 核销优惠券
        if (bo.getCouponId() != null) {
            couponService.useCoupon(bo.getUserId(), bo.getCouponId(), order.getId());
        }

        // 6. 生成预支付
        var payOrderId = payOrderService.createOrder(order);
        order.setPayOrderId(payOrderId);
        baseMapper.update(Wrappers.lambdaUpdate(Order.class)
            .set(Order::getPayOrderId, payOrderId)
            .eq(Order::getId, order.getId())
        );

        return order.getId();
    }


    /**
     * 【系统】根据id获取订单
     *
     * @param id 订单ID
     * @return 订单
     */
//    @Cacheable(value = OrderCacheConstant.ID, key = "#id", unless = "#result == null")
    @Override
    public Order getOrder(Long id) {
        var order = baseMapper.selectById(id);
        ValidateUtils.notNull(order, "订单不存在");
        return order;
    }

    /**
     * 【会员】获取订单
     *
     * @param userId 会员ID
     * @param id     订单ID
     * @return 订单
     */
    @Override
    public Order getOrder(Long userId, Long id) {
        var order = getSelf().getOrder(id);
        ValidateUtils.isTrue(order.getUserId().equals(userId), "无权操作");
        return order;
    }

    /**
     * 【会员】取消订单
     *
     * @param userId 会员ID
     * @param id     订单ID
     */
    @Override
//    @CacheEvict(value = OrderCacheConstant.ID, key = "#id")
    public void cancelOrderByMember(Long userId, Long id) {
        var order = getSelf().getOrder(userId, id);
        // 校验存在
        ValidateUtils.notNull(order, "订单不存在");
        // 校验状态
        ValidateUtils.equals(OrderStatusEnum.UNPAID.getType(), order.getStatus(), "当前订单不处于待支付状态");
        // 现场补票订单用户不能取消
        ValidateUtils.isFalse(OrderTypeEnum.TICKET_POST_PAY.getType().equals(order.getType()), "现场补票订单用户不能取消，请联系客服处理");
        // 取消订单
        getSelf().cancelOrder(order, OrderCancelTypeEnum.MEMBER_CANCEL);
    }

    /**
     * 【系统】取消订单
     *
     * @return 取消订单数量
     */
    @Override
    public int cancelOrderBySystem() {
        // 1. 检查超过5分钟未支付的订单
        var orderList = baseMapper.selectList(Wrappers.lambdaQuery(Order.class)
            .eq(Order::getStatus, OrderStatusEnum.UNPAID.getType())
            .lt(Order::getCreateTime, LocalDateTime.now().minusMinutes(5))
            .ne(Order::getType,OrderTypeEnum.TICKET_POST_PAY.getType())  // 非现场补票订单
            .orderByAsc(Order::getCreateTime)
        );
        if (CollUtil.isEmpty(orderList)) {
            return 0;
        }

        // 2. 取消订单
        int count = 0;
        for (Order order : orderList) {
            try {
                getSelf().cancelOrder(order, OrderCancelTypeEnum.PAY_TIMEOUT);
//                CacheUtils.evict(OrderCacheConstant.ID, order.getId());
                count++;
            } catch (Exception e) {
                log.error("[cancelOrderBySystem][取消订单({}) 发生异常]", order.getId(), e);
            }
        }
        return count;
    }

    /**
     * 取消订单
     *
     * @param order      订单
     * @param cancelType 取消类型
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(name = MallLockConstant.LOCK_ORDER, keys = {"#order.id"}, expire = 10000, acquireTimeout = 200)
    public void cancelOrder(Order order, OrderCancelTypeEnum cancelType) {

        // 取消支付
        if (order.getPayOrderId() != null) {
            payOrderService.updateOrderCancel(order.getPayOrderId());
        }

        // 更新订单状态
        var count = Wrappers.lambdaUpdate(Order.class)
            .set(Order::getStatus, OrderStatusEnum.CANCELED.getType())
            .set(Order::getCancelTime, new Date())
            .set(Order::getCancelType, cancelType.getType())
            .eq(Order::getId, order.getId());

        // 是候补订单则更新候补状态
        if (order.getExtraInfo() instanceof Order.WaitlistExtra &&
            ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistFlag()) {
            count.setSql("extra_info = JSON_SET(extra_info, '$.waitlistStatus', {0})", OrderWaitlistStatusEnum.CANCELLED.getStatus());
        }
        // 是现场补票订单则更新原因和取消时间
        if (order.getExtraInfo() instanceof Order.TicketPostPayExtra && OrderCancelTypeEnum.ADMIN_CANCEL.equals(cancelType)) {
            count.setSql("extra_info = JSON_SET(extra_info, '$.reason', {0})", ((Order.TicketPostPayExtra) order.getExtraInfo()).getReason());
            count.setSql("extra_info = JSON_SET(extra_info, '$.cancelTime', {0})", new Date());
        }

        ValidateUtils.isTrue(baseMapper.update(null, count) == 1, "取消订单失败");

        // 回退优惠券
        if (order.getCouponId() != null) {
            try {
                couponService.returnUsedCoupon(order.getCouponId());
            } catch (Exception e) {
                log.error("[updateOrderRefund][orderId({}) 回退优惠券失败]", order.getId(), e);
            }
        }



        if (OrderTypeEnum.isTicketType(order.getType())) {
            orderTicketService.cancelTicket(order.getId());

            // 如果是候补订单则更新候补车票状态
            if(order.getExtraInfo() instanceof Order.WaitlistExtra &&
                ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistFlag()) {
                orderWaitlistTicketService.updateWaitlistTicketByOrderId(order.getId());
            }
        }

        // 记录日志
        log.info("[cancelOrder][orderId({}) 取消订单成功]", order.getId());
    }


    /**
     * 查询运营管理端订单列表
     */
    @Override
    public TableDataInfo<OrderVo> queryOrderPageList(AdminOrderBo bo, PageQuery pageQuery) {
        if (bo.getCreateTimeStart() != null && bo.getCreateTimeEnd() != null) {
            // 时间跨度不能超过三个月
            LocalDateTime localDateTime = bo.getCreateTimeEnd().minusMonths(3);
            ValidateUtils.isTrue(bo.getCreateTimeStart().isAfter(localDateTime), "时间跨度不能超过三个月");
        }
        // 需要手机号关联查询，所以使用xml
        IPage<OrderVo> result = baseMapper.queryOrderPageList(bo, pageQuery.build());
        return TableDataInfo.build(result);
    }

    /**
     * 获取客户端订单列表
     *
     * @param userId    用户id
     * @param status    订单状态
     * @param pageQuery 分页参数
     * @return 订单列表
     */
    @Override
    public TableDataInfo<ClientOrderVo> clientOrderPageList(Long userId, Integer status, PageQuery pageQuery) {
        // 获取用户下的订单
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getUserId, userId);
        if (status != null) {
            wrapper.eq(Order::getStatus, status);
        }
        // 最大只能查询三个月内的数据
        String date = LocalDate.now().minusMonths(3).toString();
        wrapper.ge(Order::getCreateTime, date);
        wrapper.orderByDesc(Order::getCreateTime);
        IPage<OrderVo> orderVoPage = baseMapper.selectVoPage(pageQuery.build(), wrapper);
        TableDataInfo<OrderVo> tableDataInfo = TableDataInfo.build(orderVoPage);

        // 获取总记录数
        TableDataInfo<ClientOrderVo> tableInfo = new TableDataInfo<>();
        tableInfo.setTotal(tableDataInfo.getTotal());

        // 遍历订单，获取订单下的车票信息
        List<ClientOrderVo> list = new ArrayList<>();
        tableDataInfo.getRows().forEach(orderVo -> {
            ClientOrderVo clientOrderVo = new ClientOrderVo();
            // 拷贝订单基本信息
            BeanUtil.copyProperties(orderVo, clientOrderVo);

            if (orderVo.getExtraInfo() instanceof Order.WaitlistExtra
                && ((Order.WaitlistExtra) orderVo.getExtraInfo()).getWaitlistStatus() < OrderWaitlistStatusEnum.WAITLISTED.getStatus()
            ){
                // 获取候补车票信息
                List<OrderWaitlistTicketVo> orderWaitlistTicketVos = orderWaitlistTicketService.queryList(orderVo.getId());
                clientOrderVo.setWaitlistTicketList(orderWaitlistTicketVos);
            } else {
                // 获取订单下的车票信息
                List<OrderTicketVo> orderTicketList = orderTicketService.getOrderTicketByOrderId(orderVo.getId());
                clientOrderVo.setOrderTicketList(orderTicketList);
            }
            list.add(clientOrderVo);
        });
        tableInfo.setRows(list);
        return tableInfo;
    }

    /**
     * 获取订单详情
     *
     * @param userId  用户id
     * @param orderId 订单id
     * @return 订单详情
     */
    @Override
    public ClientOrderVo getOrderDetail(Long userId, Long orderId) {
        Order order = getSelf().getOrder(userId, orderId);

        // 获取订单基础信息
        ClientOrderVo clientOrderVo = new ClientOrderVo();
        BeanUtil.copyProperties(order, clientOrderVo);

        List<OrderTicketVo> orderTicketList = new ArrayList<>();
        if (order.getExtraInfo() instanceof Order.WaitlistExtra
            && ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistStatus() < OrderWaitlistStatusEnum.WAITLISTED.getStatus()
        ){
            // 获取候补车票信息
            List<OrderWaitlistTicketVo> orderWaitlistTicketVos = orderWaitlistTicketService.queryList(orderId);
            List<OrderTicketVo> orderTicketVos = BeanUtil.copyToList(orderWaitlistTicketVos, OrderTicketVo.class);
            orderTicketList.addAll(orderTicketVos);
        } else {
            // 获取车票信息
            orderTicketList = orderTicketService.getOrderTicketByOrderId(orderId);
        }
        List<TicketInfoVo> ticketInfoVoList = new ArrayList<>();
        orderTicketList.forEach(orderTicketVo -> {
            TicketInfoVo ticketInfoVo = new TicketInfoVo();
            BeanUtil.copyProperties(orderTicketVo, ticketInfoVo);

            if (ObjectUtil.isNotNull(orderTicketVo.getDepartPlanId())) {
                Long lineTemplateId =  remoteDepartPlanService.getTemplateIdByDepartPlanId(orderTicketVo.getDepartPlanId());
                if (lineTemplateId != null) {
                    ticketInfoVo.setLineTemplateName(remoteLineTemplateService.selectNameById(lineTemplateId));
                }
            }

            ticketInfoVoList.add(ticketInfoVo);
        });
        clientOrderVo.setTicketInfoList(ticketInfoVoList);
        return clientOrderVo;
    }

    @Override
    public ClientRefundTicketInfoVo getRefundTicketInfo(Long userId, Long orderId) {
        // 1. 获取ticket
        Order order = getSelf().getOrder(userId, orderId);
        ClientRefundTicketInfoVo refundVo = new ClientRefundTicketInfoVo();
        refundVo.setOrderId(order.getId());
        refundVo.setWaitlistFlag(false);

        var refundList = new ArrayList<ClientRefundTicketInfoVo.TicketRefund>();

        List<OrderTicketVo> orderTicketList = new ArrayList<>();
        if (order.getExtraInfo() instanceof Order.WaitlistExtra
            && ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistStatus() < OrderWaitlistStatusEnum.WAITLISTED.getStatus()
        ) {
            // 获取候补车票信息
            List<OrderWaitlistTicketVo> orderWaitlistTicketVos = orderWaitlistTicketService.queryList(orderId);
            List<OrderTicketVo> orderTicketVos = BeanUtil.copyToList(orderWaitlistTicketVos, OrderTicketVo.class);
            orderTicketList.addAll(orderTicketVos);

            // 如果订单候补中则需要全部退票
            if (OrderWaitlistStatusEnum.WAIT.getStatus()
                .equals(((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistStatus())){
                refundVo.setWaitlistFlag(true);
            }

        } else {
            // 获取车票信息
            orderTicketList = orderTicketService.getOrderTicketByOrderId(orderId);
        }

        orderTicketList.forEach(item -> {
            var ticket = new ClientRefundTicketInfoVo.TicketRefund();
            BeanUtil.copyProperties(item, ticket);

            // 状态必须是未售后
            if (!OrderItemRefundStatusEnum.NONE.getType().equals(item.getRefundStatus())) {
                return;
            }

            ticket.setTicketId(item.getId());
            ticket.setRefundFee(BigDecimal.ZERO);

            // 将dateAt和timeAt合起来就是完整的时间
            var dateAt = DateUtil.format(item.getDateAt(), DateUtils.YYYY_MM_DD);
            ticket.setDateAt(dateAt);

            var timeAt = DateUtil.format(item.getTimeAt(), "HH:mm:ss");
            ticket.setTimeAt(timeAt);

            var dateTime = DateUtil.parse(dateAt + " " + timeAt, DateUtils.YYYY_MM_DD_HH_MM_SS);
            // 当前时间大于车票上车时间
            if (System.currentTimeMillis() > dateTime.getTime()) {
                ticket.setCanRefund(false);
                ticket.setRefusingReason("已过退票时间");
            } else {
                ticket.setCanRefund(true);
                // 获取小程序手续费开关设置
                String serverChargeString = remoteConfigService.selectConfigByKey(ClientConfigConstant.CLIENT_SERVER_CHARGE_FLAG);
                if ("true".equals(serverChargeString) && !refundVo.getWaitlistFlag()) {
                    // 获取退款规则,根据发车时间判断是否可以购票以及手续费
                    List<RefundRulesVo> refundRules = refundRulesService.listRefundRule();
                    BigDecimal serviceChargePrice = calcServiceCharge(item, refundRules);
                    ticket.setRefundFee(serviceChargePrice);
                } else {
                    ticket.setRefundFee(BigDecimal.ZERO);
                }
            }

            refundList.add(ticket);
        });

        refundVo.setTicketRefundList(refundList);
        return refundVo;
    }

    /**
     * 撤销现场补票订单
     */
    @Override
    public void cancelPostPayOrder(CancelPostPayOrderBo bo) {
        Order order = getSelf().getOrder(bo.getOrderId());
        ValidateUtils.notNull(order, "订单不存在");
        ValidateUtils.isTrue(OrderTypeEnum.TICKET_POST_PAY.getType().equals(order.getType()), "只能撤销现场补票订单");
        ValidateUtils.isTrue(OrderStatusEnum.UNPAID.getType().equals(order.getStatus()), "只能撤销待支付的订单");
        // 将撤销原因保存到extraInfo中
        Order.TicketPostPayExtra extraInfo = (Order.TicketPostPayExtra)order.getExtraInfo();
        extraInfo.setReason(bo.getReason());
        // 取消订单
        getSelf().cancelOrder(order, OrderCancelTypeEnum.ADMIN_CANCEL);
    }



    /**
     * 计算refundTicket手续费
     */
    private BigDecimal calcServiceCharge(OrderTicketVo ticket,List<RefundRulesVo> refundRules) {

        // 获取当前的时间戳
        long nowTimestamp = System.currentTimeMillis();

        // 将dateAt和timeAt合起来就是完整的时间
        var dateAt = DateUtil.format(ticket.getDateAt(), DateUtils.YYYY_MM_DD);
        var timeAt = DateUtil.format(ticket.getTimeAt(), "HH:mm:ss");
        var dateTime = DateUtil.parse(dateAt + " " + timeAt, DateUtils.YYYY_MM_DD_HH_MM_SS);

        // 当前时间减去发车时间的时间戳
        long ticketTimestamp = dateTime.getTime() - nowTimestamp;

        //获取当前车票的退款规则
        RefundRulesVo refundRule = refundRules.stream()
            .filter(rule -> ticketTimestamp <= rule.getRefundTimestamp())
            .findFirst()
            .orElse(null);

        BigDecimal serviceChargePrice = BigDecimal.ZERO;

        // 获取手续费
        if (refundRule != null) {
            serviceChargePrice = ticket.getPayPrice().multiply(refundRule.getServiceCharge()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);

            // 手续费向下只留2位小数
            serviceChargePrice = serviceChargePrice.setScale(2, RoundingMode.DOWN );

            // 如果手续费小于0.01则手续费为0
            if (serviceChargePrice.compareTo(BigDecimal.valueOf(0.01)) < 0) {
                serviceChargePrice = BigDecimal.ZERO;
            }
        }

        // 返回本次服务费
        return serviceChargePrice;
    }


    /**
     * 【系统】更新订单退款成功
     *  已根据票来退
     */
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = OrderCacheConstant.ID, key = "#order.getId()")
    @Override
    public void updateOrderRefund(Order order, Refund refund, List<Long> ticketIds) {
        ValidateUtils.notNull(order, "订单不存在");

        // 修改选中的车票状态，要在查询车票状态之前执行
        orderTicketService.batchUpdateTicketRefund(ticketIds);

        // 查询订单下的车票是否全部退款
        Boolean allTicketRefund = orderTicketService.isAllTicketRefund(order.getId());

        // 更新订单退款金额
        BigDecimal refundAmount = order.getRefundPrice().add(refund.getRefundAmount());

        if (allTicketRefund){
            var count = baseMapper.update(Wrappers.<Order>lambdaUpdate()
                .eq(Order::getId, order.getId())
                .set(Order::getStatus, OrderStatusEnum.CANCELED.getType())
                .set(Order::getCancelType, OrderCancelTypeEnum.REFUND_CLOSE.getType())
                .set(Order::getRefundStatus, OrderRefundStatusEnum.ALL.getType())
                .set(Order::getRefundPrice, refundAmount)
                .set(Order::getCancelTime, LocalDateTime.now()));
            ValidateUtils.isTrue(count == 1, "更新订单状态失败");

            // 将order的数据更新
            order.setStatus(OrderStatusEnum.CANCELED.getType());
            order.setCancelType(OrderCancelTypeEnum.REFUND_CLOSE.getType());
            order.setRefundStatus(OrderRefundStatusEnum.ALL.getType());
            order.setRefundPrice(refundAmount);

        } else {
            // 修改订单状态为部分退款
            var count = baseMapper.update(Wrappers.<Order>lambdaUpdate()
                .eq(Order::getId, order.getId())
                .set(Order::getRefundPrice, refundAmount)
                .set(Order::getRefundStatus, OrderRefundStatusEnum.PART.getType()));
            ValidateUtils.isTrue(count == 1, "更新订单状态失败");
        }
    }

    /**
     *  对候补订单和车票进行退款
     */
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = OrderCacheConstant.ID, key = "#order.getId()")
    @Override
    public void updateWaitlistOrderRefund(Order order, Refund refund, List<Long> waitlistTicketIds) {
        // 将候补车票状态更新为已退款
        orderWaitlistTicketService.batchWaitlistTicketRefund(waitlistTicketIds);

        // 候补车票只能整单退款
        var count = baseMapper.update(Wrappers.<Order>lambdaUpdate()
            .eq(Order::getId, order.getId())
            .set(Order::getStatus, OrderStatusEnum.CANCELED.getType())
            .set(Order::getCancelType, OrderCancelTypeEnum.REFUND_CLOSE.getType())
            .set(Order::getRefundStatus, OrderRefundStatusEnum.ALL.getType())
            .set(Order::getRefundPrice, order.getPayPrice())
            .set(Order::getCancelTime, LocalDateTime.now())
            .setSql("extra_info = JSON_SET(extra_info, '$.waitlistStatus', {0})", OrderWaitlistStatusEnum.CANCELLED.getStatus()));
        ValidateUtils.isTrue(count == 1, "更新订单状态失败");

        // 将order的数据更新
        order.setStatus(OrderStatusEnum.CANCELED.getType());
        order.setCancelType(OrderCancelTypeEnum.REFUND_CLOSE.getType());
        order.setRefundStatus(OrderRefundStatusEnum.ALL.getType());
        order.setRefundPrice(order.getPayPrice());
    }

    /**
     * 创建后支付订单
     * @param bo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @OrderLog(operateType = OrderOperateTypeEnum.MEMBER_CREATE)
    public Long createPostPayOrderTicket(PostPayOrderTicketBo bo) {
        // 1、校验 用户是否有为支付的后付款订单
        ClientHasPostPayVo hasPostPayOrderByUserId = hasPostPayOrderByUserId(bo.getUserId());
        ValidateUtils.isFalse(hasPostPayOrderByUserId.getIsExist(), "该用户已有待支付的补票订单");
        // 2、构建后支付信息
        buildPostPayInfo(bo);
        // 3、校验信息
        List<RemoteBusDepartPlan> planList = validatePostPayOrder(bo);
        // 4、计算价格
        OrderPriceVo price = OrderPriceVo.builder()
            .totalPrice(bo.getTotalPrice())
            .payPrice(bo.getPayPrice())
            // 后支付订单暂不支持优惠券
            .couponPrice(BigDecimal.ZERO)
            .discountPrice(bo.getTotalPrice().subtract(bo.getPayPrice()))
            .build();

        // 5、创建订单
        var clientOrderTicketBo = BeanUtil.copyProperties(bo, ClientOrderTicketBo.class);

        Order order = createOrder(clientOrderTicketBo, price, false);
        var orderRes = baseMapper.insert(order);
        ValidateUtils.isFalse(orderRes == 0, "创建订单失败");
        // 6、创建行程
        var ticketList = buildOrderTicketItem(order, planList, clientOrderTicketBo, false);
        orderTicketService.createOrderTickets(ticketList);
        // 7. 生成预支付
        var payOrderId = payOrderService.createOrder(order);
        order.setPayOrderId(payOrderId);
        baseMapper.update(Wrappers.lambdaUpdate(Order.class)
            .set(Order::getPayOrderId, payOrderId)
            .eq(Order::getId, order.getId())
        );
        return order.getId();
    }

    private List<RemoteBusDepartPlan> validatePostPayOrder(PostPayOrderTicketBo bo) {
        ValidateUtils.notNull(bo.getPassengers(), "乘车人不能为空");
        List<RemoteBusDepartPlan> planList = new ArrayList<>();
        //判断选择的站点是否在计划站点中
        bo.getPlans().forEach(planItem -> {
            RemoteBusDepartPlan departPlan = remoteDepartPlanService.getDepartPlanById(planItem.getPlanId());
            // 校验站点信息
            validatePlan(planItem);
            // 增加座位数
            remoteDepartPlanStationService.checkPlanAndStationLimit(planItem.getPlanId(), planItem.getUpStationId(), bo.getPassengers().size(), false);
            // 校验排班
            remoteLineScheduleService.validateTicket(planItem.getScheduleId(), planItem.getUpStationId(), bo.getPassengers().size(), false);
            planList.add(departPlan);
        });

        return planList;
    }

    /**
     * 校验行程 站点信息是否存在于计划 排班中
     * @param planItem
     */
    private void validatePlan(ClientOrderTicketBo.ClientOrderTicketPlanBo planItem) {
        List<RemoteBusDepartPlanStationVo> planStationList = remoteDepartPlanService.getDepartPlanStationIds(planItem.getPlanId());
        ValidateUtils.notNull(planStationList, "班次站点不存在");
        // 找到集合中对应的上下车站点
        RemoteBusDepartPlanStationVo upStation = planStationList.stream().filter(item -> item.getStationId().equals(planItem.getUpStationId())).findFirst().orElse(null);
        RemoteBusDepartPlanStationVo downStation = planStationList.stream().filter(item -> item.getStationId().equals(planItem.getDownStationId())).findFirst().orElse(null);
        //判断票数是否超过站点设置限制
        ValidateUtils.notNull(upStation, "上车站点不在班次中");
        ValidateUtils.notNull(downStation, "下车站点不在班次中");
        if (planItem.getScheduleId() != null) {
            List<RemoteBusLineScheduleStationVo> scheduleStation = remoteLineScheduleService.getScheduleStation(planItem.getScheduleId());
            ValidateUtils.notNull(scheduleStation, "排班站点不存在");
            // 找到集合中对应的上下车站点
            RemoteBusLineScheduleStationVo upScheduleStation = scheduleStation.stream().filter(item -> item.getStationId().equals(planItem.getUpStationId())).findFirst().orElse(null);
            RemoteBusLineScheduleStationVo downScheduleStation = scheduleStation.stream().filter(item -> item.getStationId().equals(planItem.getDownStationId())).findFirst().orElse(null);
            ValidateUtils.notNull(upScheduleStation, "上车站点不在排班中");
            ValidateUtils.notNull(downScheduleStation, "下车站点不在排班中");
        }
    }

    /**
     * 该用户是否有补票订单
     */
    @Override
    public ClientHasPostPayVo hasPostPayOrderByUserId(Long userId) {
        var order = baseMapper.selectOne(Wrappers.lambdaQuery(Order.class)
            .select(Order::getId)
            .eq(Order::getUserId, userId)
            .eq(Order::getType, OrderTypeEnum.TICKET_POST_PAY.getType())
            .eq(Order::getStatus, OrderStatusEnum.UNPAID.getType())
            .eq(Order::getPayStatus, false)
            .last("limit 1")
        );
        return ClientHasPostPayVo.builder()
            .isExist(order != null)
            .orderId(order == null ? null : order.getId())
            .build();
    }


    /**
     * 构建后支付信息
     * @param bo
     */
    private void buildPostPayInfo(PostPayOrderTicketBo bo) {
        // 该排班是否是跨线排班
        ValidateUtils.notNull(bo.getPlans(), "行程不能为空");
        ValidateUtils.notNull(bo.getPassengers(), "乘客信息不能为空");
        //todo 现场补票目前只能补一个排班
        ClientOrderTicketBo.ClientOrderTicketPlanBo first = bo.getPlans().getFirst();
        ValidateUtils.notNull(first, "购票信息不能为空");
        RemoteBusLineScheduleVo remoteBusLineScheduleVo = remoteLineScheduleService.queryById(first.getScheduleId());
        ValidateUtils.notNull(remoteBusLineScheduleVo, "排班信息不存在");
        // 该排班是否是跨线排班
        if (remoteBusLineScheduleVo.getCrossLineFlag()) {
            // 根据方向获取学校站点
            Long schoolStationId = "1".equals(remoteBusLineScheduleVo.getLineKind())
                ? first.getDownStationId()
                : first.getUpStationId();
            // 获取该站点的学校id
            RemoteBusStationVo remoteBusStationVo = remoteStationService.selectById(schoolStationId);
            ValidateUtils.notNull(remoteBusStationVo, "站点信息不存在");
            // 是否为学校站点
            ValidateUtils.isTrue("1".equals(remoteBusStationVo.getSchoolFlag()), "学校站点错误");
            Long schoolId = remoteBusStationVo.getSchoolId();
            // 获取该排班中该学校的所有行程
            OrderTicketBo orderTicketBo = new OrderTicketBo();
            orderTicketBo.setScheduleId(first.getScheduleId());
            orderTicketBo.setSchoolId(schoolId);
            List<OrderTicketVo> orderTicketVos = orderTicketService.queryList(orderTicketBo);
            // 找到符合的计划
            HashSet<Long> accordPlan = new HashSet<>();
            orderTicketVos.forEach(orderTicketVo -> {
                //找出上下车站点和学校完全相同的行程
                if (first.getUpStationId().equals(orderTicketVo.getFromId()) && first.getDownStationId().equals(orderTicketVo.getToId())
                    && schoolId.equals(orderTicketVo.getSchoolId())) {
                    accordPlan.add(orderTicketVo.getDepartPlanId());
                }
            });
            // 是否有符合的计划
            ValidateUtils.isTrue(!accordPlan.isEmpty(), "所选学校下的计划无符合的站点");
            // 只有一个符合的计划
            if (accordPlan.size() == 1) {
                first.setPlanId(accordPlan.iterator().next());
                postPayInfoSetPlanInfo(bo, remoteBusLineScheduleVo);
            } else {
                // 有多个符合的计划
                // 找出最优惠的计划
                RemoteBusDepartPlan plan = null;
                for (Long planId : accordPlan) {
                    RemoteBusDepartPlan departPlan = remoteDepartPlanService.getDepartPlanById(planId);
                    // 找出最优惠的计划
                    if (plan == null || departPlan.getDiscountPrice().compareTo(plan.getDiscountPrice()) < 0) {
                        plan = departPlan;
                    }
                }
                ValidateUtils.notNull(plan, "计划信息不存在");
                first.setPlanId(plan.getId());
                postPayInfoSetPlanInfo(bo, remoteBusLineScheduleVo);
            }
        }else {
            // 不是跨线排班 计划id就是排班中的计划id
            first.setPlanId(remoteBusLineScheduleVo.getDepartPlanId());
            postPayInfoSetPlanInfo(bo, remoteBusLineScheduleVo);
        }
    }

    /**
     * 后支付信息设置计划信息
     * @param bo
     * @param remoteBusLineScheduleVo
     */
    private void postPayInfoSetPlanInfo(PostPayOrderTicketBo bo, RemoteBusLineScheduleVo remoteBusLineScheduleVo) {
        ClientOrderTicketBo.ClientOrderTicketPlanBo first = bo.getPlans().getFirst();
        RemoteBusDepartPlan plan = remoteDepartPlanService.getDepartPlanById(first.getPlanId());
        ValidateUtils.notNull(plan, "计划信息不存在");
        // 计划价格*购票人数
        bo.setTotalPrice(plan.getPrice().multiply(BigDecimal.valueOf(bo.getPassengers().size())));
        bo.setPayPrice(plan.getDiscountPrice().multiply(BigDecimal.valueOf(bo.getPassengers().size())));
        bo.getTicketPostPayBo().setVehicleId(remoteBusLineScheduleVo.getVehicleId());
        String licensePlate = remoteVehicleService.selectLicensePlateById(remoteBusLineScheduleVo.getVehicleId());
        bo.getTicketPostPayBo().setLicensePlate(licensePlate);

    }

    /**
     * 订单校验
     *
     * @param bo 订单创建信息
     * @return 选择的发车计划
     */
    private List<RemoteBusDepartPlan> validateOrder(ClientOrderTicketBo bo) {
        ValidateUtils.notNull(bo.getPlans(), "行程不能为空");
        var planList = new ArrayList<RemoteBusDepartPlan>();

        // 获取第一个 plan 的 waitListFlag 作为基准
        Boolean waitListFlag = bo.getPlans().getFirst().getWaitListFlag();

        // 校验列表中其他所有的 waitListFlag 是否都和 waitListFlag 相同
        boolean allMatch = bo.getPlans().stream()
            .allMatch(plan -> plan.getWaitListFlag().equals(waitListFlag));

        ValidateUtils.isTrue(allMatch, "不能同时购买候补和正常车票");

        if (waitListFlag) {
            ValidateUtils.isTrue(bo.getPlans().size() == 1, "候补车票只能购买一个");
        }

        bo.getPlans().forEach(planItem -> {
            // 上下车站点不能是同一个
            ValidateUtils.notEquals(planItem.getUpStationId(), planItem.getDownStationId(), "上下车站点不能相同");

            var plan = remoteDepartPlanService.getDepartPlanById(planItem.getPlanId());
            ValidateUtils.notNull(plan, "班次不存在");

            // 获取计划的发车时间
            var fullTime = DateUtil.parse(
                DateUtil.format(plan.getDateAt(), DateUtils.YYYY_MM_DD) + " " + plan.getTimeAt(),
                DateUtils.YYYY_MM_DD_HH_MM_SS);

            // 购买计划票
            if (planItem.getScheduleId() == null) {
                // 发车计划校验
                ValidateUtils.isFalse("0".equals(plan.getStatus()), "班次已停售");

                // todo 按客户需求首站发车前15分钟停止售票
                String stopSellKey = "bus.stop.sell";
                int stopBuyTicketTime = Integer.parseInt(remoteConfigService.selectConfigByKey(stopSellKey));
                ValidateUtils.isTrue(fullTime.getTime() - System.currentTimeMillis() > (long) stopBuyTicketTime * 60 * 1000,
                    "已截止买票");

            }else {
                // 排班校验
                var schedule = remoteLineScheduleService.queryById(planItem.getScheduleId());
                ValidateUtils.notNull(schedule, "排班不存在");
            }

            // 保底时间校验
            ValidateUtils.isTrue(DateUtil.compare(new Date(), fullTime, DateUtils.YYYY_MM_DD_HH_MM_SS) <= 0, "已过购票时间");

            planList.add(plan);
        });

        //判断选择的站点是否在计划站点中
        bo.getPlans().forEach(this::validatePlan);

        ValidateUtils.notNull(bo.getPassengers(), "乘车人不能为空");

        if (!waitListFlag) {
            //  校验发车计划和计划站点是否超过上架限制
            bo.getPlans().forEach(planItem -> {
                remoteDepartPlanStationService.checkPlanAndStationLimit(planItem.getPlanId(), planItem.getUpStationId(), bo.getPassengers().size(), true);
                if (planItem.getScheduleId() != null) {
                    // 校验排班
                    remoteLineScheduleService.validateTicket(planItem.getScheduleId(), planItem.getUpStationId(), bo.getPassengers().size(), true);
                }
            });
        }

        return planList;
    }

    /**
     * 构建订单
     *
     * @param order 订单
     */
    private void buildOrder(Order order) {
        order.setId(null);
        order.setStatus(OrderStatusEnum.UNPAID.getType());
        order.setCreateTime(new Date());

        // 支付信息
        // 强制为微信小程序支付
        order.setPayChannelCode(PayChannelEnum.WX_LITE.getCode());
        order.setPayStatus(false);

        // 售后信息
        order.setRefundPrice(BigDecimal.ZERO);
        order.setRefundStatus(RefundStatusEnum.NONE.getType());
    }

    /**
     * 构建订单车票
     */
    private List<OrderTicketBo> buildOrderTicketItem(Order order, List<RemoteBusDepartPlan> planList,
                                                     ClientOrderTicketBo clientOrderTicketBo,
                                                     boolean waitListFlag) {
        var ticketList = new ArrayList<OrderTicketBo>();

        // 从订单获取总的支付金额和优惠券金额
        var payPrice = order.getPayPrice();
        var couponPrice = order.getCouponPrice();
        var passengerList = clientOrderTicketBo.getPassengers();

        // 计算总的车票价格（所有计划和乘客的折扣价格之和）
        BigDecimal totalTicketPrice = BigDecimal.ZERO;
        for (var planItem : planList) {
            BigDecimal planPrice = planItem.getDiscountPrice();
            for (var passenger : passengerList) {
                totalTicketPrice = totalTicketPrice.add(planPrice);
            }
        }

        // 初始化累积的支付金额和优惠券金额
        BigDecimal cumulativePayPrice = BigDecimal.ZERO;
        BigDecimal cumulativeCouponPrice = BigDecimal.ZERO;

        // 计算总的车票数量
        int totalTickets = planList.size() * passengerList.size();
        int ticketCount = 0;

        // 标记是否订单支付金额为0.01元
        boolean isTotalPayPriceOneCent = payPrice.compareTo(BigDecimal.valueOf(0.01)) == 0;
        var planTickers = clientOrderTicketBo.getPlans();


        // 获取计划id和对应的实体
        Map<Long, ClientOrderTicketBo.ClientOrderTicketPlanBo> planOrderMap = planTickers.stream()
            .collect(Collectors.toMap(ClientOrderTicketBo.ClientOrderTicketPlanBo::getPlanId, item -> item));

        for (var planItem : planList) {

            // 在plans中根据planId拿到ticket的上下车站点Id
            Long upStationId = planTickers.stream()
                .filter(item -> item.getPlanId().equals(planItem.getId()))
                .findFirst()
                .map(ClientOrderTicketBo.ClientOrderTicketPlanBo::getUpStationId)
                .orElse(null);

            Long downStationId = planTickers.stream()
                .filter(item -> item.getPlanId().equals(planItem.getId()))
                .findFirst()
                .map(ClientOrderTicketBo.ClientOrderTicketPlanBo::getDownStationId)
                .orElse(null);

            ValidateUtils.notNull(upStationId, "数据异常，行程上车点不能为空");
            ValidateUtils.notNull(downStationId, "数据异常，行程下车点不能为空");

            // 获取计划站点到达时间
            var timeAt = remoteDepartPlanStationService.getPlanStationByPlanIdAndStationId(
                planItem.getId(), upStationId).getArrivalTime();

            BigDecimal planPrice = planItem.getDiscountPrice();
            Boolean ticketPostPayFlag = clientOrderTicketBo.getTicketPostPayBo().getTicketPostPayFlag();

            for (var passenger : passengerList) {
                ticketCount++;
                var orderTicketBo = new OrderTicketBo();

                // 获取对应的计划信息
                ClientOrderTicketBo.ClientOrderTicketPlanBo clientOrderTicketPlanBo = planOrderMap.get(planItem.getId());
                // 判断前端传回的时间是否正确
                ValidateUtils.notNull(clientOrderTicketPlanBo, "数据异常，行程信息不能为空");



                // 订单信息
                orderTicketBo.setOrderId(order.getId());
                orderTicketBo.setUserId(order.getUserId());
                orderTicketBo.setRefundStatus(RefundStatusEnum.NONE.getType());

                // 乘车人信息
                orderTicketBo.setPassengerName(passenger.getName());
                orderTicketBo.setPassengerMobile(passenger.getMobile());
                orderTicketBo.setValidCode(passenger.getValidCode());
                orderTicketBo.setBackDate(passenger.getBackDate());

                // 车票信息
                orderTicketBo.setStatus(TicketStatusEnum.NOT_SCHEDULED.getCode());
                orderTicketBo.setDateAt(planItem.getDateAt());
                orderTicketBo.setTimeAt(timeAt);
                orderTicketBo.setFromId(upStationId);
                orderTicketBo.setToId(downStationId);
                orderTicketBo.setSchoolId(planItem.getSchoolId());
                orderTicketBo.setLineKind(planItem.getLineKind());
                orderTicketBo.setDepartPlanId(planItem.getId());
                orderTicketBo.setPrice(planItem.getPrice());
                orderTicketBo.setPostPayFlag(ticketPostPayFlag);

                // 计划票和候补票的时间校验 ·拿计划时间校验
                if (clientOrderTicketPlanBo.getScheduleId() == null || waitListFlag){
                    ValidateUtils.isTrue(timeAt.equals(clientOrderTicketPlanBo.getTimeAt()), "班次发生变更，请重新下单");
                }
                // 排班票且不是候补票 拿前端传回的时间与排班的时间进行校验
                if (clientOrderTicketPlanBo.getScheduleId() != null && !waitListFlag) {
                    // 排班时间拿前端传回来的时间和排班站点时间做校验，防止跨线排班时间不一致
                    orderTicketBo.setTimeAt(clientOrderTicketPlanBo.getTimeAt());
                    buildScheduleInfo(orderTicketBo, clientOrderTicketPlanBo.getScheduleId());
                }
                // 补票行程状态
                if (ticketPostPayFlag) {
                    orderTicketBo.setStatus(TicketStatusEnum.VERIFIED.getCode());
                    orderTicketBo.setBoardingTime(new Date());
                    orderTicketBo.setActualVehicleId(clientOrderTicketBo.getTicketPostPayBo().getVehicleId());
                    orderTicketBo.setActualLicensePlate(clientOrderTicketBo.getTicketPostPayBo().getLicensePlate());
                    orderTicketBo.setVerifyScheduleId(clientOrderTicketPlanBo.getScheduleId());
                }

                BigDecimal ticketCouponPrice;
                BigDecimal ticketPayPrice;

                if (isTotalPayPriceOneCent) {
                    // 如果订单支付金额为0.01元
                    if (ticketCount == 1) {
                        // 第一个行程的支付金额为0.01元，优惠券金额为票价减去0.01元
                        ticketPayPrice = BigDecimal.valueOf(0.01);
                        ticketCouponPrice = planPrice.subtract(ticketPayPrice).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        // 其他行程的支付金额为0元，优惠券金额为票价
                        ticketPayPrice = BigDecimal.ZERO;
                        ticketCouponPrice = planPrice;
                    }
                } else {
                    // 正常按比例分配支付金额和优惠券金额

                    // 计算该车票占总车票价格的比例
                    BigDecimal proportion = planPrice.divide(totalTicketPrice, 10, RoundingMode.HALF_UP);

                    // 计算车票的优惠券金额
                    ticketCouponPrice = couponPrice
                        .multiply(proportion).setScale(2, RoundingMode.HALF_UP);

                    // 计算车票的支付金额
                    ticketPayPrice = planPrice.subtract(ticketCouponPrice).setScale(2, RoundingMode.HALF_UP);

                    // 处理舍入误差，调整最后一张车票的金额
                    if (ticketCount == totalTickets) {
                        // 调整最后一张车票的优惠券和支付金额
                        ticketCouponPrice = couponPrice.subtract(cumulativeCouponPrice);
                        ticketPayPrice = payPrice.subtract(cumulativePayPrice);
                    } else {
                        cumulativeCouponPrice = cumulativeCouponPrice.add(ticketCouponPrice);
                        cumulativePayPrice = cumulativePayPrice.add(ticketPayPrice);
                    }
                }

                // 设置计算后的金额
                orderTicketBo.setCouponPrice(ticketCouponPrice);
                orderTicketBo.setPayPrice(ticketPayPrice);

                ticketList.add(orderTicketBo);
            }
        }
        return ticketList;
    }

    /**
     * 补充排班信息
     */
    private void buildScheduleInfo(OrderTicketBo bo, Long scheduleId) {
        var schedule = remoteLineScheduleService.queryById(scheduleId);
        // 获取排班站点列表
        var scheduleStation = remoteLineScheduleService.getScheduleStation(scheduleId);
        // 获取上车站点
        var upStation = scheduleStation.stream()
            .filter(item -> item.getStationId().equals(bo.getFromId()))
            .findFirst()
            .orElse(null);

        ValidateUtils.notNull(upStation, "上车站点不存在");
        // 如果是现场补票那么不需要校验时间 将上车站点时间设置为发车时间，防止排班为跨线排班
        if (bo.getPostPayFlag()) {
            bo.setTimeAt(upStation.getArrivalTime());
        }
        ValidateUtils.isTrue(upStation.getArrivalTime().equals(bo.getTimeAt()), "班次发生变更，请重新下单");
        if (schedule != null) {
            bo.setScheduleId(scheduleId);
            bo.setStatus(TicketStatusEnum.PENDING_VERIFICATION.getCode());
            bo.setVehicleId(schedule.getVehicleId());
            bo.setDriverId(schedule.getDriverId());
            bo.setLicensePlate(remoteVehicleService.selectLicensePlateById(schedule.getVehicleId()));
            bo.setCrossLineFlag(schedule.getCrossLineFlag());
        }
    }





    /**
     * 检查订单是否可支付
     *
     * @param order    订单
     * @param payOrder 支付订单
     */
    private void validOrderPayable(Order order, PayOrder payOrder) {
        // 校验订单是否存在
        ValidateUtils.notNull(order, "订单不存在");
        // 校验订单未支付
        ValidateUtils.isTrue(order.getStatus().equals(OrderStatusEnum.UNPAID.getType()), "订单状态不正确");
        // 检验订单中的支付单和传入的支付单是否一致
        ValidateUtils.equals(order.getPayOrderId(), payOrder.getId(), "支付单不正确");
    }

    /**
     * 判断订单是否为候补车票类型
     *
     * @param order 订单对象
     * @return 是否为候补订单
     */
    private boolean isWaitlistOrder(Order order) {
        return order.getExtraInfo() instanceof Order.WaitlistExtra &&
            ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistFlag();
    }

    /**
     * 构建订单对象
     */
    private Order createOrder(ClientOrderTicketBo bo, OrderPriceVo price, Boolean waitListFlag) {
        var order = new Order();
        BeanUtil.copyProperties(bo, order);
        order.setPayPrice(price.getPayPrice());
        order.setCouponPrice(price.getCouponPrice());
        order.setDiscountPrice(price.getDiscountPrice());
        order.setTotalPrice(price.getTotalPrice());
        buildOrder(order);
        order.setType(OrderTypeEnum.TICKET.getType());

        if (Boolean.TRUE.equals(waitListFlag)) {
            // 候补订单类型
            order.setType(OrderTypeEnum.TICKET_WAITLIST.getType());
            // 候补订单额外信息
            var waitlistExtra = new Order.WaitlistExtra();
            waitlistExtra.setWaitlistFlag(true);
            waitlistExtra.setWaitlistStatus(OrderWaitlistStatusEnum.WAIT.getStatus());
            order.setExtraInfo(waitlistExtra);
        } else if  (bo.getTicketPostPayBo().getTicketPostPayFlag()){
            // 现场补票订单
            order.setType(OrderTypeEnum.TICKET_POST_PAY.getType());
            // 现场补票额外信息
            var ticketPostPayExtra = new Order.TicketPostPayExtra();
            ticketPostPayExtra.setDriverId(bo.getTicketPostPayBo().getDriverId());
            ticketPostPayExtra.setParentMobile(bo.getTicketPostPayBo().getParentMobile());
            order.setExtraInfo(ticketPostPayExtra);
        }
        return order;
    }

}
