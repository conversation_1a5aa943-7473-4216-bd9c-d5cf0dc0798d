package yq.mall.service;

import com.baomidou.lock.annotation.Lock4j;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.api.domain.vo.RemoteBusLineScheduleStationVo;
import yq.bus.api.domain.vo.RemoteBusLineScheduleVo;
import yq.mall.api.domain.bo.RemoteScheduleBo;
import yq.mall.constant.MallLockConstant;
import yq.mall.controller.admin.mall.bo.CouponStatisticsQueryBo;
import yq.mall.controller.admin.mall.bo.StationPassengerHeatMapBo;
import yq.mall.controller.admin.mall.bo.SupplierTicketSalesBo;
import yq.mall.controller.admin.mall.bo.TicketSalesBo;
import yq.mall.controller.admin.mall.vo.*;
import yq.mall.controller.client.order.bo.ClientOrderTicketQueryBo;
import yq.mall.controller.client.order.vo.QuickTicketMessageVo;
import yq.mall.domain.Order;
import yq.mall.domain.OrderWaitlistTicket;
import yq.mall.domain.bo.OrderTicketBo;
import yq.mall.domain.bo.StationAndDepartPlanBo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.domain.vo.PlanTicketCountVo;
import yq.mall.domain.vo.TicketCountVo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;


import java.util.List;
import java.util.Map;

/**
 * 行程Service接口
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IOrderTicketService {

    /**
     * 查询行程分页列表
     */
    TableDataInfo<OrderTicketVo> queryPageList(OrderTicketBo bo, PageQuery pageQuery);

    /**
     * 查询行程列表
     */
    List<OrderTicketVo> queryList(OrderTicketBo orderTicketBo);

    /**
     *获取未排班的行程数量
     */
    List<TicketCountVo> getUnSchedulingTicketCount();

    /**
     *查询行程详情
     */
    OrderTicketVo queryById(Long id);

    /**
     *通过站点和发车计划获取未排班的行程列表
     */
    List<OrderTicketVo> getUnSchedulingTicketByStationAndDepartPlan(StationAndDepartPlanBo bo);

    /**
     *批量更新行程
     */
    Boolean updateBatchById(List<OrderTicketBo> orderTicketBos);

    /**
     * 获取家庭的行程详情列表
     */
    TableDataInfo<OrderTicketVo> queryTicketInfoPageList(Long userId , ClientOrderTicketQueryBo bo, PageQuery pageQuery);

    /**
     * 验票
     */
    Boolean checkTicket(Long ticketId, String licensePlate, Long vehicleId, Long scheduleId);


    /**
     * 取消验票
     */
    Boolean cancelCheckTicket(Long ticketId);

    /**
     * 获取乘客列表
     */
    List<OrderTicketVo> getOrderTicketByScheduleID(Long scheduleId);

    /**
     * 创建车票订单
     * @param boList 订单信息
     */
    void createOrderTickets(List<OrderTicketBo> boList);

    /**
     * 根据orderId获取车票集合
     */
    List<OrderTicketVo> getOrderTicketByOrderId(Long orderId);

    /**
     * 取消车票
     * @param orderId 订单ID
     */
    void cancelTicket(Long orderId);

    /**
     * 更新车票支付状态
     * @param orderId 订单ID
     */
    void updateTicketPaid(Long orderId);

    /**
     * 【系统】更新为已退款
     * @param orderId 订单ID
     */
    void updateTicketRefund(Long orderId);

    /**
     * 【系统】更新为已退款
     * @param ticketIds 车票ids集合
     */
    void batchUpdateTicketRefund(List<Long> ticketIds);

    /**
     *  查询订单下的车票是否全退款
     */
    Boolean isAllTicketRefund(Long orderId);

    /**
     * 【系统】查询订单行程
     */
     List<OrderTicketVo> queryOrderTicket(Long orderId);

    /**
     * 排班结束后，更新行程状态为完成
     */
    Boolean finishTripByScheduleId(Long scheduleId);

    /**
     * 完成行程
     */
    Boolean finishTrip(Long id);

    /**
     * 根据用户ID获取车票分页
     */
    TableDataInfo<OrderTicketVo> getTicketByUser(Long userId, PageQuery pageQuery);

    PlanTicketCountVo getPlanTicketCount(Long departPlanId);

    List<OrderTicketVo> queryByPlanId(Long departPlanId);

    TicketSaleDetailsVo getTicketSalesDetail(TicketSalesBo bo);

    QuickTicketMessageVo getQuickTicketMessage(Long passengerId, Long userId);

    /**
     * 将行程状态重置为未排班
     */
    Integer resetTicketStatus(Long ticketId);

    List<CouponStatisticsVo> getCouponStatistics(CouponStatisticsQueryBo bo);

    TodayCountVo getWeeklyCount();

    /**
     * 获取站点热力图数据
     */
    List<StationPassengerHeatMapVo> getStationPassengerHeatMap(StationPassengerHeatMapBo bo);

    void waitlistTicket(Long planId);

    /**
     * 获取排班下的行程数量
     * @param scheduleId
     */
    Long getScheduleTicketCount(Long scheduleId);

    /**
     * 获取供应商售票统计
     */
    SupplierTicketDetailsSalesVo getSupplierTicketSalesDetail(SupplierTicketSalesBo bo);

    /**
     *将候补行程转为行程后直接加入排班
     */
    void waitlistJoinSchedule(List<Long> waitlistIds, RemoteScheduleBo scheduleInfo);

    /**
     * 将候补行程转为行程后直接加入排班并更新行程状态

     */
    void waitlistByScheduleAndUpdateOrder (RemoteBusLineScheduleVo schedule, Map.Entry<Order, List<OrderWaitlistTicket>> entry
        , Long orderId, List<RemoteBusLineScheduleStationVo> scheduleStationList);
}
