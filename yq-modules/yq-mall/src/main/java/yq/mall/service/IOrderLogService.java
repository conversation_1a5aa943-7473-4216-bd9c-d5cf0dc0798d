package yq.mall.service;

import yq.mall.domain.bo.order.OrderLogBo;
import yq.mall.domain.vo.order.OrderLogVo;

import java.util.List;

/**
 * 订单日志服务接口
 * <AUTHOR>
 */
public interface IOrderLogService {

    /**
     * 创建订单日志
     * @param bo 订单日志
     */
    void creatOrderLog(OrderLogBo bo);

    /**
     * 获取订单日志
     * @param orderId 订单ID
     */
    List<OrderLogVo> listOrderLogByOrderId(Long orderId);

}
