package yq.mall.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingReturnV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.result.ProfitSharingReturnV3Result;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.utils.SpringUtils;
import yq.common.core.utils.StringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.config.properties.MallProperties;
import yq.mall.constant.MallLockConstant;
import yq.mall.domain.PayProfitSharing;
import yq.mall.domain.PayProfitSharingReturn;
import yq.mall.domain.PayRefund;
import yq.mall.domain.bo.pay.PayProfitSharingReturnBo;
import yq.mall.domain.vo.pay.PayProfitSharingReturnVo;
import yq.mall.enums.pay.*;
import yq.mall.mapper.PayProfitSharingReturnMapper;
import yq.mall.service.IPayOrderService;
import yq.mall.service.IPayProfitSharingReturnService;
import yq.mall.service.IPayProfitSharingService;
import yq.mall.service.IPayRefundService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 分账退回服务接口实现类
 *
 * <AUTHOR> Suen
 */
@Slf4j
@Service
public class PayProfitSharingReturnServiceImpl implements IPayProfitSharingReturnService {

    @Resource
    private WxPayService wxPayService;

    @Resource
    private MallProperties mallProperties;

    @Resource
    private PayProfitSharingReturnMapper baseMapper;

    @Resource
    private IPayProfitSharingService profitSharingService;

    @Resource
    private IPayRefundService payRefundService;

    @Resource
    private IPayOrderService payOrderService;

    private PayProfitSharingReturnServiceImpl getSelf() {
        return SpringUtils.getBean(getClass());
    }

    /**
     * 分账退回，异步调用，开启新的事务，如果没有分账记录则不做任何操作
     *
     * @param payRefund 支付退款单
     */
    @Async
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#payRefund.payOrderId"})
    @Override
    public void profitSharingReturn(PayRefund payRefund) {

        // 获取分账记录，目前一个支付订单只能有一个分账记录
        var profitSharing = profitSharingService.getByPayOrderId(payRefund.getPayOrderId());

        // 没有分账记录则不做任何操作、或者分账未完成的，不做任何操作
        if (profitSharing == null) {
            return;
        }

        var canProfitString = CollUtil.contains(Arrays.asList(
            PayProfitSharingStatusEnum.FINISHED.getType(),
            PayProfitSharingStatusEnum.IGNORE.getType()
        ), profitSharing.getStatus());
        if (!canProfitString) {
            return;
        }

        var payProfitSharingReturn = getSelf().getByPayRefundId(payRefund.getId());
        if (payProfitSharingReturn != null) {
            // 如果已经有分账退回记录，则重试
            this.retryProfitSharingReturn(payProfitSharingReturn);
            return;
        }

        // 构建分账退回记录
        payProfitSharingReturn = buildPayProfitSharingReturn(payRefund, profitSharing);

        // 如果分账已经忽略，则直接忽略
        if (PayProfitSharingStatusEnum.IGNORE.getType().equals(profitSharing.getStatus())) {
            payProfitSharingReturn.setStatus(PayProfitSharingReturnStatusEnum.IGNORE.getType());
            payProfitSharingReturn.setFinishTime(LocalDateTime.now());

            payProfitSharingReturn.setReturnAmount(BigDecimal.ZERO);

            payProfitSharingReturn.setRemark("分账单状态为忽略，无需退回");
        }

        baseMapper.insert(payProfitSharingReturn);

        // 更新分账单退回金额
        profitSharingService.updateReturnAmount(profitSharing.getId(), payProfitSharingReturn.getReturnAmount());
        // 更新支付退款单状态
        payRefundService.updateProfitSharingReturnStatus(payRefund.getId(), PayRefundProfitSharingStatusEnum.SUCCESS);

        if (!PayProfitSharingReturnStatusEnum.IGNORE.getType().equals(payProfitSharingReturn.getStatus())) {
            if (PayChannelEnum.WX_LITE.getCode().equals(payRefund.getChannelCode())) {
                // TODO 同一笔分账单最多只能发起20次分账退回
                // TODO 分账退回的期限是180天
                this.requestReturnWx(payProfitSharingReturn);
            }
        }
    }

    /**
     * 查询处理中的分账退回的记录
     *
     * @return 处理中的退回的记录
     */
    @Override
    public List<PayProfitSharingReturn> listPending() {
        return baseMapper.selectList(Wrappers.<PayProfitSharingReturn>lambdaQuery()
            .eq(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.PROCESSING.getType()));
    }

    /**
     * 同步微信分账退回记录
     *
     * @param payProfitSharingReturn 分账退回记录
     */
    @Async
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#payProfitSharingReturn.payOrderId"})
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncWx(PayProfitSharingReturn payProfitSharingReturn) {
        try {
            var result = wxPayService.getProfitSharingService().profitSharingReturnQueryV3(
                payProfitSharingReturn.getPayProfitSharingId().toString(),
                payProfitSharingReturn.getId().toString()
            );

            // 如果分账完成，更新分账单状态
            if (WxPayConstants.ResultCode.SUCCESS.equals(result.getResult())) {
                var zdt = ZonedDateTime.parse(result.getFinishTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZZZZZ"));
                this.updateSuccess(
                    payProfitSharingReturn.getId(),
                    result.getReturnId(),
                    LocalDateTime.from(zdt));
            } else if (WxPayConstants.ResultCode.FAIL.equals(result.getResult())) {
                this.updateFail(payProfitSharingReturn.getId(), result.getResult(), payProfitSharingReturn);
            }

        } catch (WxPayException e) {
            log.error("同步微信分账结果失败", e);
            this.updateFail(payProfitSharingReturn.getId(), e.getMessage(), payProfitSharingReturn);
        }
    }

    /**
     * 根据支付退款单ID查询分账退回记录
     *
     * @param payRefundId 支付退款单ID
     * @return 分账退回记录
     */
    @Override
    public PayProfitSharingReturn getByPayRefundId(Long payRefundId) {
        return baseMapper.selectOne(Wrappers.<PayProfitSharingReturn>lambdaQuery()
            .eq(PayProfitSharingReturn::getPayRefundId, payRefundId)
            .last("limit 1"));
    }

    /**
     * 分页支付分账回退记录列表
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<PayProfitSharingReturnVo> queryPageList(PayProfitSharingReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayProfitSharingReturn> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStatus() != null, PayProfitSharingReturn::getStatus, bo.getStatus());
        lqw.eq(bo.getPayRefundId() != null, PayProfitSharingReturn::getPayRefundId, bo.getPayRefundId());
        lqw.eq(bo.getPayOrderId() != null, PayProfitSharingReturn::getPayOrderId, bo.getPayOrderId());
        lqw.eq(bo.getPayProfitSharingId() != null, PayProfitSharingReturn::getPayProfitSharingId, bo.getPayProfitSharingId());
        lqw.orderByDesc(PayProfitSharingReturn::getCreateTime);
        Page<PayProfitSharingReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询支付分账回退记录列表
     * @param bo
     * @return
     */
    @Override
    public List<PayProfitSharingReturnVo> queryList(PayProfitSharingReturnBo bo) {
        LambdaQueryWrapper<PayProfitSharingReturn> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStatus() != null, PayProfitSharingReturn::getStatus, bo.getStatus());
        return baseMapper.selectVoList(lqw);
    }

    private PayProfitSharingReturn buildPayProfitSharingReturn(PayRefund payRefund, PayProfitSharing profitSharing) {
        var payProfitSharingReturn = PayProfitSharingReturn.builder()
            .payRefundId(payRefund.getId())
            .payOrderId(payRefund.getPayOrderId())
            .payProfitSharingId(profitSharing.getId())
            .createTime(LocalDateTime.now())
            .remark("")

            .channelCode(payRefund.getChannelCode())
            .channelErrorMsg("")
            .rate(profitSharing.getRate())
            .refundAmount(payRefund.getRefundAmount())
            .returnAmount(BigDecimal.ZERO)
            .account(profitSharing.getAccount())

            .description(StringUtils.blankToDefault(payRefund.getReason(), "退款"))
            .status(PayProfitSharingReturnStatusEnum.PROCESSING.getType())
            .retryCount(0)
            .build();

        // 计算剩余分账金额：剩余分账金额 = 分账金额 - 已退回金额
        var remainingAmount = profitSharing.getProfitSharingAmount().subtract(profitSharing.getProfitSharingReturnAmount());

        // 查一下支付单，如果支付单是全额退款的，那么分账退回金额就是分账单剩余可退回金额
        var payOrder = payOrderService.getById(payRefund.getPayOrderId());
        if (PayOrderStatusEnum.REFUND.getType().equals(payOrder.getStatus())) {
            payProfitSharingReturn.setReturnAmount(remainingAmount);
        } else {
            // 如果支付单不是全额退款的，那么分账退回金额就是退款金额 * 分账比例
            // 计算退回金额，保留两位小数，四舍五入
            payProfitSharingReturn.setReturnAmount(
                payRefund.getRefundAmount()
                    .multiply(payProfitSharingReturn.getRate())
                    .setScale(2, RoundingMode.HALF_UP)
            );
        }

        // 如果需要退回的金额大于剩余分账金额，则设置为剩余分账金额
        if (payProfitSharingReturn.getReturnAmount().compareTo(remainingAmount) > 0) {
            payProfitSharingReturn.setReturnAmount(remainingAmount);
        }

        // 如果需要回退的金额小于0.01，则设置为0，并且标记为忽略
        if (payProfitSharingReturn.getReturnAmount().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            payProfitSharingReturn.setReturnAmount(BigDecimal.ZERO);
            payProfitSharingReturn.setStatus(PayProfitSharingReturnStatusEnum.IGNORE.getType());
            payProfitSharingReturn.setFinishTime(LocalDateTime.now());

            payProfitSharingReturn.setRemark("退回金额小于0.01, 无需退回");
        }

        return payProfitSharingReturn;
    }

    /**
     * 重试分账退回
     */
    private void retryProfitSharingReturn(PayProfitSharingReturn payProfitSharingReturn) {
        // 重置分账单状态
        baseMapper.update(Wrappers.<PayProfitSharingReturn>lambdaUpdate()
            .set(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.PROCESSING.getType())
            .set(PayProfitSharingReturn::getChannelErrorMsg, "")
            // 主键
            .eq(PayProfitSharingReturn::getId, payProfitSharingReturn.getId()));

        // 更新支付退款单状态
        payRefundService.updateProfitSharingReturnStatus(
            payProfitSharingReturn.getPayRefundId(),
            PayRefundProfitSharingStatusEnum.SUCCESS
        );

        if (PayChannelEnum.WX_LITE.getCode().equals(payProfitSharingReturn.getChannelCode())) {
            this.requestReturnWx(payProfitSharingReturn);
        }

    }

    /**
     * 查询需要重试的分账退回记录
     * @return
     */
    @Override
    public List<PayProfitSharingReturn> listNeedRetry() {
        return baseMapper.selectList(Wrappers.<PayProfitSharingReturn>lambdaQuery()
            .eq(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.RETRY.getType())
            .le(PayProfitSharingReturn::getNextRetryTime, LocalDateTime.now())
        );
    }

    /**
     * 请求微信退回
     *
     * @param payProfitSharingReturn 分账退回记录
     */
    private void requestReturnWx(PayProfitSharingReturn payProfitSharingReturn) {
        var request = ProfitSharingReturnV3Request.newBuilder()
            .outOrderNo(payProfitSharingReturn.getPayProfitSharingId().toString())
            .outReturnNo(payProfitSharingReturn.getId().toString())
            .returnMchid(payProfitSharingReturn.getAccount())
            .amount(payProfitSharingReturn.getReturnAmount().multiply(BigDecimal.valueOf(100)).longValue())
            .description(payProfitSharingReturn.getDescription())
            .build();
        ProfitSharingReturnV3Result result;

        try {
            result = wxPayService.getProfitSharingService().profitSharingReturnV3(request);
            payProfitSharingReturn.setChannelOrderNo(result.getReturnId());

            log.info("请求微信退回成功, {}", request);
            // TODO 后续处理
        } catch (WxPayException e) {
            log.error("请求微信退回失败", e);
            this.updateFail(payProfitSharingReturn.getId(), e.getMessage(), payProfitSharingReturn);
        }
    }

    /**
     * 更新成功
     *
     * @param id 主键
     */
    private void updateSuccess(Long id, String channelOrderNo, LocalDateTime finishTime) {
        var count = baseMapper.update(Wrappers.<PayProfitSharingReturn>lambdaUpdate()
            .set(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.SUCCESS.getType())
            .set(PayProfitSharingReturn::getChannelOrderNo, channelOrderNo)
            .set(PayProfitSharingReturn::getSuccessTime, finishTime)
            .set(PayProfitSharingReturn::getFinishTime, finishTime)
            .set(PayProfitSharingReturn::getChannelErrorMsg, "")
            .eq(PayProfitSharingReturn::getId, id));
        ValidateUtils.isTrue(count == 1, "更新分账退回状态失败");
    }

    /**
     * 更新失败
     *
     * @param id      主键
     * @param message 失败原因
     */
    private void updateFail(Long id, String message, PayProfitSharingReturn payProfitSharingReturn) {

        LambdaUpdateWrapper<PayProfitSharingReturn> updateWrapper = Wrappers.<PayProfitSharingReturn>lambdaUpdate()
            .set(PayProfitSharingReturn::getChannelErrorMsg, message)
            .eq(PayProfitSharingReturn::getId, id);

        // 根据 profitSharingReturn 的重试次数，判断是否需要重试
        int retryCount = payProfitSharingReturn.getRetryCount();
        // 找到下一次重试的间隔
        int interval = mallProperties.getRetryIntervalByIndex(retryCount);

        if (interval == 0) {
            // 到达最大重试次数，更新状态为失败
            updateWrapper.set(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.FAILURE.getType());
        } else {
            // 更新重试次数
            updateWrapper.set(PayProfitSharingReturn::getRetryCount, retryCount + 1);
            // 更新状态为重试
            updateWrapper.set(PayProfitSharingReturn::getStatus, PayProfitSharingReturnStatusEnum.RETRY.getType());
            // 更新下一次重试时间
            updateWrapper.set(PayProfitSharingReturn::getNextRetryTime, LocalDateTime.now().plusSeconds(interval));
        }

        int count = baseMapper.update(null, updateWrapper);
        ValidateUtils.isTrue(count == 1, "更新分账退回状态失败");
    }

}
