package yq.mall.service;

import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.controller.pay.bo.PayRefundNotifyBo;
import yq.mall.domain.PayRefund;
import yq.mall.domain.bo.pay.PayRefundBo;
import yq.mall.domain.vo.pay.PayRefundVo;
import yq.mall.enums.pay.PayRefundProfitSharingStatusEnum;

import java.util.List;

/**
 * 支付退款单服务接口
 * <AUTHOR>
 */
public interface IPayRefundService {

    /**
     * 根据id获取支付退款单
     * @return 支付退款单
     */
    PayRefund getById(Long id);

    /**
     * 创建支付退款单
     * @return 支付退款单ID
     */
    Long createRefund(PayRefund refund);

    /**
     * 更新退款成功
     * @param bo 退款通知业务对象
     */
    void updateRefundSuccess(PayRefundNotifyBo bo);

    /**
     * 更新退款失败
     */
    void updateRefundFail(Long refundId,String message,PayRefund refund);

    /**
     * 发起微信退款
     * @param refund 退款单
     */
    void requestRefundWx(PayRefund refund);

    /**
     *  根据orderId查询退款单记录
     */
    List<PayRefundVo> getList(Long orderId, Integer status);

    /**
     * 退款单重试退款列表
     */
    List<PayRefund> payRefundRetryList();

    /**
     * 查询支付退款单列表
     */
    TableDataInfo<PayRefundVo> queryPageList(PayRefundBo bo, PageQuery pageQuery);

    /**
     * 更新支付单分账状态
     * @param id 支付单ID
     * @param status 分账状态
     */
    void updateProfitSharingReturnStatus(Long id, PayRefundProfitSharingStatusEnum status);

    /**
     * 查询待分账退回的退款单
     * @return 待分账退回的退款单
     */
    List<PayRefund> listWaitingProfitSharingReturn();

    /**
     * 更新支付退款单接口提交时间
     * @param id payRefundId
     */
    void applyRefundById(Long id);
}
