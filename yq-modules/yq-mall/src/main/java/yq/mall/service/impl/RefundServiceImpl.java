package yq.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import yq.bus.api.RemoteLineScheduleService;
import yq.common.core.exception.AlertException;
import yq.common.core.utils.DateUtils;
import yq.common.core.utils.SpringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.mall.constant.ClientConfigConstant;
import yq.mall.constant.MallLockConstant;
import yq.mall.controller.client.refund.bo.TicketRefundBo;
import yq.mall.domain.*;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.domain.vo.OrderWaitlistTicketVo;
import yq.mall.domain.vo.RefundRulesVo;
import yq.mall.domain.vo.RefundVo;
import yq.mall.enums.TicketStatusEnum;
import yq.mall.enums.order.OrderItemRefundStatusEnum;
import yq.mall.enums.order.OrderStatusEnum;
import yq.mall.enums.order.OrderTypeEnum;
import yq.mall.enums.order.OrderWaitlistStatusEnum;
import yq.mall.enums.refund.RefundOperateTypeEnum;
import yq.mall.enums.refund.RefundStatusEnum;
import yq.mall.mapper.RefundItemMapper;
import yq.mall.mapper.RefundMapper;
import yq.mall.service.*;
import yq.system.api.RemoteConfigService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 退款服务实现类
 *
 * <AUTHOR> Suen
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class RefundServiceImpl implements IRefundService {

    @Resource
    private RefundMapper baseMapper;
    @Resource
    private RefundItemMapper itemMapper;

    @Resource
    private IPayRefundService payRefundService;

    @Resource
    private IOrderService orderService;

    @Resource
    private IPayOrderService payOrderService;

    @Resource
    private IOrderTicketService orderTicketService;

    @Resource
    @DubboReference
    private RemoteLineScheduleService remoteLineScheduleService;

    @Resource
    private IRefundRulesService refundRulesService;

    @Resource
    @Lazy
    private IPromotionCouponService couponService;

    @DubboReference
    @Resource
    private final RemoteConfigService remoteConfigService;

    @Resource
    private IOrderWaitlistTicketService orderWaitlistTicketService;

    /**
     * 获取自身实例, 解决AOP失效问题
     * @return 本类实例
     */
    private RefundServiceImpl getSelf() {
        return SpringUtils.getBean(RefundServiceImpl.class);
    }

    /**
     * 全额退款
     * 事务由applyRefund 方法控制
     * @param userId 用户ID
     * @param bo     客户端-退款-车票退款业务对象
     */
    @Override
    public void applyRefundByMember(Long userId, TicketRefundBo bo) {
        var order = orderService.getOrder(userId, bo.getOrderId());
//        var refundInfo = orderService.getRefundTicketInfo(userId, bo.getOrderId());
        // 检查订单是否可退款
        validMemberOrderRefund(order);

        // 获取小程序手续费开关设置
        String serverChargeString = remoteConfigService.selectConfigByKey(ClientConfigConstant.CLIENT_SERVER_CHARGE_FLAG);
        boolean serverChargeFlag = false;
        if ("true".equals(serverChargeString)) {
            serverChargeFlag = true;
        }

        var refund = getSelf().applyRefund(order, RefundOperateTypeEnum.MEMBER_CREATE,RefundOperateTypeEnum.MEMBER_CREATE.getName(), bo, serverChargeFlag);

        // 退款金额为0则不发起微信退款
        if (refund.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        try{
            // 发起微信退款
            payRefundService.applyRefundById(refund.getPayRefundId());

        } catch (Exception e) {
            throw new AlertException("退款异常，请联系客服处理");
        }
    }

    /**
     * 系统发起退款
     * 事务由applyRefund 方法控制
     * @param userId 用户ID
     * @param bo ticket退款业务对象
     */
    @Override
    public void applyRefundByAdmin(Long userId, TicketRefundBo bo, Boolean serverChargeFlag) {
        var order = orderService.getOrder(bo.getOrderId());
        // 检查订单是否可退款
        validAdminOrderRefund(order);

        var refund = getSelf().applyRefund(order, RefundOperateTypeEnum.ADMIN_CREATE, bo.getRefundReason(), bo, serverChargeFlag);

        // 退款金额为0则不发起微信退款
        if (refund.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 发起微信退款
        payRefundService.applyRefundById(refund.getPayRefundId());
    }

    /**
     * 查询订单退款记录
     * @param orderId
     * @return
     */
    @Override
    public List<RefundVo> refundList(Long orderId) {
        LambdaQueryWrapper<Refund> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Refund::getOrderId, orderId);
        return baseMapper.selectVoList(queryWrapper);
    }

    /**
     * 申请退款
     * @param order 订单
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(name = MallLockConstant.LOCK_ORDER, keys = {"#order.id"}, expire = 10000, acquireTimeout = 200)
    public Refund applyRefund(Order order, RefundOperateTypeEnum operateType, String reason, TicketRefundBo bo, boolean serverChargeFlag) {

        // 获取退款项
        List<TicketRefundBo.RefundItem> refundItems = bo.getRefundItems();
        ValidateUtils.notNull(refundItems, "退款项不存在");

        List<OrderTicketVo> ticketList = new ArrayList<>();

        //候补车票退款标记
        boolean waitlistRefundFlag = false;

        //订单为候补订单并且车票未候补成功
        if( order.getExtraInfo() instanceof Order.WaitlistExtra &&
            ((Order.WaitlistExtra) order.getExtraInfo()).getWaitlistStatus() < OrderWaitlistStatusEnum.WAITLISTED.getStatus()){
            //获取候补订单的车票
            List<OrderWaitlistTicketVo> waitlistTicketList = orderWaitlistTicketService.queryList(order.getId());
            // 进行拷贝
            List<OrderTicketVo> copyTickets = BeanUtil.copyToList(waitlistTicketList, OrderTicketVo.class);
            ticketList.addAll(copyTickets);

            waitlistRefundFlag = true;
            // 候补车票退款不收手续费
            serverChargeFlag = false;

        } else {
            // 获取订单下的所有车票
            ticketList = orderTicketService.getOrderTicketByOrderId(order.getId());
        }

        ValidateUtils.notNull(ticketList, "车票不存在");

        // 获取所有车票的 ID 列表
        List<Long> allTicketIds = ticketList.stream().map(OrderTicketVo::getId).toList();

        // 获取需要退款的车票 ID 列表
        List<Long> refundTicketIds = refundItems.stream().map(TicketRefundBo.RefundItem::getTicketId).toList();
        ValidateUtils.isTrue(CollUtil.isNotEmpty(refundTicketIds), "退款项不存在");

        // 检查退款车票是否都存在于 ticketList 中
        ValidateUtils.isTrue(CollUtil.containsAll(allTicketIds, refundTicketIds), "退款车票不存在");

        // 获取需要退款的车票列表
        List<OrderTicketVo> refundTicketList = ticketList.stream()
            .filter(item -> refundTicketIds.contains(item.getId()))
            .toList();

        // 候补票全部退
        if (waitlistRefundFlag){
            ValidateUtils.isTrue(allTicketIds.size() == refundTicketIds.size(), "候补行程仅支持全部退票");
        }

        // 检查车票是否可退款
        refundTicketList.forEach(item -> {
            ValidateUtils.isTrue(item.getPayStatus(), "申请退票失败，车票未支付");
            ValidateUtils.isTrue(OrderItemRefundStatusEnum.NONE.getType().equals(item.getRefundStatus()), "车票已申请退款");
        });


        // 会员发起退款时，需要检查车票是否已验票，后台发起则不需要
        if (RefundOperateTypeEnum.MEMBER_CREATE.equals(operateType)) {
            validMemberTicketRefund(order, refundTicketList);
        }

        // 给refundTicketList设置初始化的服务费0
        refundTicketList.forEach(item -> item.setServiceChargePrice(BigDecimal.ZERO));

        // 计算手续费
         BigDecimal totalServiceCharge = BigDecimal.ZERO;
         if (serverChargeFlag){
             totalServiceCharge = calcServiceCharge(refundTicketList);
         }

        // refundTicketList的实付金额
        BigDecimal payPrice = refundTicketList.stream().map(OrderTicketVo::getPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 实际退款金额 = payPrice - totalServiceCharge
        BigDecimal refundPrice = payPrice.subtract(totalServiceCharge);

        // 校验客户端传递的手续费是否一致
        if (RefundOperateTypeEnum.MEMBER_CREATE.equals(operateType)) {
            ValidateUtils.notNull(bo.getTotalServerCharge(), "手续费不能为空");
            ValidateUtils.isTrue(bo.getTotalServerCharge().compareTo(totalServiceCharge) == 0, "手续费不一致，请重试");
        }

        //判断实际退款金额是否大于支付金额
        ValidateUtils.isFalse( refundPrice.compareTo(payPrice) > 0 , "退款金额不能大于支付金额");

        // 创建退款单
        var refund = Refund.builder()
            .memberId(order.getUserId())
            .status(RefundStatusEnum.APPLY.getType())
            .orderId(order.getId())
            // 拿item的合计payPrice
            .payPrice(payPrice)
            .refundAmount(refundPrice)
            .applyReason(reason)
            .createTime(new Date())
            .build();

        // 如果实际退款金额为0，则直接退款成功
        if (refundPrice.compareTo(BigDecimal.ZERO) == 0) {
            refund.setStatus(RefundStatusEnum.SUCCESS.getType());
            refund.setRefundTime(new Date());
        }

        // 保存退款信息
        baseMapper.insert(refund);

        if (OrderTypeEnum.isTicketType(order.getType())) {

            // 创建退款明细
            var itemList = new ArrayList<RefundItem>();

            refundTicketList.forEach(ticket -> {
                // 计算ticket的refundAmount
                BigDecimal ticketRefundAmount = ticket.getPayPrice().subtract(ticket.getServiceChargePrice());

                var refunditem = RefundItem.builder()
                    .refundId(refund.getId())
                    .refundAmount(ticketRefundAmount)
                    .orderType(order.getType())
                    .orderId(ticket.getOrderId())
                    .orderItemId(ticket.getId())
                    .refundRule(ticket.getRefundRule())
                    .build();
                itemList.add(refunditem);
            });
            itemMapper.insertBatch(itemList);
        } else {
            // TODO 其他类型订单退款
            throw new UnsupportedOperationException("暂不支持其他类型订单退款");
        }


        // 更新指定车票退款信息
        if (waitlistRefundFlag){
            orderService.updateWaitlistOrderRefund(order, refund, refundTicketIds);
        } else {
            orderService.updateOrderRefund(order, refund, refundTicketIds);
        }

        // 如果所有车票都退款，则回退优惠券
        if ( order.getCouponId() != null && OrderStatusEnum.CANCELED.getType().equals(order.getStatus())) {
            try {
                couponService.returnUsedCoupon(order.getCouponId());
            } catch (Exception e) {
                log.error("[updateOrderRefund][orderId({}) 回退优惠券失败]", order.getId(), e);
            }
        }


        // 如果退款金额为0则直接返回refund即可，不需要创建payRefund记录
        if (refundPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return refund;
        }

        // 创建退款单
        var payRefund = PayRefund.builder()
            .payOrderId(order.getPayOrderId())
            .channelCode(order.getPayChannelCode())
            .refundAmount(refund.getRefundAmount())
            .merchantRefundId(refund.getId())
            .merchantOrderId(order.getId())
            // 这里只能为order的payPrice, 微信需要校验
            .payPrice(order.getPayPrice())
            .reason(refund.getApplyReason())
            .build();

        // 修改支付单状态
       payOrderService.updateOrderRefund(order.getPayOrderId(), refund);

        // 创建支付退款单，并发起退款
        var payRefundId = payRefundService.createRefund(payRefund);
        ValidateUtils.isTrue(payRefundId != null, "创建支付退款单失败");
        refund.setPayRefundId(payRefundId);
        // 回写支付退款单号
        baseMapper.update(Wrappers.<Refund>lambdaUpdate().eq(Refund::getId, refund.getId())
            .set(Refund::getPayRefundId, payRefundId)
            .set(Refund::getStatus, RefundStatusEnum.SUCCESS.getType())
        );

        return refund;
    }

    /**
     * 计算refundTicket手续费
     */
    private BigDecimal calcServiceCharge(List<OrderTicketVo> refundTicketList) {
        // 获取退款规则
        List<RefundRulesVo> refundRules = refundRulesService.listRefundRule();

        // 获取当前的时间戳
        long nowTimestamp = System.currentTimeMillis();

        // 将dateAt和timeAt合起来就是完整的时间
        refundTicketList.forEach( item -> {
            var dateAt = DateUtil.format(item.getDateAt(), DateUtils.YYYY_MM_DD);
            var timeAt = DateUtil.format(item.getTimeAt(), "HH:mm:ss");
            var dateTime = DateUtil.parse(dateAt + " " + timeAt, DateUtils.YYYY_MM_DD_HH_MM_SS);

            // 当前时间减去发车时间的时间戳
            long ticketTimestamp = dateTime.getTime() - nowTimestamp;

            //获取当前车票的退款规则
            RefundRulesVo refundRule = refundRules.stream()
                .filter(rule -> ticketTimestamp <= rule.getRefundTimestamp())
                .findFirst()
                .orElse(null);

            BigDecimal serviceChargePrice = BigDecimal.ZERO;

            // 获取手续费
            if (refundRule != null) {
                serviceChargePrice = item.getPayPrice().multiply(refundRule.getServiceCharge()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);

                // 手续费向下只留2位小数
                serviceChargePrice = serviceChargePrice.setScale(2, RoundingMode.DOWN );

                // 如果手续费小于0.01则手续费为0
                if (serviceChargePrice.compareTo(BigDecimal.valueOf(0.01)) < 0) {
                    serviceChargePrice = BigDecimal.ZERO;
                }

                // 记录退款规则快照，json序列化
                String ruleJson = JSONUtil.toJsonStr(refundRule);
                item.setRefundRule(ruleJson);
            }
            item.setServiceChargePrice(serviceChargePrice);
        });

        // 返回本次服务费
        return refundTicketList.stream().map(OrderTicketVo::getServiceChargePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 检查订单是否可退款
     *
     * @param order 订单
     */
    private void validOrderRefundCommon(Order order) {
        ValidateUtils.notNull(order, "订单不存在");
        // 支付状态检查: 必须时已支付状态
        ValidateUtils.isTrue(OrderStatusEnum.PAYED.getType().equals(order.getStatus()), "申请退款失败，订单必须是已支付状态");
    }

    /**
     * 检查订单是否可退款(用户发起）
     *
     * @param order 订单
     */
    private void validMemberOrderRefund(Order order) {
        validOrderRefundCommon(order);
    }

    /**
     * 检查订单是否可退款（系统发起）
     * @param order 订单
     */
    private void validAdminOrderRefund(Order order) {
        validOrderRefundCommon(order);
    }

    /**
     * 检查车票是否可退款（用户发起）
     */
    private void validMemberTicketRefund(Order order, List<OrderTicketVo> ticketList) {
        for (OrderTicketVo ticket : ticketList) {
            // 支付状态检查: 必须时已支付状态
            ValidateUtils.isTrue(ticket.getPayStatus(), "申请退票失败，车票未支付");
            // 状态检查：车票状态为未排班和待核验时才可退票
            ticketList.forEach(item -> {
                ValidateUtils.isFalse( TicketStatusEnum.VERIFIED.getCode().equals(item.getStatus())
                || TicketStatusEnum.COMPLETED.getCode().equals(item.getStatus()) , "车票已核验，请联系客服处理");
            });

            // 超过发车时间不可退票
            long nowTimestamp = System.currentTimeMillis();
            ticketList.forEach(item -> {
                var dateAt = DateUtil.format(item.getDateAt(), DateUtils.YYYY_MM_DD);
                var timeAt = DateUtil.format(item.getTimeAt(), "HH:mm:ss");
                var dateTime = DateUtil.parse(dateAt + " " + timeAt, DateUtils.YYYY_MM_DD_HH_MM_SS);

                // 当前时间减去车票发车时间的时间戳
               if (dateTime.getTime() - nowTimestamp < 0) {
                    ValidateUtils.isTrue(false, "已过发车时间,申请退票失败");
                }

            });
        }
    }


}
