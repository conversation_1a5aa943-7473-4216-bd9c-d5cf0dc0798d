package yq.mall.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingUnfreezeV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingV3Request;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yq.common.core.exception.ServiceException;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.config.properties.MallProperties;
import yq.mall.config.properties.PayProfitSharingProperties;
import yq.mall.config.properties.WxPayV3Properties;
import yq.mall.constant.MallLockConstant;
import yq.mall.domain.PayOrder;
import yq.mall.domain.PayProfitSharing;
import yq.mall.domain.bo.pay.PayProfitSharingBo;
import yq.mall.domain.vo.pay.PayProfitSharingVo;
import yq.mall.enums.pay.*;
import yq.mall.mapper.PayProfitSharingMapper;
import yq.mall.service.IPayOrderService;
import yq.mall.service.IPayProfitSharingService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 分账服务实现类
 *
 * <AUTHOR> Suen
 */
@Slf4j
@Service
public class PayProfitSharingServiceImpl implements IPayProfitSharingService {

    @Resource
    private WxPayV3Properties wxPayV3Properties;

    @Resource
    private MallProperties mallProperties;

    @Resource
    private PayProfitSharingMapper baseMapper;

    @Resource
    private PayProfitSharingProperties payProfitSharingProperties;

    @Resource
    private IPayOrderService payOrderService;

    @Resource
    private WxPayService wxPayService;

    /**
     * 发起分账，开启新的事务
     *
     * @param payOrder 支付单
     */
    @Async
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#payOrder.id"})
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void profitSharing(PayOrder payOrder) {

        var profitSharing = this.getByPayOrderId(payOrder.getId());
        if (profitSharing != null) {
            this.retryProfitSharing(payOrder, profitSharing);
            return;
        }

        // 构建分账单
        profitSharing = buildProfitSharing(payOrder);
        var receiver = payProfitSharingProperties.getReceiver();
        if (ObjectUtil.isNull(receiver)) {
            // 无分账方，先不进行处理
            return;
        }

        // 如果支付单状态为退款了，那么将状态设置为忽略
        if (PayOrderStatusEnum.REFUND.getType().equals(payOrder.getStatus())) {
            profitSharing.setStatus(PayProfitSharingStatusEnum.IGNORE.getType());
            profitSharing.setFinishTime(LocalDateTime.now());
            // 错误信息
            profitSharing.setRemark("支付单已退款，无需分账");
            profitSharing.setAmount(BigDecimal.ZERO);
            profitSharing.setUnFreeze(true);
        }

        // 保存分账单
        baseMapper.insert(profitSharing);

        // 更新支付订单分账状态
        payOrderService.updateProfitSharingStatus(payOrder.getId(), PayOrderProfitSharingStatusEnum.SUCCESS);

        // 忽略的分账单，直接退出
        if (PayProfitSharingStatusEnum.IGNORE.getType().equals(profitSharing.getStatus())) {
            return;
        }

        // 如果分账金额 > 0，进行分账
        // 否则，解冻
        if (profitSharing.getProfitSharingAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 如果分账金额 > 0，进行分账
            if (PayChannelEnum.WX_LITE.getCode().equals(payOrder.getChannelCode())) {
                this.requestWx(payOrder, profitSharing);
            }
        } else {
            // 如果分账金额 = 0，进行解冻
            this.unfreeze(payOrder, profitSharing);
        }
    }

    /**
     * 查询处理中的分账记录
     *
     * @return 处理中的分账记录
     */
    @Override
    public List<PayProfitSharing> listPending() {
        return baseMapper.selectList(Wrappers.<PayProfitSharing>lambdaQuery()
            .eq(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.PROCESSING.getType())
        );
    }

    /**
     * 同步微信分账结果
     *
     * @param profitSharing 分账单
     */
    @Async
    @Lock4j(name = MallLockConstant.SYNC_PAY, keys = {"#profitSharing.payOrderId"})
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncWx(PayProfitSharing profitSharing) {
        var payOrder = payOrderService.getById(profitSharing.getPayOrderId());
        try {
            var result = wxPayService.getProfitSharingService().profitSharingQueryV3(
                profitSharing.getId().toString(),
                payOrder.getChannelOrderNo()
            );

            var receiver = CollUtil.getFirst(result.getReceivers());
            // 如果分账完成，更新分账单状态
            if (WxPayConstants.ResultCode.SUCCESS.equals(receiver.getResult())) {
                var zdt = ZonedDateTime.parse(receiver.getFinishTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZZZZZ"));
                this.updateFinal(profitSharing.getId(),
                    result.getOrderId(),
                    receiver.getDetailId(),
                    LocalDateTime.from(zdt)
                );
            } else if (WxPayConstants.ResultCode.FAIL.equals(receiver.getResult())) {
                this.updateFinalFail(profitSharing.getId(), receiver.getResult());
            }

        } catch (WxPayException e) {
            log.error("同步微信分账结果失败", e);
        }
    }

    /**
     * 更新退回金额
     *
     * @param id           分账记录id
     * @param returnAmount 退回金额
     */
    @Override
    public void updateReturnAmount(Long id, BigDecimal returnAmount) {
        baseMapper.update(null, Wrappers.<PayProfitSharing>lambdaUpdate()
            .setSql("profit_sharing_return_amount = profit_sharing_return_amount + " + returnAmount)
            // 主键
            .eq(PayProfitSharing::getId, id));
    }

    /**
     * 根据支付订单ID获取分账单
     *
     * @param payOrderId 支付订单ID
     * @return 分账单
     */
    @Override
    public PayProfitSharing getByPayOrderId(Long payOrderId) {
        return baseMapper.selectOne(Wrappers.<PayProfitSharing>lambdaQuery()
            .eq(PayProfitSharing::getPayOrderId, payOrderId)
            .last("limit 1"));
    }


    /**
     * 分页支付分账单列表
     */
    @Override
    public TableDataInfo<PayProfitSharingVo> queryPageList(PayProfitSharingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayProfitSharing> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPayOrderId() != null, PayProfitSharing::getPayOrderId, bo.getPayOrderId());
        lqw.eq(bo.getStatus() != null, PayProfitSharing::getStatus, bo.getStatus());
        lqw.orderByDesc(PayProfitSharing::getCreateTime);
        Page<PayProfitSharingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询支付分账单列表
     */
    @Override
    public List<PayProfitSharingVo> queryList(PayProfitSharingBo bo) {
        LambdaQueryWrapper<PayProfitSharing> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPayOrderId() != null, PayProfitSharing::getPayOrderId, bo.getPayOrderId());
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 找到需要重试的分账记录
     * @return
     */
    @Override
    public List<PayProfitSharing> listRetry() {
        LambdaQueryWrapper<PayProfitSharing> lqw = Wrappers.lambdaQuery();
        lqw.eq(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.RETRY.getType());
        lqw.le(PayProfitSharing::getNextRetryTime, LocalDateTime.now());
        return baseMapper.selectList(lqw);
    }

    /**
     * 重试分账
     *
     * @param profitSharing 分账单
     */
    private void retryProfitSharing(PayOrder payOrder, PayProfitSharing profitSharing) {

        //如果忽略的账单没有解冻，并且分账金额为0，那么需要进行重试解冻
        if (!profitSharing.getUnFreeze() &&
            profitSharing.getProfitSharingAmount().compareTo(BigDecimal.ZERO) == 0) {
            this.unfreeze(payOrder, profitSharing);
            return;
        }

        // 重置分账单状态
        baseMapper.update(Wrappers.<PayProfitSharing>lambdaUpdate()
            .set(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.PROCESSING.getType())
            .set(PayProfitSharing::getChannelErrorMsg, "")
            // 主键
            .eq(PayProfitSharing::getId, profitSharing.getId()));

        // 更新支付订单分账状态
        payOrderService.updateProfitSharingStatus(payOrder.getId(), PayOrderProfitSharingStatusEnum.SUCCESS);

        if (PayChannelEnum.WX_LITE.getCode().equals(payOrder.getChannelCode())) {
            this.requestWx(payOrder, profitSharing);
        }
    }

    /**
     * 构建分账单
     *
     * @param payOrder 支付单
     * @return 分账单
     */
    private PayProfitSharing buildProfitSharing(PayOrder payOrder) {
        // TODO 同一笔分账单最多只能发起20次分账
        // TODO 分账的期限是180天

        var receiver = payProfitSharingProperties.getReceiver();
        var profitSharing = PayProfitSharing.builder()
            .payOrderId(payOrder.getId())
            .amount(payOrder.getTotalAmount())
            .profitSharingReturnAmount(BigDecimal.ZERO)
            .rate(receiver.getRate())
            .status(PayProfitSharingStatusEnum.PROCESSING.getType())

            .channelCode(payOrder.getChannelCode())
            .channelErrorMsg("")

            .createTime(LocalDateTime.now())
            .description(payOrder.getSubject())
            .unFreeze(false)

            .type(receiver.getType())
            .name(receiver.getName())
            .relationType(receiver.getRelationType())
            .account(receiver.getAccount())
            .remark("")

            .retryCount(0)

            .build();

        // 对于部分退款的订单，分账金额需要减去退款金额
        if (PayOrderStatusEnum.REFUND_PART.getType().equals(payOrder.getStatus())) {
            profitSharing.setAmount(profitSharing.getAmount().subtract(payOrder.getRefundAmount()));
            profitSharing.setRemark("分账时支付单已部分退款，可分账金额：" + profitSharing.getAmount());
        }

        // 计算金额：分账金额 = 订单金额 * 分账比例
        profitSharing.setProfitSharingAmount(
            profitSharing.getAmount().multiply(profitSharing.getRate())
                .setScale(2, RoundingMode.HALF_UP)
        );

        // 0.01元以下的分账金额不进行分账
        // 0.01（订单金额） * 0.03（比例） = 0.0003，分账没有意义
        if (profitSharing.getProfitSharingAmount().compareTo(BigDecimal.valueOf(0.01)) <= 0) {
            profitSharing.setProfitSharingAmount(BigDecimal.ZERO);
            profitSharing.setFinishTime(LocalDateTime.now());
        }

        return profitSharing;
    }

    /**
     * 请求微信分账
     *
     * @param payOrder      支付单
     * @param profitSharing 分账单
     */
    public void requestWx(PayOrder payOrder, PayProfitSharing profitSharing) {
        var request = ProfitSharingV3Request.newBuilder()
            .appid(wxPayV3Properties.getAppId())
            .transactionId(payOrder.getChannelOrderNo())
            .outOrderNo(profitSharing.getId().toString())
            .unfreezeUnsplit(true)
            .build();

        var wxReceiver = new ProfitSharingV3Request.Receiver();
        // 元转分
        wxReceiver.setAmount(profitSharing.getProfitSharingAmount().multiply(BigDecimal.valueOf(100)).intValue());
        wxReceiver.setAccount(profitSharing.getAccount());
        wxReceiver.setName(profitSharing.getName());
        wxReceiver.setType(profitSharing.getType());
        wxReceiver.setRelationType(profitSharing.getRelationType());
        wxReceiver.setDescription(profitSharing.getDescription());

        request.setReceivers(new ArrayList<>());
        request.getReceivers().add(wxReceiver);

        try {
            // 请求微信分账
            var result = wxPayService.getProfitSharingService().profitSharingV3(request);
            profitSharing.setChannelOrderNo(result.getOrderId());
        } catch (WxPayException e) {
            log.error("请求微信分账失败", e);
            this.updateFail(profitSharing.getId(), e.getMessage(),profitSharing);
        } catch (Exception e){
            log.error("请求微信分账失败,全异常捕获", e);
        }
    }

    /**
     * 根据渠道发起解冻
     */
    private void unfreeze(PayOrder payOrder, PayProfitSharing profitSharing) {
        try {
            if(PayChannelEnum.WX_LITE.getCode().equals(payOrder.getChannelCode())) {
                this.requestUnfreezeWx(payOrder, profitSharing);
            }
        } catch (Exception e) {
            // 解冻失败，标记为重试状态
            this.updateFail(profitSharing.getId(), e.getMessage(), profitSharing);
        }
    }


    /**
     * 请求微信分账解冻，对于忽略的分账
     *
     * @param payOrder      支付单
     * @param profitSharing 分账单
     */
    private void requestUnfreezeWx(PayOrder payOrder, PayProfitSharing profitSharing) {
        var request = ProfitSharingUnfreezeV3Request.newBuilder()
            .transactionId(payOrder.getChannelOrderNo())
            .outOrderNo(profitSharing.getId().toString())
            .description(profitSharing.getDescription())
            .build();
        try {
          wxPayService.getProfitSharingService().profitSharingUnfreeze(request);
        } catch (WxPayException e) {
            log.error("请求微信分账解冻失败", e);
            throw new ServiceException("请求微信分账解冻失败");
        }
    }


    /**
     * 更新失败
     *
     * @param id      主键
     * @param message 失败原因
     */
    private void updateFail(Long id, String message,PayProfitSharing profitSharing) {

        LambdaUpdateWrapper<PayProfitSharing> updateWrapper = Wrappers.<PayProfitSharing>lambdaUpdate()
            .set(PayProfitSharing::getChannelErrorMsg, message)
            .eq(PayProfitSharing::getId, id);

        //根据profitSharing的重试次数，判断是否需要重试
        int retryCount = profitSharing.getRetryCount();
        // 找到下一次重试的间隔
        int interval = mallProperties.getRetryIntervalByIndex(retryCount);

        if (interval == 0){
            // 到达最大重试次数，更新状态为失败
            updateWrapper.set(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.FAILURE.getType());
        } else {
            // 更新重试次数
            updateWrapper.set(PayProfitSharing::getRetryCount, retryCount + 1);
            // 更新状态为重试
            updateWrapper.set(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.RETRY.getType());
            // 更新下一次重试时间
            updateWrapper.set(PayProfitSharing::getNextRetryTime, LocalDateTime.now().plusSeconds(interval));
        }

        var count = baseMapper.update(null, updateWrapper);
        ValidateUtils.isTrue(count == 1, "更新分账状态失败");
    }

    /**
     * 更新分账完结
     *
     * @param id              主键
     * @param channelDetailId 通道分账明细单号
     */
    private void updateFinal(Long id, String channelOrderNo, String channelDetailId, LocalDateTime finishTime) {
        var count = baseMapper.update(null, Wrappers.<PayProfitSharing>lambdaUpdate()
            .set(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.FINISHED.getType())
            .set(PayProfitSharing::getChannelOrderNo, channelOrderNo)
            .set(PayProfitSharing::getFinishTime, finishTime)
            .set(PayProfitSharing::getSuccessTime, finishTime)
            .set(PayProfitSharing::getChannelErrorMsg, "")
            .set(PayProfitSharing::getChannelDetailId, channelDetailId)
            .set(PayProfitSharing::getUnFreeze, true)

            .eq(PayProfitSharing::getId, id));
        ValidateUtils.isTrue(count == 1, "更新分账状态失败");
    }

    /**
     * 更新分账最终失败
     *
     * @param id      主键
     * @param message 失败原因
     */
    private void updateFinalFail(Long id, String message) {
        var count = baseMapper.update(null, Wrappers.<PayProfitSharing>lambdaUpdate()
            .set(PayProfitSharing::getStatus, PayProfitSharingStatusEnum.FINISH_FAILED.getType())
            .set(PayProfitSharing::getChannelErrorMsg, message)
            .eq(PayProfitSharing::getId, id));
        ValidateUtils.isTrue(count == 1, "更新分账状态失败");
    }

}
