package yq.mall.service;

import org.springframework.transaction.annotation.Transactional;
import yq.mall.controller.admin.mall.vo.StationAndtPlanWaitlistBo;
import yq.mall.domain.OrderWaitlistTicket;
import yq.mall.domain.vo.OrderWaitlistTicketVo;
import yq.mall.domain.bo.OrderWaitlistTicketBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * @author：fish
 * @date：2024/10/24
 */
public interface IOrderWaitlistTicketService {

    /**
     * 查询候补行程
     */
    OrderWaitlistTicketVo queryById(Long id);

    /**
     * 查询候补行程列表
     */
    TableDataInfo<OrderWaitlistTicketVo> queryPageList(OrderWaitlistTicketBo bo, PageQuery pageQuery);

    /**
     * 查询候补行程列表
     */
    List<OrderWaitlistTicketVo> queryList(Long orderId);

    /**
     * 候补行程退款
     */
    void batchWaitlistTicketRefund(List<Long> waitlistTicketIds);

    /**
     * 创建候补行程
     */
    void createOrderWaitlistTicket(List<OrderWaitlistTicket> orderWaitlistTickets);

    /**
     * 通过订单id查询候补行程
     */
    List<OrderWaitlistTicket> queryListByOrderId(Long orderId);

    /**
     * 更新候补行程支付状态
     */
    void updateWaitlistTicketPaid(Long id);

    /**
     * 通过计划id查询候补行程
     */
    List<OrderWaitlistTicket> queryListByDepartPlanId(Long departPlanId);


    Long queryCountByDepartPlanId(Long departPlanId);

    void updateWaitlistTicket(List<OrderWaitlistTicket> orderWaitlistTickets);

    // 系统取消候补行程
    int systemCancelWaitlistTicket();

    /**
     * 根据订单id更新候补车票状态
     * @param orderId
     */
    void updateWaitlistTicketByOrderId(Long orderId);

    /**
     * 根据站点和计划id查询未排班的候补车票
     */
    List<OrderWaitlistTicketVo> getUnScheduleWaitlistTicketByStationAndPlanId(StationAndtPlanWaitlistBo bo);
}
