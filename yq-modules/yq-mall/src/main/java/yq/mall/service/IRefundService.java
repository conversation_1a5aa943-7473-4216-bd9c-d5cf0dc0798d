package yq.mall.service;
import yq.mall.controller.client.refund.bo.TicketRefundBo;
import yq.mall.domain.vo.RefundVo;

import java.util.List;

/**
 * 退款服务接口
 * <AUTHOR>
 */
public interface IRefundService {

    /**
     * 【用户】申请整单退款
     * @param userId 用户ID
     * @param bo 客户端-退款-车票退款业务对象
     */
    void applyRefundByMember(Long userId, TicketRefundBo bo);

    /**
     * 【系统】申请整单退款
     * @param userId 用户ID
     * @param bo 车票退款业务对象
     *                退款原因
     */
    void applyRefundByAdmin(Long userId, TicketRefundBo bo , Boolean serverChargeFlag);

    /**
     * 获取订单的退款记录
     * @param orderId
     * @return
     */
    List<RefundVo> refundList(Long orderId);

}
