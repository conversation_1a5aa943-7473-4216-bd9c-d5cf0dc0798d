package yq.mall.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.binarywang.wxpay.exception.WxPayException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import yq.common.core.exception.AlertException;
import yq.mall.api.RemotePayRefundService;
import yq.mall.domain.PayRefund;
import yq.mall.enums.pay.PayRefundProfitSharingStatusEnum;
import yq.mall.enums.pay.PayRefundStatusEnum;
import yq.mall.service.IPayRefundService;
import javax.annotation.Resource;
import java.util.List;

/**
 * RPC - 订单服务实现类
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemotePayRefundServiceImpl implements RemotePayRefundService {

    @Resource
    private IPayRefundService payRefundService;

    @Override
    public void payRefundRetryTask() throws AlertException {
        List<PayRefund> listNeedRetry = payRefundService.payRefundRetryList();
        if (CollUtil.isEmpty(listNeedRetry)) {
            return;
        }
        listNeedRetry.forEach(payRefund -> {
            try {
                payRefundService.applyRefundById(payRefund.getId());
            } catch (Exception ignore) {}
        });
    }
}
