package yq.mall.mapper;

import org.apache.ibatis.annotations.Param;
import yq.common.mybatis.core.mapper.BaseMapperPlus;
import yq.mall.controller.admin.mall.vo.FundFlowBillVo;
import yq.mall.domain.PayProfitSharing;
import yq.mall.domain.vo.pay.PayProfitSharingVo;

import java.util.List;

/**
 * 分账记录Mapper接口
 * <AUTHOR>
 */
public interface PayProfitSharingMapper extends BaseMapperPlus<PayProfitSharing, PayProfitSharingVo> {
    List<FundFlowBillVo> getProfitSharingAmount(@Param("startDate") String startDate, @Param("endDate") String endDate);

}
