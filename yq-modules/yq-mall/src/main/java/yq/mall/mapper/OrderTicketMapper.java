package yq.mall.mapper;

import yq.common.mybatis.core.mapper.BaseMapperPlus;
import yq.mall.controller.admin.mall.bo.CouponStatisticsQueryBo;
import yq.mall.controller.admin.mall.bo.SupplierTicketSalesBo;
import yq.mall.controller.admin.mall.bo.TicketSalesBo;
import yq.mall.controller.admin.mall.vo.CouponStatisticsVo;
import yq.mall.controller.admin.mall.vo.SupplierTicketSalesVo;
import yq.mall.controller.admin.mall.vo.TicketSalesVo;
import yq.mall.domain.OrderTicket;
import yq.mall.domain.vo.TicketCountVo;
import yq.mall.domain.vo.OrderTicketVo;


import java.util.List;


/**
 * 行程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface OrderTicketMapper extends BaseMapperPlus<OrderTicket, OrderTicketVo> {

    List<TicketCountVo> getUnSchedulingTicketCount(String dateAt);

    List<TicketSalesVo> getTicketSalesDetail(TicketSalesBo bo);

    List<CouponStatisticsVo> getCouponStatistics(CouponStatisticsQueryBo bo);

    List<SupplierTicketSalesVo> getSupplierTicketSalesDetail(SupplierTicketSalesBo bo);
}
