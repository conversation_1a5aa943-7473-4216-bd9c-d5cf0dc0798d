package yq.mall.notify.baen.model;

import lombok.Data;
import yq.common.json.utils.JsonUtils;

import java.util.HashMap;

/**
 * 车辆修改通知
 */
@Data
public class CarEditNotify extends NotifyModel {

    /**
     * 车牌号
     */
    private String licensePlate;


    @Override
    public String getParams() {
        var map = new HashMap<String, String>();
        map.put("车牌号", "licensePlate");

        return JsonUtils.toJsonString(map);
    }

}
