package yq.mall.notify.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import yq.bus.api.RemoteStationService;
import yq.mall.api.domain.bo.RemoteScheduleBo;
import yq.mall.constant.NotifyTemplateTypeConstant;
import yq.mall.domain.MemberUser;
import yq.mall.domain.bo.OrderTicketBo;
import yq.mall.domain.vo.NotifyTemplateVo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.enums.NotifyModelEnums;
import yq.mall.notify.baen.model.DownNotify;
import yq.mall.notify.baen.model.NotifyModel;

import yq.mall.notify.baen.model.TripConfirmNotify;
import yq.mall.notify.baen.model.UpNoticeNotify;
import yq.mall.notify.baen.wx.WxTemplate;
import yq.mall.notify.service.INotifyTemplateConfigService;
import yq.mall.notify.service.INotifyTemplateService;
import yq.mall.notify.service.ISendMessageService;
import yq.mall.service.IMemberUserService;
import yq.mall.service.IOrderTicketService;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;


/**
 * 发送消息服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SendMessageServiceImpl implements ISendMessageService {

    @Resource
    private WxMaService wxMaService;
    @Resource
    @DubboReference
    private RemoteStationService remoteStationService;
    private final INotifyTemplateService notifyTemplateService;
    private final INotifyTemplateConfigService notifyTemplateConfigService;
    private final IMemberUserService memberUserService;

    @Lazy
    @Resource
    private IOrderTicketService orderTicketService;

    @Lazy
    @Resource
    private ISendMessageService self;

    /**
     * 构建上车消息并发送
     */
    @Async
    @Override
    public void buildUpNoticeNotify(List<OrderTicketVo> orderTicketVoList) {
        try {
            orderTicketVoList.parallelStream().forEach(orderTicketVo -> {
                UpNoticeNotify upNoticeNotify = new UpNoticeNotify();
                Date boardingTime = orderTicketVo.getBoardingTime();
                String format = DateUtil.format(boardingTime, "yyyy年MM月dd日 HH:mm");

                // 构建对象
                upNoticeNotify.setTime(format);
                upNoticeNotify.setId(orderTicketVo.getId());
                upNoticeNotify.setLicensePlate(orderTicketVo.getActualLicensePlate());
                upNoticeNotify.setPassengerName(orderTicketVo.getPassengerName());
                String upStation = remoteStationService.selectNameById(orderTicketVo.getFromId());
                if (upStation != null) {
                    upNoticeNotify.setLocale(upStation);
                }

                // 发送消息
                self.sendMessage(NotifyModelEnums.UP_NOTICE.getCode(), upNoticeNotify, List.of(orderTicketVo.getUserId()));
            });
        } catch (Exception e) {
            System.out.println("发送通知失败");
        }
    }

    /**
     * 构建下车消息并发送
     */
    @Async
    @Override
    public void buildDownNoticeNotify(List<OrderTicketVo> orderTicketVoList) {
        try {
            // 获取当前时间为下车时间
            String time = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");

            orderTicketVoList.parallelStream().forEach(orderTicketVo -> {
                DownNotify downNotify = new DownNotify();
                String downStation = remoteStationService.selectNameById(orderTicketVo.getToId());

                // 构建对象
                if (downStation != null) {
                    downNotify.setLocale(downStation);
                }
                downNotify.setTime(time);
                downNotify.setId(orderTicketVo.getId());
                downNotify.setPassengerName(orderTicketVo.getPassengerName());
                downNotify.setLicensePlate(orderTicketVo.getActualLicensePlate());
                // todo 其他字段

                // 发送消息
                self.sendMessage(NotifyModelEnums.DOWN_NOTICE.getCode(), downNotify, List.of(orderTicketVo.getUserId()));
            });
        } catch (Exception e) {
            System.out.println("发送通知失败");
        }
    }

    /**
     * 构建行程确认通知并发送
     */
    @Async
    @Override
    public void buildTripConfirmNotify(RemoteScheduleBo scheduleInfo) {
        try {
            scheduleInfo.getTicketIds().parallelStream().forEach(ticketId -> {
                OrderTicketVo orderTicketVo = orderTicketService.queryById(ticketId);
                TripConfirmNotify modelBy = createTripConfirmNotifyModel(orderTicketVo, scheduleInfo.getLicensePlate());
                self.sendMessage(NotifyModelEnums.TRIP_CONFIRM.getCode(), modelBy, List.of(orderTicketVo.getUserId()));
            });
        } catch (Exception e) {
            System.out.println("发送通知失败");
        }
    }

    /**
     * 构建计划变更通知
     */
    @Async
    @Override
    public void buildPlanChangeNotify(Long departPlanId) {
        try {
            OrderTicketBo orderTicketBo = new OrderTicketBo();
            orderTicketBo.setDepartPlanId(departPlanId);
            orderTicketService.queryList(orderTicketBo).parallelStream().forEach(vo -> {
                OrderTicketVo orderTicketVo = orderTicketService.queryById(vo.getId());
                TripConfirmNotify modelBy = createTripConfirmNotifyModel(orderTicketVo, null);
                self.sendMessage(NotifyModelEnums.TRIP_CONFIRM.getCode(), modelBy, List.of(orderTicketVo.getUserId()));
            });
        } catch (Exception e) {
            System.out.println("发送通知失败");
        }
    }

    @Async
    @Override
    public void buildWaitlistTicketNotify(Long orderId) {
        try{
            // 查询行程信息
            List<OrderTicketVo> orderTicketVos = orderTicketService.queryOrderTicket(orderId);
            orderTicketVos.parallelStream().forEach(orderTicketVo -> {
                // 构建模型
                TripConfirmNotify modelBy = createTripConfirmNotifyModel(orderTicketVo, null);
                // 发送消息
                self.sendMessage(NotifyModelEnums.TRIP_CONFIRM.getCode(), modelBy, List.of(orderTicketVo.getUserId()));
            });
        } catch (Exception e) {
            System.out.println("发送通知失败");
        }
    }

    /**
     * 发送消息
     */
    @Async
    @Override
    public void sendMessage(String code, NotifyModel model, List<Long> userIds) {
        try {
            // 通过code获取模板
            NotifyTemplateVo notifyTemplateVo = notifyTemplateService.getByCode(code);
            if (notifyTemplateVo.getTypeWx()) {
                sendWxMessage(notifyTemplateVo, model, userIds);
            }
        } catch (Exception e) {
            // todo
            System.out.println(e.getMessage());
        }

    }

    /**
     * 发送微信消息
     */
    private void sendWxMessage(NotifyTemplateVo notifyTemplateVo,
                               NotifyModel model,
                               List<Long> userIds) {
        // 获取微信模板
        WxTemplate wxTemplate = notifyTemplateConfigService.getTemplateByTypeAndCode(
            NotifyTemplateTypeConstant.WX,
            notifyTemplateVo.getCode(),
            WxTemplate.class);

        SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
        TemplateParserContext templateParserContext = new TemplateParserContext();

        // 构建微信消息
        WxMaSubscribeMessage build = WxMaSubscribeMessage.builder()
            .templateId(wxTemplate.getTemplateId())
            .build();
        // 使用el表达式将model中的数据填充到wxTemplate中
        if (wxTemplate.getUrl() != null) {
            // 使用时需要先去除首尾空格，防止微信模板中的数据类型不匹配
            build.setPage(spelExpressionParser
                .parseExpression(wxTemplate.getUrl().trim(), templateParserContext).getValue(model, String.class));
        }

        wxTemplate.getField().forEach(field -> {
            String fieldValue = spelExpressionParser.parseExpression(
                field.getFieldValue().trim(), templateParserContext).getValue(model, String.class);
            build.addData(new WxMaSubscribeMessage.MsgData(field.getFieldParam(), fieldValue));
        });

        userIds.forEach(userId -> {
            // 通过userId获取用户信息
            MemberUser memberUser = memberUserService.getById(userId);
            build.setToUser(memberUser.getOpenId());
            try {
                wxMaService.getMsgService().sendSubscribeMsg(build);
            } catch (WxErrorException e) {
                System.out.println(e.getMessage());
            }
        });
    }

    /**
     * 创建 TripConfirmNotify 模型
     */
    private TripConfirmNotify createTripConfirmNotifyModel(OrderTicketVo orderTicketVo, String licensePlate) {
        TripConfirmNotify modelBy = new TripConfirmNotify();

        String upStation = remoteStationService.selectNameById(orderTicketVo.getFromId());
        if (upStation != null) {
            modelBy.setUpStation(upStation);
        }

        String downStation = remoteStationService.selectNameById(orderTicketVo.getToId());
        if (downStation != null) {
            modelBy.setDownStation(downStation);
        }

        LocalDate dateAt = orderTicketVo.getDateAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalTime timeAt = orderTicketVo.getTimeAt().toLocalTime();
        LocalDateTime dateTime = LocalDateTime.of(dateAt, timeAt);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
        String formattedTime = dateTime.format(formatter);

        modelBy.setTime(formattedTime);
        modelBy.setId(orderTicketVo.getId());
        modelBy.setLicensePlate(licensePlate != null ?
            licensePlate : orderTicketVo.getLicensePlate() != null ?
            orderTicketVo.getLicensePlate() : "待排");

        return modelBy;
    }
}
