package yq.mall.notify.service;

import yq.mall.api.domain.bo.RemoteScheduleBo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.notify.baen.model.NotifyModel;

import java.util.List;

public interface ISendMessageService {

    /**
     * 构建上车通知
     */
    void buildUpNoticeNotify(List<OrderTicketVo> orderTicketVoList);

    /**
     * 构建下车通知
     */
    void buildDownNoticeNotify(List<OrderTicketVo> orderTicketVoList);

    /**
     * 构建行程确认通知
     */
    void buildTripConfirmNotify(RemoteScheduleBo scheduleInfo);

    void buildWaitlistTicketNotify(Long orderId);

    /**
     * 发送消息
     */
    void sendMessage(String code, NotifyModel model, List<Long> userIds);

    /**
     * 构建计划变更通知
     */
    void buildPlanChangeNotify(Long departPlanId);
}
