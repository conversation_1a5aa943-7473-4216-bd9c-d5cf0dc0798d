package yq.mall.aop;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import yq.common.satoken.utils.LoginHelper;
import yq.mall.service.IOrderLogService;

import javax.annotation.Resource;

/**
 * 订单日志注解
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
public class OrderLogAspect {

    @Resource
    private IOrderLogService orderLogService;

    /**
     * 订单日志注解
     */
    @AfterReturning("@annotation(orderLog)")
    public void orderLog(JoinPoint joinPoint, OrderLog orderLog) {
        try {

        } catch (Exception e) {
           log.error("订单日志记录错误{}", orderLog, e);
        } finally {

        }
    }

    /**
     * 获取用户类型
     * @return 用户类型
     */
    private static String getUserType() {
//        return ObjectUtil.defaultIfNull(LoginHelper.getUserType().getUserType(), yq.mall.domain.OrderLog.USER_TYPE_SYSTEM);
        return "";
    }

    /**
     * 获取用户编号
     * @return 用户编号
     */
    private static Long getUserId() {
        return ObjectUtil.defaultIfNull(LoginHelper.getUserId(), yq.mall.domain.OrderLog.USER_ID_SYSTEM);
    }

}
