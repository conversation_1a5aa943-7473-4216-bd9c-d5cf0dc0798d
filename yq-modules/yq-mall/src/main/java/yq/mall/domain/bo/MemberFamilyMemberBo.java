package yq.mall.domain.bo;


import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import yq.mall.domain.MemberFamilyMember;

/**
 * 家庭成员业务对象 member_family_member
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberFamilyMember.class, reverseConvertGenerate = false)
public class MemberFamilyMemberBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 家庭id
     */
    @NotNull(message = "家庭id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long familyId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 邀请的token
     */
    private String shareToken;


}
