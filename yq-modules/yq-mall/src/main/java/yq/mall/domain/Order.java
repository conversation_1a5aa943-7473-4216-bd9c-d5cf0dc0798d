package yq.mall.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import yq.mall.enums.order.OrderRefundStatusEnum;
import yq.mall.enums.refund.RefundStatusEnum;
import yq.mall.enums.order.OrderCancelTypeEnum;
import yq.mall.enums.order.OrderStatusEnum;
import yq.mall.enums.order.OrderTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "trade_order", autoResultMap = true)
public class Order implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // ~ 订单基本信息
    // ==================================================

    @TableId(value = "id")
    private Long id;

    /**
     * 订单类型
     * 枚举 {@link OrderTypeEnum}
     */
    private Integer type;

    /**
     * 商城用户id
     */
    private Long userId;

    /**
     * 用户ip
     */
    private String userIp;

    /**
     * 订单状态
     * 枚举 {@link OrderStatusEnum}
     */
    private Integer status;

    /**
     * 订单完成时间
     */
    private Date finishTime;

    /**
     * 订单取消时间
     */
    private Date cancelTime;

    /**
     * 取消类型
     * 枚举 {@link OrderCancelTypeEnum}
     */
    private Integer cancelType;

    /**
     * 记录是否被逻辑删除.
     * 0 - 代表存在;
     * 2 - 代表删除;
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    // ~ 价格 & 支付
    // ==================================================

    /**
     * 支付订单编号
     */
    private Long payOrderId;

    /**
     * 是否已支付.
     * true - 已经支付过;
     * false - 没有支付过;
     */
    private Boolean payStatus;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 支付渠道
     * 对应 PayChannelEnum 枚举
     */
    private String payChannelCode;

    /**
     * 商品原价
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    private BigDecimal payPrice;

    // ~ 售后信息
    // ==================================================

    /**
     * 售后状态
     * 枚举 {@link OrderRefundStatusEnum}
     */
    private Integer refundStatus;

    /**
     * 退款金额
     * 注意，退款并不会影响 {@link #payPrice} 实际支付金额
     * 也就说，一个订单最终产生多少金额的收入 = payPrice - refundPrice
     */
    private BigDecimal refundPrice;

    // ~ 营销信息
    // ==================================================

    /**
     * 优惠劵编号
     */
    private Long couponId;

    /**
     * 优惠劵减免金额
     */
    private BigDecimal couponPrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 扩展字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Extra extraInfo;


    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "@type")
    @JsonSubTypes({
        @JsonSubTypes.Type(value = WaitlistExtra.class, name = "waitlist"),
        @JsonSubTypes.Type(value = TicketPostPayExtra.class, name = "postPay"),
    })
    public static class Extra implements Serializable{

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class  WaitlistExtra extends Extra {

        /**
         * 候补订单标识
         */
        private Boolean waitlistFlag;

        /**
         * 候补状态-成功/失败/候补中
         */
        private Integer waitlistStatus;

        /**
         * 候补截止时间
         */
        private Integer deadline;

        /**
         * 候补成功时间
         */
        private String successTime;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class  TicketPostPayExtra extends Extra {

        /**
         * 补票的司机标识
         */
        private Long driverId;

        /**
         * 家长手机号
         */
        private String parentMobile;

        /**
         * 撤销的原因
         */
        private String reason;

        /**
         * 撤销的时间
         */
        private String cancelTime;
    }
}
