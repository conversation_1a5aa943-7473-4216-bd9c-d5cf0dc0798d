package yq.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;
import yq.mall.api.translation.constant.MallTransConstant;
import yq.mall.domain.MemberUser;
import yq.mall.domain.Order;
import yq.mall.enums.order.OrderCancelTypeEnum;
import yq.mall.enums.order.OrderStatusEnum;
import yq.mall.enums.order.OrderTypeEnum;
import yq.mall.enums.refund.RefundStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单视图对象
 * <AUTHOR> Suen
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Order.class)
public class OrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // ~ 订单基本信息
    // ==================================================

    @ExcelProperty(value = "编号")
    private Long id;

    /**
     * 订单类型
     * 枚举 {@link OrderTypeEnum}
     */
    @ExcelProperty(value = "订单类型")
    private Integer type;

    /**
     * 商城用户id
     */
    @ExcelProperty(value = "商城用户id")
    private Long userId;

    /**
     * 订单状态
     * 枚举 {@link OrderStatusEnum}
     */
    @ExcelProperty(value = "订单状态")
    private Integer status;

    /**
     * 订单完成时间
     */
    @ExcelProperty(value = "订单完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    /**
     * 订单取消时间
     */
    @ExcelProperty(value = "订单取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cancelTime;

    /**
     * 取消类型
     * 枚举 {@link OrderCancelTypeEnum}
     */
    @ExcelProperty(value = "取消类型")
    private Integer cancelType;


    // ~ 价格 & 支付
    // ==================================================

    /**
     * 支付订单编号
     */
    @ExcelProperty(value = "支付订单编号")
    private Long payOrderId;

    /**
     * 是否已支付.
     * true - 已经支付过;
     * false - 没有支付过;
     */
    @ExcelProperty(value = "是否已支付")
    private Boolean payStatus;

    /**
     * 付款时间
     */
    @ExcelProperty(value = "付款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 支付渠道
     * 对应 PayChannelEnum 枚举
     */
    @ExcelProperty(value = "支付渠道")
    private String payChannelCode;

    /**
     * 原价
     */
    @ExcelProperty(value = "原价")
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    @ExcelProperty(value = "应付金额")
    private BigDecimal payPrice;

    // ~ 售后信息
    // ==================================================

    /**
     * 售后状态
     * 枚举 {@link RefundStatusEnum}
     */
    @ExcelProperty(value = "售后状态")
    private Integer refundStatus;

    /**
     * 退款金额，单位：分
     * 注意，退款并不会影响 {@link #payPrice} 实际支付金额
     * 也就说，一个订单最终产生多少金额的收入 = payPrice - refundPrice
     */
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundPrice;

    // ~ 营销信息
    // ==================================================

    /**
     * 优惠劵编号
     */
    @ExcelProperty(value = "优惠劵编号")
    private Long couponId;

    /**
     * 优惠劵减免金额
     */
    @ExcelProperty(value = "优惠劵减免金额")
    private BigDecimal couponPrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    // 用户手机号
    @Translation(type = MallTransConstant.USER_MOBILE, mapper = "userId")
    private String mobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Order.Extra extraInfo;
}
