package yq.mall.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import yq.mall.enums.pay.PayOrderProfitSharingStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 支付退款单实体类
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@TableName("pay_refund")
public class PayRefund implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    private Long payOrderId;

    private Long merchantOrderId;
    private Long merchantRefundId;

    /**
     * 异步通知地址
     */
    private String notifyUrl;

    /**
     * 退款状态
     * ${@link yq.mall.enums.pay.PayRefundStatusEnum}
     */
    private Integer status;
    private BigDecimal payPrice;
    private BigDecimal refundAmount;
    private String reason;
    private Date successTime;

    // ~ 支付渠道回调信息

    /**
     * 冗余 ${@link PayOrder#getChannelOrderNo()}
     */
    private String channelOrderNo;
    /**
     * 渠道退款单号
     */
    private String channelCode;
    private String channelRefundNo;
    private String channelErrorCode;
    private String channelErrorMsg;
    private String rawData;
    private String userReceivedAccount;

    /**
     * 渠道手续费率
     */
    private BigDecimal channelFeeRate;

    /**
     * 渠道手续费金额
     */
    private BigDecimal channelFeeAmount;

    /**
     * 是否分账退回
     */
    private Boolean profitSharingReturn;

    /**
     * 分账退回状态
     * 枚举 {@link PayOrderProfitSharingStatusEnum}
     */
    private Integer profitSharingReturnStatus;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     *  接口提交时间
     */
    private Date applyTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 下一次重试时间
     */
    private LocalDateTime nextRetryTime;

}
