package yq.mall.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.domain.RefundRules;

import java.math.BigDecimal;

/**
 * 退款规则业务对象 bus_refund_rules
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RefundRules.class, reverseConvertGenerate = false)
public class RefundRulesBo extends BaseEntity {

    /**
     * 退款规则唯一标识
     */
    @NotNull(message = "退款规则唯一标识不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 发车前的时间
     */
    @NotNull(message = "发车前的时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal timeBefore;

    /**
     * 时间单位:  0-分钟  1-hour 小时  2-day 天
     */
    @NotBlank(message = "时间单位:  0-分钟  1-hour 小时  2-day 天不能为空", groups = {AddGroup.class, EditGroup.class})
    private String timeUnit;

    /**
     * 手续费 %
     */
    @NotNull(message = "手续费 %不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal serviceCharge;


}
