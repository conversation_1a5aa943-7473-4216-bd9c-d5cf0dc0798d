package yq.mall.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.mall.domain.NotifyTemplate;

@Data
@AutoMapper(target = NotifyTemplate.class)
public class NotifyTemplateBo {

    private Long id;

    /**
     * 模板类型
     */
    private String type;

    /**
     * 模板代码
     */
    private String code;

    /**
     * 微信状态
     */
    private Boolean typeWx;
}
