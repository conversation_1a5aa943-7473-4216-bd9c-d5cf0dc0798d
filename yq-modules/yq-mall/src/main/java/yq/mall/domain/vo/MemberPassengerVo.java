package yq.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.bus.api.translation.SchoolNameTranslationImpl;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.Translation;
import yq.mall.domain.MemberPassenger;

import java.io.Serial;
import java.io.Serializable;

/**
 * member_passenger管理视图对象
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberPassenger.class)
public class MemberPassengerVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    private Long userId;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String mobile;

    /**
     * 乘车人姓名
     */
    @ExcelProperty(value = "乘车人姓名")
    private String name;

    /**
     * 乘车人身份证号
     */
    @ExcelProperty(value = "乘车人身份证号")
    private String idCard;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 乘车人学校
     */
    private String schoolId;

    /**
     * 乘车人学校
     */
    @Translation(type = BusTransConstant.SCHOOL_ID_TO_NAME, mapper = "schoolId")
    @ExcelProperty(value = "乘车人学校")
    private String schoolName;

    /**
     * 乘车人地址
     */
    @ExcelProperty(value = "乘车人地址")
    private String address;
    @ExcelProperty(value = "乘车人地名")
    private String addressName;
    @ExcelProperty(value = "乘车人地址纬度")
    private String addressLatitude;
    @ExcelProperty(value = "乘车人地址经度")
    private String addressLongitude;
    @ExcelProperty(value = "默认标识")
    private Boolean defaultFlag;
    @ExcelProperty(value = "年级")
    private String grade;
    @ExcelProperty(value = "返校日期")
    private String backDate;


}
