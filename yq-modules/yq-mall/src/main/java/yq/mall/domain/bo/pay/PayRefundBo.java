package yq.mall.domain.bo.pay;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.domain.PayOrder;
import yq.mall.domain.PayRefund;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付退款记录Body
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PayRefund.class, reverseConvertGenerate = false)
public class PayRefundBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long payOrderId;

    private Long merchantOrderId;
    private Long merchantRefundId;

    /**
     * 异步通知地址
     */
    private String notifyUrl;

    /**
     * 退款状态
     * ${@link yq.mall.enums.pay.PayRefundStatusEnum}
     */
    private Integer status;
    private BigDecimal payPrice;
    private BigDecimal refundAmount;
    private String reason;
    private Date successTime;

    // ~ 支付渠道回调信息

    /**
     * 冗余 ${@link PayOrder#getChannelOrderNo()}
     */
    private String channelOrderNo;
    /**
     * 渠道退款单号
     */
    private String channelCode;
    private String channelRefundNo;
    private String channelErrorCode;
    private String channelErrorMsg;
    private String rawData;
    private String userReceivedAccount;

    /**
     * 渠道手续费率
     */
    private BigDecimal channelFeeRate;

    /**
     * 渠道手续费金额
     */
    private BigDecimal channelFeeAmount;

}
