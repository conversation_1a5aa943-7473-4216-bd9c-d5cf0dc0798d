package yq.mall.domain.vo;

import lombok.Data;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Date;

@Data
public class TicketCountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发车日期
     */
    private Date dateAt;

    /**
     * 未排班数量
     */
    private Integer count;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 学校名称
     */
    @Translation(type = BusTransConstant.SCHOOL_ID_TO_NAME, mapper = "schoolId")
    private String schoolName;
}
