package yq.mall.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.enums.refund.RefundOperateTypeEnum;

import java.io.Serial;

/**
 * 退款单日志
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("refund_log")
public class RefundLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;
    /**
     * 用户编号
     * 关联 1：sys_user 的 id 字段
     * 关联 2：member_user 的 id 字段
     */
    private Long userId;

    /**
    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 操作前状态
     */
    private Integer beforeStatus;
    /**
     * 操作后状态
     */
    private Integer afterStatus;

    /**
     * 操作类型
     * 枚举 {@link RefundOperateTypeEnum}
     */
    private Integer operateType;

    /**
     * 操作明细
     */
    private String content;

    /**
     * 记录是否被逻辑删除.
     * 0 - 代表存在;
     * 2 - 代表删除;
     */
    @TableLogic
    private String delFlag;

}
