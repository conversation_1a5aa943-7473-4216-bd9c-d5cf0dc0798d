package yq.mall.domain.bo;
import yq.mall.domain.OrderWaitlistTicket;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 候补行程业务对象 order_waitlist_ticket
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderWaitlistTicket.class, reverseConvertGenerate = false)
public class OrderWaitlistTicketBo extends BaseEntity {

    /**
     * 订单号
     */
    private Long orderId;
    private Long id;

    private Long departPlanId;
    private Long schoolId;
    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;
}
