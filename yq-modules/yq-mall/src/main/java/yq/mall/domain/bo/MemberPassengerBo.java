package yq.mall.domain.bo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.domain.MemberPassenger;

import java.io.Serial;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberPassenger.class, reverseConvertGenerate = false)
public class MemberPassengerBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    @NotNull(message = "会员不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    @NotNull(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mobile;

    /**
     * 乘车人姓名
     */
    @NotNull(message = "乘车人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 乘车人身份证号
     */
    @NotNull(message = "乘车人身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCard;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 乘车人学校
     */
    private String schoolId;

    /**
     * 乘车人地址
     */
    private String address;
    private String addressName;
    private String addressLatitude;
    private String addressLongitude;
    private Boolean defaultFlag;
    private String grade;
    private String backDate;


}
