package yq.mall.domain.vo.promotion;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Builder;
import lombok.Data;
import yq.common.translation.annotation.Translation;
import yq.common.translation.constant.TransConstant;
import yq.mall.domain.PromotionCouponTemplate;
import yq.mall.enums.promotion.CouponScopeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PromotionCouponTemplate.class)
public class PromotionCouponTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     * 枚举 {@link yq.mall.enums.promotion.CouponTypeEnum}
     */
    private Integer type;

    /**
     * 优惠券描述
     */
    private String description;

    /**
     * 优惠券状态 - 是否开启
     */
    private Boolean enable;


    // ~ 领取规则
    // ==================================

    /**
     * 优惠券发放数量
     */
    private Integer quantityLimit;

    /**
     * 每人领取数量限制,必须大于0
     */
    private Integer takeLimit;

    /**
     * 领取方式
     * 枚举 {@link yq.mall.enums.promotion.CouponTakeTypeEnum}
     */
    private Integer takeType;


    // ~ 使用规则
    // ==================================

    /**
     * 满减或折扣的最低消费金额
     */
    private BigDecimal minConsume;

    /**
     * 折扣最大抵扣金额(满减券为面值)
     */
    private BigDecimal maxDiscount;

    /**
     * 适用范围
     * 枚举 {@link CouponScopeEnum}
     */
    private Integer useScope;

    /**
     * 适用范围的学校id
     */
    private List<Long> schoolIds;

    private List<String> schoolNames;

    /**
     * 生效日期类型
     * 枚举 {@link yq.mall.enums.promotion.CouponValidTypeEnum}
     */
    private Integer validType;

    /**
     * 固定日期 - 有效期起
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#DATE}时，有效
     */
    private LocalDateTime validDateFrom;
    /**
     * 固定日期 - 有效期止
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#DATE}时，有效
     */
    private LocalDateTime validDateTo;

    /**
     * 领取日期 - 有效期起
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#TERM}时，有效
     */
    private Integer validTermStart;

    /**
     * 领取日期 - 有效期止
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#TERM}时，有效
     */
    private Integer validTermEnd;

    // ~ 统计信息
    // ==================================

    /**
     * 已领取数量
     */
    private Integer takeCount;

    /**
     * 已使用数量
     */
    private Integer useCount;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

}
