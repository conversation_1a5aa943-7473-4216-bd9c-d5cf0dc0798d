package yq.mall.domain;

import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 家庭对象 member_family
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("member_family")
public class MemberFamily extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 家庭id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 家庭名称
     */
    private String name;

    /**
     * 所属人（群主）
     */
    private Long ownerId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
