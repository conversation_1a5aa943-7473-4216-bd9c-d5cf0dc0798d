package yq.mall.domain.bo.pay;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.domain.PayOrder;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付订单Body
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PayOrder.class, reverseConvertGenerate = false)
public class PayOrderBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 商户订单号
     */
    @NotBlank(message = "商户订单号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merchantOrderId;

    /**
     * 商品标题
     */
    private String subject;

    /**
     * 通知地址
     */
    private String notifyUrl;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "支付金额不能小于0", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal totalAmount;

    /**
     * 支付状态
     */
    private Integer status;

    /**
     * 用户ip
     */
    private String userIp;

    /**
     * 订单失效时间
     */
    private Date expireTime;

    /**
     * 订单支付成功时间
     */
    private Date successTime;

    // ~ 退款相关
    // ==================================================

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    // ~ 渠道相关
    // ==================================================

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道手续费
     */
    private BigDecimal channelFeeRate;

    /**
     * 渠道手续费金额
     */
    private BigDecimal channelFeePrice;

    /**
     * 渠道用户编号
     */
    private String channelUserId;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

}
