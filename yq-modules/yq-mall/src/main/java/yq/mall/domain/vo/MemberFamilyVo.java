package yq.mall.domain.vo;
import yq.mall.domain.MemberFamily;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 家庭视图对象 member_family
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberFamily.class)
public class MemberFamilyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 家庭id
     */
    @ExcelProperty(value = "家庭id")
    private Long id;

    /**
     * 家庭名称
     */
    @ExcelProperty(value = "家庭名称")
    private String name;

    /**
     * 所属人（群主）
     */
    @ExcelProperty(value = "所属人", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "群=主")
    private Long ownerId;


}
