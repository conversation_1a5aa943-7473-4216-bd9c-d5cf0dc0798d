package yq.mall.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通知模板
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("notify_template")
public class NotifyTemplate {

    private Long id;

    /**
     * 模板类型
     */
    private String type;

    /**
     * 模板代码
     */
    private String code;

    /**
     * 微信状态
     */
    private Boolean typeWx;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    private String delFlag;
}
