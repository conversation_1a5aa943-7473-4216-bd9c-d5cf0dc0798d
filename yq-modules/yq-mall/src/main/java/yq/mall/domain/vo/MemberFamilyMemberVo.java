package yq.mall.domain.vo;
import yq.common.translation.annotation.Translation;
import yq.mall.api.translation.constant.MallTransConstant;
import yq.mall.domain.MemberFamilyMember;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;


/**
 * 家庭成员视图对象 member_family_member
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberFamilyMember.class)
public class MemberFamilyMemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 家庭id
     */
    @ExcelProperty(value = "家庭id")
    private Long familyId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String memo;

    /**
     * 手机号
     */
    @Translation(type = MallTransConstant.USER_MOBILE, mapper = "userId")
    private String mobile;

    /**
     * 1群主 0成员
     */
    private String ownerFlag;

}
