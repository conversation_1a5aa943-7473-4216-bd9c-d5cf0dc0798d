package yq.mall.domain.vo.order;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class OrderPriceVo {

    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 优惠券金额
     */
    private BigDecimal couponPrice;

    /**
     * 实付金额
     */
    private BigDecimal payPrice;


}
