package yq.mall.domain.convert;

import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import yq.mall.api.domain.vo.RemoteOrderTicketVo;
import yq.mall.domain.vo.OrderTicketVo;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderTicketVoConvert extends BaseMapper<OrderTicketVo, RemoteOrderTicketVo> {
}
