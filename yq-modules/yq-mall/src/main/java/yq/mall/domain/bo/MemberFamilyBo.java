package yq.mall.domain.bo;


import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import yq.mall.domain.MemberFamily;

/**
 * 家庭业务对象 member_family
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberFamily.class, reverseConvertGenerate = false)
public class MemberFamilyBo extends BaseEntity {

    /**
     * 家庭id
     */
    @NotNull(message = "家庭id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 家庭名称
     */
    private String name;

    /**
     * 所属人（群主）
     */
    @NotNull(message = "所属人（群主）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long ownerId;


}
