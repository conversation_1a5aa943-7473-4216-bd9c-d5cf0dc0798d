package yq.mall.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import yq.mall.domain.MemberUser;

import java.io.Serial;

/**
 * member_user业务对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberUser.class, reverseConvertGenerate = false)
public class MemberUserBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mobile;

    /**
     * 微信公众号openId
     */
    @NotBlank(message = "微信公众号openId", groups = { AddGroup.class, EditGroup.class })
    private String openId;

    /**
     * 状态
     */
    private String status;

}
