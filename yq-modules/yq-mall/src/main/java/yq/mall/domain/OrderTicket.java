package yq.mall.domain;


import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.mall.enums.refund.RefundStatusEnum;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

import java.io.Serial;

/**
 * 行程对象 ticket
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("order_ticket")
public class OrderTicket extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 发车日期
     */
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 发车计划id
     */
    private Long departPlanId;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 出发站点
     */
    private Long fromId;

    /**
     * 下车站点
     */
    private Long toId;

    /**
     * 状态
     */
    private String status;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车辆id
     */
    private Long vehicleId;

    /**
     * 排班id
     */
    private Long scheduleId;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 实际乘坐车辆
     */
    private String actualLicensePlate;

    /**
     * 实际乘坐车辆id
     */
    private Long actualVehicleId;

    /**
     * 上车时间
     */
    private Date boardingTime;

    /**
     * 核验排班id
     */
    private Long verifyScheduleId;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    @TableLogic
    private String delFlag;

    // ~ 价格 & 支付
    // ==================================================

    /**
     * 商品原价（单）
     */
    private BigDecimal price;

    /**
     * 应付金额（总）
     */
    private BigDecimal payPrice;

    // ~ 营销信息
    // ==================================================

    /**
     * 优惠劵减免金额
     */
    private BigDecimal couponPrice;

    // ~ 售后信息
    // ==================================================
//
//    /**
//     * 售后单编号
//     * 关联 {@link Refund#getId()} 字段
//     */
//    private Long refundId;

    /**
     * 售后状态
     * 枚举 {@link RefundStatusEnum}
     */
    private Integer refundStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 订单支付状态
     */
    private Boolean payStatus;

    /**
     * 入学时间
     */
    private String backDate;

    /**
     * 是否跨线
     */
    private Boolean crossLineFlag;

    /**
     * 后付费标识
     */
    private Boolean postPayFlag;
}
