package yq.mall.domain.vo.pay;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.mall.domain.PayProfitSharing;
import yq.mall.enums.pay.PayChannelEnum;
import yq.mall.enums.pay.PayProfitSharingStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付分账视图对象
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PayProfitSharing.class)
public class PayProfitSharingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 支付订单id
     */
    @ExcelProperty(value = "支付订单id")
    private Long payOrderId;

    /**
     * 分账比例
     */
    @ExcelProperty(value = "分账比例")
    private BigDecimal rate;

    /**
     * 支付单金额
     */
    @ExcelProperty(value = "支付单金额")
    private BigDecimal amount;

    /**
     * 分账金额
     */
    @ExcelProperty(value = "分账金额")
    private BigDecimal profitSharingAmount;

    /**
     * 退回金额
     */
    @ExcelProperty(value = "退回金额")
    private BigDecimal profitSharingReturnAmount;

    /**
     * 分账状态
     */
    @ExcelProperty(value = "分账状态")
    private Long status;

    /**
     * 成功时间
     */
    @ExcelProperty(value = "成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime successTime;

    /**
     * 完结时间
     */
    @ExcelProperty(value = "完结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    /**
     * 支付渠道
     */
    @ExcelProperty(value = "支付渠道")
    private String channelCode;

    /**
     * 渠道分账单号
     */
    @ExcelProperty(value = "渠道分账单号")
    private String channelOrderNo;

    /**
     * 渠道错误信息
     */
    @ExcelProperty(value = "渠道错误信息")
    private String channelErrorMsg;

    /**
     * 分账接收方类型
     */
    @ExcelProperty(value = "分账接收方类型")
    private String type;

    /**
     * 分账接收方名称
     */
    @ExcelProperty(value = "分账接收方名称")
    private String name;

    /**
     * 分账接收方帐号
     */
    @ExcelProperty(value = "分账接收方帐号")
    private String account;

    /**
     * 与分账方的关系类型
     */
    @ExcelProperty(value = "与分账方的关系类型")
    private String relationType;

    /**
     * 渠道分账明细id
     */
    @ExcelProperty(value = "渠道分账明细id")
    private String channelDetailId;

    /**
     * 分账接收方分账原因
     */
    @ExcelProperty(value = "分账接收方分账原因")
    private String description;

    /**
     * 资金是否已解冻
     */
    @ExcelProperty(value = "资金是否已解冻")
    private Boolean unFreeze;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 下一次重试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime nextRetryTime;

}
