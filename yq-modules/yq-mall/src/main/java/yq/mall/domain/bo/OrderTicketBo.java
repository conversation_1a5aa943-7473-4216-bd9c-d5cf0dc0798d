package yq.mall.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.mall.domain.OrderTicket;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import yq.mall.domain.Refund;
import yq.mall.enums.refund.RefundStatusEnum;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

/**
 * 行程业务对象 ticket
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderTicket.class, reverseConvertGenerate = false)
public class OrderTicketBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 发车日期
     */
    @NotNull(message = "发车日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 发车时间
     */
    @NotNull(message = "发车时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Time timeAt;

    /**
     * 发车计划id
     */
    @NotNull(message = "发车计划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departPlanId;

    /**
     * 学校id
     */
    @NotNull(message = "学校id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 乘客姓名
     */
    @NotBlank(message = "乘客姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String passengerName;

    /**
     * 乘客手机号
     */
    @NotBlank(message = "乘客手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String passengerMobile;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String validCode;

    /**
     * 出发站点
     */
    @NotNull(message = "出发站点不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fromId;

    /**
     * 下车站点
     */
    @NotNull(message = "下车站点不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long toId;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { EditGroup.class })
    private Long driverId;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空", groups = { EditGroup.class })
    private String licensePlate;

    /**
     * 车辆id
     */
    @NotNull(message = "车辆id不能为空", groups = { EditGroup.class })
    private Long vehicleId;

    /**
     * 排班id
     */
    @NotNull(message = "排班id不能为空", groups = { EditGroup.class })
    private Long scheduleId;

    /**
     * 线路方向
     */
    @NotBlank(message = "线路方向不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lineKind;

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 实际乘坐车辆
     */
    @NotBlank(message = "实际乘坐车辆不能为空", groups = { EditGroup.class })
    private String actualLicensePlate;

    /**
     * 实际乘坐车辆id
     */
    @NotNull(message = "实际乘坐车辆id不能为空", groups = { EditGroup.class })
    private Long actualVehicleId;

    /**
     * 上车时间
     */
    @NotNull(message = "上车时间不能为空", groups = { EditGroup.class })
    private Date boardingTime;

    /**
     * 入学时间
     */
    private String backDate;

    /**
     * 核验排班id
     */
    private Long verifyScheduleId;

    /**
     * 商品原价（单）
     */
    private BigDecimal price;

    /**
     * 应付金额（总）
     */
    private BigDecimal payPrice;

    /**
     * 优惠劵减免金额
     */
    private BigDecimal couponPrice;

    /**
     * 售后单编号
     * 关联 {@link Refund#getId()} 字段
     */
    private Long refundId;

    /**
     * 售后状态
     * 枚举 {@link RefundStatusEnum}
     */
    private Integer refundStatus;

    /**
     * 是否跨线
     */
    private Boolean crossLineFlag;

    /**
     * 是否后付费
     */
    private Boolean postPayFlag;
}
