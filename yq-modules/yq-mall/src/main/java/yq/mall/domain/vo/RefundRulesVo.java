package yq.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import yq.mall.domain.RefundRules;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 退款规则视图对象 bus_refund_rules
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RefundRules.class)
public class RefundRulesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退款规则唯一标识
     */
    @ExcelProperty(value = "退款规则唯一标识")
    private Long id;

    /**
     * 发车前的时间
     */
    @ExcelProperty(value = "发车前的时间")
    private BigDecimal timeBefore;

    /**
     * 时间单位:  0-分钟  1-hour 小时  2-day 天
     */
    @ExcelProperty(value = "时间单位:  0-分钟  1-hour 小时  2-day 天", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "time_unit")
    private String timeUnit;

    /**
     * 手续费 %
     */
    @ExcelProperty(value = "手续费 %")
    private BigDecimal serviceCharge;

    /**
     * 与当前时间相加转换后的时间戳
     */
    private Long refundTimestamp;


}
