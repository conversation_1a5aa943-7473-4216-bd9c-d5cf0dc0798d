package yq.mall.enums;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import yq.mall.constant.NotifyTemplateTypeConstant;
import yq.mall.notify.baen.wx.WxTemplate;

/**
 * 模板类型枚举
 */
@Getter
public enum TemplateTypeEnums {
    // 微信
    WX(NotifyTemplateTypeConstant.WX, WxTemplate.class);

    private final String type;
    private final Class<?> clazz;

    TemplateTypeEnums(String type, Class<?> clazz) {
        this.type = type;
        this.clazz = clazz;
    }

    public static String getTemplateByType(String type) {
        for (TemplateTypeEnums templateTypeEnums : TemplateTypeEnums.values()) {
            if (templateTypeEnums.getType().equals(type)) {
                try {
                    // 创建模板类的实例
                    Object instance = templateTypeEnums.getClazz().getDeclaredConstructor().newInstance();
                    // 使用 Jackson 将实例转为 JSON 字符串
                    ObjectMapper objectMapper = new ObjectMapper();
                    return objectMapper.writeValueAsString(instance);
                } catch (Exception e) {
                    return "{}";
                }
            }
        }
        return "{}";
    }
}
