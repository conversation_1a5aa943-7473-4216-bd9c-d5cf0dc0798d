package yq.mall.controller.client.order.vo;

import lombok.Data;
import yq.mall.domain.RefundRules;
import yq.mall.domain.vo.RefundRulesVo;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.List;

/**
 * 订单退票信息
 * <AUTHOR>
 */
@Data
public class ClientRefundTicketInfoVo {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 候补订单标记
     */
    private Boolean waitlistFlag;

    /**
     * 退票信息
     */
    private List<TicketRefund> ticketRefundList;

    @Data
    public static class TicketRefund {

        private Long ticketId;
        private String passengerName;
        private String passengerMobile;
        private String dateAt;
        private String timeAt;

        /**
         * 是否可退票
         */
        private Boolean canRefund;
        /**
         * 拒绝退款原因
         */
        private String refusingReason;

        /**
         * 支付金额
         */
        private BigDecimal payPrice;

        /**
         * 退票手续费
         */
        private BigDecimal refundFee;
        /**
         * 退票规则
         */
        private RefundRulesVo refundRules;
    }


}
