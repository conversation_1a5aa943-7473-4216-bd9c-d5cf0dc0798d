package yq.mall.controller.driver.member;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.common.core.domain.R;
import yq.common.core.utils.ValidateUtils;
import yq.mall.controller.driver.member.vo.DriverSearchPassengerByMobileVo;
import yq.mall.domain.vo.MemberPassengerVo;
import yq.mall.domain.vo.MemberUserVo;
import yq.mall.service.IMemberPassengerService;
import yq.mall.service.IMemberUserService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 乘客
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/passenger")
public class DriverMemberController {

    @Resource
    private IMemberPassengerService passengerService;

    @Resource
    private IMemberUserService memberUserService;

    /**
     * 根据手机号获取用户下的乘车人
     */
    @GetMapping("/getPassengerByMobile")
    public R<DriverSearchPassengerByMobileVo> getPassengerByMobile(String mobile) {
        MemberUserVo byMobile = memberUserService.getByMobile(mobile);
        // 如果用户不存在 返回空
        if (byMobile == null) {
            return R.ok(new DriverSearchPassengerByMobileVo());
        }
        List<MemberPassengerVo> passengerByUserId = passengerService.listPassengerByUser(byMobile.getId());
        return R.ok(DriverSearchPassengerByMobileVo.builder()
            .userFlag(true)
            .passengers(passengerByUserId)
            .build());
    }
}
