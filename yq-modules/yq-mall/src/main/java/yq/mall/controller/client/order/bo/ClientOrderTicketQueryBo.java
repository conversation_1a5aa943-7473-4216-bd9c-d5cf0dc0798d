package yq.mall.controller.client.order.bo;

import lombok.Data;
import yq.mall.enums.TicketStatusEnum;

@Data
public class ClientOrderTicketQueryBo {

    private String status;

    /**
     * 所有
     */
    public static final String STATUS_ALL = "ALL";

    /**
     * 最近
     */
    public static final String STATUS_RECENT = "RECENT";

    /**
     * 待出行
     */
    public static final String STATUS_UN_USE = "UN_USE";

    /**
     * 出行中
     */
    public static final String STATUS_IN_USE = "IN_USE";

    /**
     * 已完成
     */
    public static final String STATUS_COMPLETED = "COMPLETED";

    /**
     * 获取状态
     */
    public String[] getStatusList() {
        return switch (status) {
            case STATUS_ALL -> null;
            case STATUS_RECENT -> new String[]{
                TicketStatusEnum.PENDING_VERIFICATION.getCode(),
                TicketStatusEnum.VERIFIED.getCode(),
                TicketStatusEnum.NOT_SCHEDULED.getCode()};
            case STATUS_UN_USE -> new String[]{TicketStatusEnum.PENDING_VERIFICATION.getCode()};
            case STATUS_IN_USE -> new String[]{TicketStatusEnum.VERIFIED.getCode()};
            case STATUS_COMPLETED -> new String[]{TicketStatusEnum.COMPLETED.getCode()};
            default -> null;
        };
    }


}
