package yq.mall.controller.admin.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author：fish
 * @date：2024/11/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FundFlowBillVo {
    /**
     * 发车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate dateAt;

    /**
     * 订单收入
     */
    private BigDecimal orderAmount = BigDecimal.ZERO;

    /**
     * 订单退款
     */
    private BigDecimal refundAmount = BigDecimal.ZERO;

    /**
     * 微信手续费
     */
    private BigDecimal channelFeeAmount = BigDecimal.ZERO;

    /**
     * 微信手续费退回
     */
    private BigDecimal channelFeeRefundAmount = BigDecimal.ZERO;

    /**
     * 平台服务费
     */
    private BigDecimal profitSharingAmount = BigDecimal.ZERO;

    /**
     * 平台服务费退回
     */
    private BigDecimal profitSharingReturnAmount = BigDecimal.ZERO;

    /**
     * 营收金额
     */
    private BigDecimal revenueAmount = BigDecimal.ZERO;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount = BigDecimal.ZERO;

    /**
     * 可提现金额
     */
    private BigDecimal withdrawAmount = BigDecimal.ZERO;


}
