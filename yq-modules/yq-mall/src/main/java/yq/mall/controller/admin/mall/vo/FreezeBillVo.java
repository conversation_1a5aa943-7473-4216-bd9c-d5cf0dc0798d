package yq.mall.controller.admin.mall.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author：fish
 * @date：2025/1/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreezeBillVo {

    /**
     * 订单id
     */
    private Long merchantOrderId;

    /**
     * 实付金额
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 微信手续费
     */
    private BigDecimal channelFeeAmount = BigDecimal.ZERO;

    /**
     * 支付成功时间
     */
    private LocalDate successTime;

    /**
     * 分账完结时间
     */
    private LocalDate finishTime;
}
