package yq.mall.controller.client.order.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import yq.mall.domain.vo.RefundVo;
import yq.mall.domain.vo.pay.PayRefundVo;

import java.util.List;

/**
 * @author：fish
 * @date：2024/10/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientRefundVo {

   private List<PayRefundVo>  payRefundList;

   private List<RefundVo> refundList;
}
