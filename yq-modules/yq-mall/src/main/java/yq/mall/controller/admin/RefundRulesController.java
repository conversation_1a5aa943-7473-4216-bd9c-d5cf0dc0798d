package yq.mall.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.excel.utils.ExcelUtil;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.log.enums.BusinessType;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.web.core.BaseController;
import yq.mall.domain.bo.RefundRulesBo;
import yq.mall.domain.vo.RefundRulesVo;
import yq.mall.service.IRefundRulesService;

import java.util.List;

/**
 * 退款规则
 * 前端访问路由地址为:/mall/refundRules
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/refundRules")
public class RefundRulesController extends BaseController {

    private final IRefundRulesService refundRulesService;

/**
 * 查询退款规则列表
 */
@SaCheckPermission("mall:refundRules:list")
@GetMapping("/list")
    public TableDataInfo<RefundRulesVo> list(RefundRulesBo bo, PageQuery pageQuery) {
        return refundRulesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出退款规则列表
     */
    @SaCheckPermission("mall:refundRules:export")
    @Log(title = "退款规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(RefundRulesBo bo, HttpServletResponse response) {
        List<RefundRulesVo> list = refundRulesService.queryList(bo);
        ExcelUtil.exportExcel(list, "退款规则", RefundRulesVo.class, response);
    }

    /**
     * 获取退款规则详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mall:refundRules:query")
    @GetMapping("/{id}")
    public R<RefundRulesVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(refundRulesService.queryById(id));
    }

    /**
     * 新增退款规则
     */
    @SaCheckPermission("mall:refundRules:add")
    @Log(title = "退款规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RefundRulesBo bo) {
        return toAjax(refundRulesService.insertByBo(bo));
    }

    /**
     * 修改退款规则
     */
    @SaCheckPermission("mall:refundRules:edit")
    @Log(title = "退款规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody RefundRulesBo bo) {
        return toAjax(refundRulesService.updateByBo(bo));
    }

    /**
     * 删除退款规则
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mall:refundRules:remove")
    @Log(title = "退款规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(refundRulesService.deleteWithValidByIds(List.of(ids), true));
    }
}
