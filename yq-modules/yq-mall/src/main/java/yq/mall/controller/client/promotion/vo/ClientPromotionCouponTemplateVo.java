package yq.mall.controller.client.promotion.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.mall.domain.PromotionCouponTemplate;
import yq.mall.enums.promotion.CouponScopeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = PromotionCouponTemplate.class)
public class ClientPromotionCouponTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     * 枚举 {@link yq.mall.enums.promotion.CouponTypeEnum}
     */
    private Integer type;

    /**
     * 优惠券描述
     */
    private String description;

    /**
     * 优惠券状态 - 是否开启
     */
    private Boolean enable;


    // ~ 领取规则
    // ==================================

    /**
     * 每人领取数量限制,必须大于0
     */
    private Integer takeLimit;

    /**
     * 领取方式
     * 枚举 {@link yq.mall.enums.promotion.CouponTakeTypeEnum}
     */
    private Integer takeType;


    // ~ 使用规则
    // ==================================

    /**
     * 满减或折扣的最低消费金额
     */
    private BigDecimal minConsume;

    /**
     * 折扣最大抵扣金额(满减券为面值)
     */
    private BigDecimal maxDiscount;

    /**
     * 适用范围
     * 枚举 {@link CouponScopeEnum}
     */
    private Integer useScope;

    /**
     * 适用范围的学校id
     */
    private List<Long> schoolIds;

    /**
     * 适用范围的学校名称
     */
    private List<String> schoolNames;

    /**
     * 生效日期类型
     * 枚举 {@link yq.mall.enums.promotion.CouponValidTypeEnum}
     */
    private Integer validType;

    /**
     * 固定日期 - 有效期起
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#DATE}时，有效
     */
    private LocalDateTime validDateFrom;
    /**
     * 固定日期 - 有效期止
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#DATE}时，有效
     */
    private LocalDateTime validDateTo;

    /**
     * 领取日期 - 有效期起
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#TERM}时，有效
     */
    private Integer validTermStart;

    /**
     * 领取日期 - 有效期止
     * 当 {@link #validType} 为{@link yq.mall.enums.promotion.CouponValidTypeEnum#TERM}时，有效
     */
    private Integer validTermEnd;

    /**
     * 优惠券发放数量
     */
    private Integer quantityLimit;

    /**
     * 已领取数量
     */
    private Integer takeCount;

    /**
     * 可领取的数量
     */
    private Integer canTakeCount;
}
