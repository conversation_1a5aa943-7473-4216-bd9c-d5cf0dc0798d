package yq.mall.controller.client.member;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.common.core.domain.R;
import yq.common.core.exception.AlertException;
import yq.common.satoken.utils.LoginHelper;
import yq.mall.domain.vo.MemberUserVo;

import javax.annotation.Resource;

/**
 * 会员用户管理Controller
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/client/member")
public class ClientMemberUserController {

    @Resource
    private  WxMaService wxMaService;

    @GetMapping("/info")
    public R<MemberUserVo> getInfo() {
        var vo = new MemberUserVo();
        vo.setId(LoginHelper.getUserId());
        vo.setMobile(LoginHelper.getUsername());

        return R.ok(vo);
    }

    @GetMapping("/getMobile")
    public R<String> getPhoneNumber(String mobileCode){
        var userService = wxMaService.getUserService();
        WxMaPhoneNumberInfo phoneNoInfo;
        try {
            phoneNoInfo = userService.getPhoneNoInfo(mobileCode);
        } catch (WxErrorException e) {
            throw new AlertException("获取手机号码失败");
        }
        return R.ok("ok",phoneNoInfo.getPhoneNumber());
    }


}
