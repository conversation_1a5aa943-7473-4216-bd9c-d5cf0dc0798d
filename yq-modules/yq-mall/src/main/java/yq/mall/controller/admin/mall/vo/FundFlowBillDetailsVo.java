package yq.mall.controller.admin.mall.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
/**
 * @author：fish
 * @date：2024/11/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class FundFlowBillDetailsVo {

    /**
     * 交易日期
     */
    @ExcelProperty(value = "交易时间")
    private String tradeTime;

    @ExcelProperty(value = "订单号")
    private Long orderId;

    /**
     * payOrderId
     */
    @ExcelProperty(value = "支付订单号")
    private Long payOrderId;

    @ExcelProperty(value = "渠道交易号")
    private String channelOrderNo;

    @ExcelProperty(value = "交易类型")
    private String tradeType;

    @ExcelProperty(value = "交易金额")
    private BigDecimal amount = BigDecimal.ZERO;

    @ExcelProperty(value = "渠道交易手续费")
    private BigDecimal channelFeeAmount = BigDecimal.ZERO;

}
