package yq.mall.controller.driver.member.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import yq.mall.domain.vo.MemberPassengerVo;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverSearchPassengerByMobileVo {
    /**
     * 是否存在用户
     */
    private Boolean userFlag = false;

    /**
     * 乘车人列表
     */
    private List<MemberPassengerVo> passengers;
}
