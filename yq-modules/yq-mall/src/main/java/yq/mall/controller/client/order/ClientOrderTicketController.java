package yq.mall.controller.client.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.bus.api.RemoteDepartPlanService;
import yq.bus.api.RemoteDriverService;
import yq.bus.api.RemoteLineScheduleService;
import yq.bus.api.RemoteLineTemplateService;
import yq.common.core.domain.R;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.web.core.BaseController;
import yq.mall.constant.ClientConfigConstant;
import yq.mall.controller.client.order.bo.ClientOrderTicketQueryBo;
import yq.mall.controller.client.order.vo.QuickTicketMessageVo;
import yq.mall.controller.client.order.vo.TicketInfoVo;
import yq.mall.domain.vo.MemberFamilyMemberVo;
import yq.mall.domain.vo.MemberPassengerVo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.service.IMemberFamilyMemberService;
import yq.mall.service.IMemberPassengerService;
import yq.mall.service.IOrderTicketService;
import yq.system.api.RemoteConfigService;


import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 订单行程
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/order/ticket")
public class ClientOrderTicketController extends BaseController {

    private final IOrderTicketService ticketService;
    @DubboReference
    @Resource
    private RemoteDriverService remoteDriverService;

    @Resource
    private IMemberFamilyMemberService familyMemberService;
    private final IMemberPassengerService memberPassengerService;

    @DubboReference
    private RemoteLineTemplateService remoteLineTemplateService;

    @DubboReference
    private RemoteDepartPlanService remoteDepartPlanService;

    @DubboReference
    @Resource
    private RemoteConfigService remoteConfigService;

    @DubboReference
    @Resource
    private RemoteLineScheduleService remoteLineScheduleService;

    /**
     * 获取家庭行程详情列表
     */
    @GetMapping("/page")
    public R<TableDataInfo<TicketInfoVo>> page(ClientOrderTicketQueryBo bo, PageQuery pageQuery) {
        TableDataInfo<OrderTicketVo> tableInfo = ticketService.queryTicketInfoPageList(getUserId(), bo, pageQuery);
        TableDataInfo<TicketInfoVo> ticketInfoVoTableDataInfo = new TableDataInfo<>();
        BeanUtil.copyProperties(tableInfo, ticketInfoVoTableDataInfo);
        List<TicketInfoVo> res = new ArrayList<>();
        tableInfo.getRows().forEach(orderTicketVo -> {
            TicketInfoVo ticketInfoVo = new TicketInfoVo();
            OrderTicketVo vo = ticketService.queryById(orderTicketVo.getId());
            BeanUtil.copyProperties(vo, ticketInfoVo);
            if (ObjectUtil.isNotNull(vo.getDriverId())) {
                ticketInfoVo.setDriverName(remoteDriverService.selectById(vo.getDriverId()).getName());
            }
            if (ObjectUtil.isNotNull(vo.getDepartPlanId())) {
               Long lineTemplateId =  remoteDepartPlanService.getTemplateIdByDepartPlanId(vo.getDepartPlanId());
               if (lineTemplateId != null) {
                   ticketInfoVo.setLineTemplateName(remoteLineTemplateService.selectNameById(lineTemplateId));
               }
            }
            res.add(ticketInfoVo);
        });
        ticketInfoVoTableDataInfo.setRows(res);
        return R.ok(ticketInfoVoTableDataInfo);
    }

    /**
     * 通过id查询行程详情
     */
    @GetMapping("/get")
    public R<TicketInfoVo> get(Long id) {
        OrderTicketVo orderTicketVo = ticketService.queryById(id);
        if (orderTicketVo == null) {
            return R.ok();
        }

        // 权限校验
        var familyMemberList = familyMemberService.listFamilyMemberByUserId(getUserId());
        var memberIds = new HashSet<Long>();
        memberIds.add(getUserId());
        ObjectUtil.defaultIfNull(familyMemberList, new ArrayList<MemberFamilyMemberVo>())
            .forEach(member -> memberIds.add(member.getUserId()));
        ValidateUtils.isTrue(memberIds.contains(orderTicketVo.getUserId()), "无权限查看");

        TicketInfoVo ticketInfoVo = new TicketInfoVo();
        BeanUtil.copyProperties(orderTicketVo, ticketInfoVo);
        if (ObjectUtil.isNotNull(orderTicketVo.getDriverId())) {
            ticketInfoVo.setDriverName(remoteDriverService.selectById(orderTicketVo.getDriverId()).getName());
        }
        if (ObjectUtil.isNotNull(orderTicketVo.getDepartPlanId())) {
            Long lineTemplateId =  remoteDepartPlanService.getTemplateIdByDepartPlanId(orderTicketVo.getDepartPlanId());
            if (lineTemplateId != null) {
                ticketInfoVo.setLineTemplateName(remoteLineTemplateService.selectNameById(lineTemplateId));
            }
        }
        //获取排班状态
        if(ObjectUtil.isNotNull(orderTicketVo.getScheduleId())){
            String signInFlag = remoteLineScheduleService.queryById(orderTicketVo.getScheduleId()).getSignInFlag();
            ticketInfoVo.setSignInFlag(signInFlag);
        }

        return R.ok(ticketInfoVo);
    }

    /**
     * 获取乘客快捷选票消息列表
     */
    @GetMapping("/getQuickTicketMessage")
    public R<QuickTicketMessageVo> getQuickTicketMessage(Long passengerId) {
        return R.ok(ticketService.getQuickTicketMessage(passengerId, getUserId()));
    }

    /**
     * 获取默认乘车人快捷选票消息列表
     */
    @GetMapping("/getDefaultQuickTicketMessage")
    public R<QuickTicketMessageVo> getDefaultQuickTicketMessage() {
        MemberPassengerVo defaultPassenger = memberPassengerService.getDefaultPassenger(getUserId());
        if (defaultPassenger == null) {
            return R.ok();
        }
        return R.ok(ticketService.getQuickTicketMessage(defaultPassenger.getId(), getUserId()));
    }

    /**
     * 查询往返票开关
     */
    @GetMapping("/getReturnTicketFlag")
    public R<Boolean> getReturnTicketFlag() {
        String returnTicketStr = remoteConfigService.selectConfigByKey(ClientConfigConstant.CLIENT_RETURN_TICKET);
        return R.ok("true".equals(returnTicketStr));
    }

}
