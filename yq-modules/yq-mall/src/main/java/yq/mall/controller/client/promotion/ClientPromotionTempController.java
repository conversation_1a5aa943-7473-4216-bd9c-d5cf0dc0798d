package yq.mall.controller.client.promotion;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.bus.api.RemoteSchoolService;
import yq.common.core.domain.R;
import yq.mall.controller.client.promotion.bo.ClientPromotionTempQueryBo;
import yq.mall.controller.client.promotion.vo.ClientPromotionCouponTemplateVo;
import yq.mall.enums.promotion.CouponTakeTypeEnum;
import yq.mall.enums.promotion.CouponValidTypeEnum;
import yq.mall.service.IPromotionCouponService;
import yq.mall.service.IPromotionCouponTemplateService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 客户端 - 优惠券模板 Controller
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/promotion/temp")
public class ClientPromotionTempController {

    @Resource
    private IPromotionCouponService promotionCouponService;

    @Resource
    private IPromotionCouponTemplateService promotionCouponTemplateService;

    @Resource
    @DubboReference
    private RemoteSchoolService remoteSchoolService;

    /**
     * 获取用户可领取的优惠券数量
     */
    @GetMapping("/getCanTakeCount")
    public R<Integer> getCanTakeCount(ClientPromotionTempQueryBo bo) {
        bo.setType(CouponTakeTypeEnum.USER.getType());
        // 获取优惠券模板列表
        var tempList = promotionCouponTemplateService.getList(bo);
        // 获取用户是否可领取优惠券
        var memberCanTakeMap = promotionCouponService.getMemberCanTakeMap(getUserId(), tempList);
        // 统计模板可领取数量
        int tempTakeCount = tempList.stream().mapToInt(temp -> {
            var count = (temp.getQuantityLimit() - temp.getTakeCount());
            return Math.max(count, 0);
        }).sum();
        // 统计可领取的优惠券数量
        int canTakeCount = memberCanTakeMap.values().stream().mapToInt(Integer::intValue).sum();
        return R.ok(tempTakeCount > 0 && canTakeCount > 0 ? canTakeCount : 0);
    }

    /**
     * 获取优惠券列表
     * @return 优惠券列表
     */
    @GetMapping("/list")
    public R<List<ClientPromotionCouponTemplateVo>> getList(ClientPromotionTempQueryBo bo) {
        bo.setType(CouponTakeTypeEnum.USER.getType());
        // 获取优惠券模板列表
        var tempList = promotionCouponTemplateService.getList(bo);
        // 获取用户是否可领取优惠券
        var memberCanTakeMap = promotionCouponService.getMemberCanTakeMap(getUserId(), tempList);

        // 获取学校信息
        var schoolMap = new HashMap<Long, String>();
        var templateVoList = new ArrayList<ClientPromotionCouponTemplateVo>();

        // 组装数据
        for (var temp : tempList) {
            var vo = BeanUtil.copyProperties(temp, ClientPromotionCouponTemplateVo.class);
            vo.setCanTakeCount(memberCanTakeMap.get(temp.getId()));
            // 获取学校名称
            if (CollUtil.isNotEmpty(vo.getSchoolIds())) {
                temp.getSchoolIds().forEach(schoolId -> {
                    if (!schoolMap.containsKey(schoolId)) {
                        schoolMap.put(schoolId, remoteSchoolService.selectNameById(schoolId));
                    }
                    if (vo.getSchoolNames() == null) {
                        vo.setSchoolNames(CollUtil.newArrayList());
                    }
                    vo.getSchoolNames().add(schoolMap.get(schoolId));
                });
            }
            templateVoList.add(vo);
        }
        // todo 领取后多少天先进行推算然后统一排序
        // 按照优惠券模板的金额、有效期排序，在有效期的靠前，即将过期的靠前, 金额大的靠前
        var now = LocalDateTime.now();
        templateVoList.sort((o1, o2) -> {
            if (o1.getValidType().equals(CouponValidTypeEnum.TERM.getType())) {
                o1.setValidDateTo(LocalDateTimeUtil.endOfDay(now.plusDays(o1.getValidTermStart() + o1.getValidTermEnd())));
            }
            if (o2.getValidType().equals(CouponValidTypeEnum.TERM.getType())) {
                o2.setValidDateTo(LocalDateTimeUtil.endOfDay(now.plusDays(o2.getValidTermStart() + o2.getValidTermEnd())));
            }
            LocalDateTime dateTo1 = o1.getValidDateTo();
            LocalDateTime dateTo2 = o2.getValidDateTo();
            int dateComparison = dateTo1.compareTo(dateTo2);
            return dateComparison != 0 ? dateComparison : o2.getMaxDiscount().compareTo(o1.getMaxDiscount());
        });
        return R.ok(templateVoList);
    }
}
