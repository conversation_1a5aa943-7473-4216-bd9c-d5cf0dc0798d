package yq.mall.controller.client.refund;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.common.core.domain.R;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.mall.controller.client.refund.bo.TicketRefundBo;
import yq.mall.service.IRefundService;

import javax.annotation.Resource;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 客户端-退款
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/refund")
public class ClientRefundController {

    @Resource
    private IRefundService refundService;

    /**
     * 申请车票退款
     */
    @PostMapping("/applyRefund")
    @RepeatSubmit
    public R<Void> applyTicketRefund(@RequestBody TicketRefundBo clientTicketRefundBo) {
//        throw new AlertException("暂不支持退款，请联系客服");
        refundService.applyRefundByMember(getUserId(), clientTicketRefundBo);
        return R.ok();
    }

}
