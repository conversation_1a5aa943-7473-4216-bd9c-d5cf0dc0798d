package yq.mall.controller.client.mall;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.common.core.domain.R;
import yq.common.mybatis.core.page.PageQuery;
import yq.mall.domain.vo.RefundRulesVo;
import yq.mall.service.IRefundRulesService;

import java.util.List;

/**
 * 客户端-退款规则
 *
 * @author：fish
 * @date：2024/8/9
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/refundRules")
public class ClientRefundRulesController {

    private final IRefundRulesService refundRulesService;

    /**
     * 获取退款规则列表
     */
    @GetMapping("/list")
    public R<List<RefundRulesVo>> listRefundRule() {
        return R.ok(refundRulesService.listRefundRule());
    }

}
