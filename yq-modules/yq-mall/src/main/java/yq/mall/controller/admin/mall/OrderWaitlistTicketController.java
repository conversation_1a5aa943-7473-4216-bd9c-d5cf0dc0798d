package yq.mall.controller.admin.mall;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.constraints.*;
import org.springframework.validation.annotation.Validated;
import yq.bus.api.RemoteDepartPlanStationService;
import yq.bus.api.RemoteStationService;
import yq.bus.api.domain.vo.RemoteBusDepartPlanStationVo;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.mall.controller.admin.mall.vo.StationAndtPlanWaitlistBo;
import yq.mall.controller.admin.mall.vo.StationWaitlistTicketCountVo;
import yq.mall.domain.OrderWaitlistTicket;
import yq.mall.domain.vo.OrderWaitlistTicketVo;
import yq.mall.domain.bo.OrderWaitlistTicketBo;
import yq.mall.service.IOrderWaitlistTicketService;
import yq.common.mybatis.core.page.TableDataInfo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author：fish
 * @date：2024/10/24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/waitlistTicket")
public class OrderWaitlistTicketController {

    private final IOrderWaitlistTicketService orderWaitlistTicketService;

    @Resource
    @DubboReference
    private RemoteStationService remoteStationService;

    @Resource
    @DubboReference
    private RemoteDepartPlanStationService remoteDepartPlanStationService;

    /**
     * 查询候补行程列表-分页
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/list/page")
    public TableDataInfo<OrderWaitlistTicketVo> list(OrderWaitlistTicketBo bo, PageQuery pageQuery) {
        return orderWaitlistTicketService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询候补行程列表
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/list")
    public R<List<OrderWaitlistTicketVo>>  list(Long orderId) {
        return R.ok(orderWaitlistTicketService.queryList(orderId));
    }

    /**
     * 根据计划id查询有效的候补行程
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/getWaitlistCountByPlanId")
    public R<List<StationWaitlistTicketCountVo>> listByPlanId(Long departPlanId) {
        List<StationWaitlistTicketCountVo>  voList = new ArrayList<>();
        List<OrderWaitlistTicket> list = orderWaitlistTicketService.queryListByDepartPlanId(departPlanId);

        if(list.isEmpty()){
           return R.ok(voList);
        }

        //取list中第一个元素判定方向
        String lineKind = list.getFirst().getLineKind();
        // 获取站点ID集合
        Set<Long> stationIds = list.stream()
            .flatMap(ticket -> "1".equals(lineKind) ? Stream.of(ticket.getFromId()) : Stream.of(ticket.getToId()))
            .collect(Collectors.toSet());

        // 统计每个站点的人数
        Map<Long, Long> stationCounts = list.stream()
            .collect(Collectors.groupingBy(
                ticket -> "1".equals(lineKind) ? ticket.getFromId() : ticket.getToId(),
                Collectors.counting()
            ));

        voList = stationIds.stream()
            .map(stationId -> {
                StationWaitlistTicketCountVo vo = new StationWaitlistTicketCountVo();
                vo.setStationId(stationId);
                vo.setCount(stationCounts.get(stationId));
                vo.setStationName(remoteStationService.selectNameById(stationId));
                return vo;
            })
            .collect(Collectors.toList());

        //获取按发车计划排序的站点顺序
        var planStationList =remoteDepartPlanStationService.getStationByDepartPlanId(departPlanId);
        Map<Long, Long> stationOrderMap = planStationList.stream()
            .collect(Collectors.toMap(RemoteBusDepartPlanStationVo::getStationId, RemoteBusDepartPlanStationVo::getOrderNo));

        //按计划站点排序排序
        voList.sort((a, b) -> {
            Long orderA = stationOrderMap.get(a.getStationId());
            Long orderB = stationOrderMap.get(b.getStationId());
            return orderA.compareTo(orderB);
        });

        return R.ok(voList);
    }


    /**
     * 根据站点和计划id查询未排班的候补车票
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/getUnScheduleWaitlistTicketByStationAndPlanId")
    public R<List<OrderWaitlistTicketVo>> getUnScheduleWaitlistTicketByStationAndPlanId(StationAndtPlanWaitlistBo bo){
        return R.ok(orderWaitlistTicketService.getUnScheduleWaitlistTicketByStationAndPlanId(bo));
    }

}
