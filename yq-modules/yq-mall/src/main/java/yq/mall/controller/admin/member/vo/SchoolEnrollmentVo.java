package yq.mall.controller.admin.member.vo;

import lombok.Data;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.Translation;

import java.util.List;

@Data
public class SchoolEnrollmentVo {
    private Long schoolId;
    @Translation(type = BusTransConstant.SCHOOL_ID_TO_NAME, mapper = "schoolId")
    private String schoolName;
    private List<EnrollmentDetailVo> backDates;

    @Data
    public static class EnrollmentDetailVo {
        private String backDate;
        private Integer count;
    }
}
