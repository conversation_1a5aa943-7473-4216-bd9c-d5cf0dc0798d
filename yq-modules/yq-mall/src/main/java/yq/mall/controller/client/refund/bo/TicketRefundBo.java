package yq.mall.controller.client.refund.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *-退款-车票退款业务对象
 * <AUTHOR>
 */
@Data
public class TicketRefundBo {

    @NotNull(message = "订单号不能为空")
    private Long orderId;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 退款项
     */
    private List<RefundItem> refundItems;
    private BigDecimal refundAmount;

    @Data
    public static class RefundItem {
        /**
         * 车票ID
         */
        private Long ticketId;
        /**
         * 退款金额
         */
        private BigDecimal refundAmount;
    }

    /**
     * 预估的手续费
     */
    private BigDecimal totalServerCharge;

}
