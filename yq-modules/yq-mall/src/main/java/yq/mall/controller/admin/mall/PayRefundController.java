package yq.mall.controller.admin.mall;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.common.core.domain.R;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.domain.bo.pay.PayRefundBo;
import yq.mall.domain.vo.pay.PayRefundVo;
import yq.mall.service.IPayRefundService;

import java.util.List;


/**
 * @author：fish
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payRefund")
public class PayRefundController {

    private final IPayRefundService payRefundService;

    /**
     * payRefundId进行退款
     */
    @SaCheckPermission("mall:order:refund")
    @PostMapping("/retry")
    public R<Void> retry(@NotNull(message = "支付退款单Id不能为空") Long payRefundId) {
        payRefundService.applyRefundById(payRefundId);
        return R.ok();
    }

    /**
     * 查询支付退款记录
     */
    @SaCheckPermission("mall:order:list")
    @GetMapping("/list")
    public R<List<PayRefundVo>> list(@NotNull(message = "订单编号不能为空") Long orderId,Integer status) {
        List<PayRefundVo> refundVos = payRefundService.getList(orderId, status);
        return R.ok(refundVos);
    }

    /**
     * 查询支付退款记录分页
     */
    @SaCheckPermission("mall:payRefund:list")
    @GetMapping("/pageList")
    public TableDataInfo<PayRefundVo> queryPageList(PayRefundBo bo, PageQuery pageQuery){
        return payRefundService.queryPageList(bo, pageQuery);
    }


}

