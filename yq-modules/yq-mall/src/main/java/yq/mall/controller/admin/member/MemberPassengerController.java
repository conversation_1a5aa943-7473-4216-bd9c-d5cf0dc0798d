package yq.mall.controller.admin.member;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import yq.common.core.domain.R;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.web.core.BaseController;
import yq.mall.controller.admin.member.vo.EnrollmentVo;
import yq.mall.controller.admin.member.vo.SchoolEnrollmentVo;
import yq.mall.domain.bo.MemberPassengerBo;
import yq.mall.domain.bo.PassengerSearchBo;
import yq.mall.domain.vo.MemberPassengerVo;
import yq.mall.service.IMemberPassengerService;

import java.util.List;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 乘车人管理
 * 前端访问路由地址为:/mall/user
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/passenger")
public class MemberPassengerController extends BaseController {

    private final IMemberPassengerService memberPassengerService;

    /**
     * 查询乘车人列表
     */
    @SaCheckPermission("mall:user:list")
    @GetMapping("/list")
    public TableDataInfo<MemberPassengerVo> list(PassengerSearchBo bo) {
        return memberPassengerService.listPassengerBySearchBo(bo);
    }

    /**
     * 根据userId获取乘车人列表
     * @return
     */
    @SaCheckPermission("mall:user:list")
    @GetMapping("/listByUserId")
    public R<List<MemberPassengerVo>> listPassengerByUser(@NotNull(message = "主键不能为空") Long userId){
        var list = memberPassengerService.listPassengerByUser(userId);
        return R.ok(list);
    }

    /**
     * 编辑乘车人
     */
    @SaCheckPermission("mall:user:edit")
    @PutMapping("/update")
    public R<Void> updatePassenger(@Validated @RequestBody MemberPassengerBo bo) {
        memberPassengerService.editPassenger(getUserId(), bo);
        return R.ok();
    }

    /**
     * 获取学校注册人数
     */
    @SaCheckPermission("mall:user:getEnrollmentList")
    @GetMapping("/getEnrollmentList")
    public R<List<SchoolEnrollmentVo>> getEnrollmentList() {
        return R.ok(memberPassengerService.getEnrollmentList());
    }

}
