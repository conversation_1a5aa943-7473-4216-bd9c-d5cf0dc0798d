package yq.mall.controller.admin.mall;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.log.enums.BusinessType;
import yq.common.excel.utils.ExcelUtil;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.mall.domain.PayRefund;
import yq.mall.domain.bo.pay.PayProfitSharingReturnBo;
import yq.mall.domain.vo.pay.PayProfitSharingReturnVo;
import yq.mall.service.IPayProfitSharingReturnService;
import yq.mall.service.IPayRefundService;

import javax.annotation.Resource;

/**
 * 支付分账回退记录
 * 前端访问路由地址为:/bus/profitSharingReturn
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pay/profitSharingReturn")
public class PayProfitSharingReturnController extends BaseController {

    private final IPayProfitSharingReturnService payProfitSharingReturnService;

    @Resource
    private IPayRefundService payRefundService;

    /**
     * 查询支付分账回退记录列表
     */
    @SaCheckPermission("mall:profitSharingReturn:list")
    @GetMapping("/pageProfitSharingReturn")
    public TableDataInfo<PayProfitSharingReturnVo> list(PayProfitSharingReturnBo bo, PageQuery pageQuery) {
        return payProfitSharingReturnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付分账回退记录列表
     */
    @SaCheckPermission("mall:profitSharingReturn:export")
    @Log(title = "支付分账回退记录", businessType = BusinessType.EXPORT)
    @PostMapping("/exportProfitSharingReturn")
    public void export(PayProfitSharingReturnBo bo, HttpServletResponse response) {
        List<PayProfitSharingReturnVo> list = payProfitSharingReturnService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付分账回退记录", PayProfitSharingReturnVo.class, response);
    }

    /**
     * 重试发起支付分账回退
     */
    @SaCheckPermission("mall:profitSharingReturn:retry")
    @PostMapping("/retry")
    public void retryProfitSharingReturn(@NotNull(message = "payRefundId不能为空") Long payRefundId){
        var payProfitSharingReturn = payProfitSharingReturnService.getByPayRefundId(payRefundId);
        if (payProfitSharingReturn != null) {
            PayRefund payRefund = payRefundService.getById(payProfitSharingReturn.getPayRefundId());
            // 再调用profitSharingReturn方法加锁
            payProfitSharingReturnService.profitSharingReturn(payRefund);
        }
    }

}
