package yq.mall.controller.admin.mall.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TodayCountVo {

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 退款数量
     */
    private Integer refundCount;

    /**
     * 行程数量
     */
    private Long ticketCount;

    /**
     * 计划数量
     */
    private Long planCount;

    /**
     * 班次数量
     */
    private Long scheduleCount;
}
