package yq.mall.controller.admin.mall;

import cn.hutool.core.util.DesensitizedUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.bus.api.RemoteSchoolService;
import yq.bus.api.RemoteStationService;
import yq.bus.api.RemoteSupplierService;
import yq.common.core.domain.R;
import yq.common.excel.utils.ExcelUtil;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.log.enums.BusinessType;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.mall.controller.admin.mall.bo.*;
import yq.mall.controller.admin.mall.vo.*;
import yq.mall.domain.bo.OrderTicketBo;
import yq.mall.domain.bo.StationAndDepartPlanBo;
import yq.mall.domain.vo.OrderTicketVo;
import yq.mall.service.IOrderTicketService;
import yq.common.mybatis.core.page.TableDataInfo;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 行程
 * 前端访问路由地址为:/mall/ticket
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ticket")
public class OrderTicketController extends BaseController {

    private final IOrderTicketService ticketService;
    @Resource
    @DubboReference
    private RemoteStationService remoteStationService;

    @Resource
    @DubboReference
    private RemoteSchoolService remoteSchoolService;

    @Resource
    @DubboReference
    private RemoteSupplierService remoteSupplierService;


    /**
     * 查询行程列表
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/list")
    public TableDataInfo<OrderTicketVo> list(OrderTicketBo bo, PageQuery pageQuery) {
        return ticketService.queryPageList(bo, pageQuery);
    }

    /**
     * 通过站点id和发车计划id获取未排班的行程
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/getUnSchedulingTicketByStationAndDepartPlan")
    public R<List<OrderTicketVo>> getUnSchedulingTicketByStationAndDepartPlan(StationAndDepartPlanBo bo){
        return R.ok(ticketService.getUnSchedulingTicketByStationAndDepartPlan(bo));
    }

    /**
     * 查询订单行程
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/list/admin")
    public R<List<OrderTicketVo>>  list(Long orderId) {
        return R.ok(ticketService.queryOrderTicket(orderId));
    }


    @Log(title = "订单行程", businessType = BusinessType.EXPORT)
    @SaCheckPermission("mall:ticket:export")
    @PostMapping("/export")
    public void export(OrderTicketBo bo, HttpServletResponse response){
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(-1);
        pageQuery.setPageNum(1);
        TableDataInfo<OrderTicketVo> tableDataInfo = ticketService.queryPageList(bo, pageQuery);

        // 使用 ConcurrentHashMap 实现缓存
        ConcurrentHashMap<Long, String> stationCache = new ConcurrentHashMap<>();

        tableDataInfo.getRows().forEach(item -> {
            // 缓存上车站点名称
            String upStationName = stationCache.computeIfAbsent(item.getFromId(), id -> remoteStationService.selectNameById(id));
            item.setFromName(upStationName);

            // 缓存下车站点名称
            String downStationName = stationCache.computeIfAbsent(item.getToId(), id -> remoteStationService.selectNameById(id));
            item.setToName(downStationName);

            // 使用hutool脱敏工具类对手机号码进行脱敏
            item.setPassengerMobile(DesensitizedUtil.mobilePhone(item.getPassengerMobile()));
        });

        ExcelUtil.exportExcel(tableDataInfo.getRows(), "乘客列表", OrderTicketVo.class, response);
    }

    /**
     * 核验车票
     */
    @SaCheckPermission("mall:ticket:edit")
    @PostMapping("/check")
    @RepeatSubmit
    public R<Void> checkTicket(@RequestBody CheckTicketBo bo){
        return toAjax(ticketService.checkTicket(bo.getId(),
            bo.getLicensePlate(), bo.getVehicleId(), bo.getScheduleId()));
    }

    /**
     * 取消核验
     */
    @SaCheckPermission("mall:ticket:edit")
    @PutMapping("/cancelCheck/{id}")
    @RepeatSubmit
    public R<Void> cancelCheckTicket(@PathVariable Long id){
        return toAjax(ticketService.cancelCheckTicket(id));
    }

    /**
     * 结束行程
     */
    @SaCheckPermission("mall:ticket:edit")
    @PutMapping("/finishTrip/{id}")
    @RepeatSubmit
    public R<Boolean> finishTrip(@PathVariable Long id){
        return R.ok(ticketService.finishTrip(id));
    }

    /**
     * 获取用户行程列表
     */
    @SaCheckPermission("mall:ticket:list")
    @GetMapping("/getTicketByUser")
    public TableDataInfo<OrderTicketVo> getTicketByUser(Long userId, PageQuery pageQuery) {
        return ticketService.getTicketByUser(userId, pageQuery);
    }

    /**
     * 售票明细
     */
    @GetMapping("/getTicketSalesDetail")
    public R<TicketSaleDetailsVo> getTicketSalesDetail(TicketSalesBo bo) {
        return R.ok(ticketService.getTicketSalesDetail(bo));
    }

    /**
     * 导出售票明细
     */
    @Log(title = "售票明细", businessType = BusinessType.EXPORT)
    @SaCheckPermission("mall:ticket:export")
    @PostMapping("/exportTicketSalesDetail")
    public void exportTicketSalesDetail(TicketSalesBo bo, HttpServletResponse response) {
        var res = ticketService.getTicketSalesDetail(bo);
        res.getRows().forEach(e -> e.setSchoolName(remoteSchoolService.selectNameById(e.getSchoolId())));
        res.getRows().add(TicketSalesVo.builder()
            .schoolName("合计" + res.getSchoolCount() + "所学校")
            .lineName("合计" + res.getLineCount() + "条线路")
            .soldCount(res.getTicketCount())
            .restSeatNumber(res.getRestSeatCount())
            .soldAmount(res.getTicketAmount())
            .scheduleCount(res.getScheduleCount())
            .avgAmount(res.getScheduleAvgAmount())
            .build());
        ExcelUtil.exportExcel(res.getRows(), "售票明细", TicketSalesVo.class, response);
    }

    /**
     * 获取用券统计
     */
    @SaCheckPermission("mall:ticket:ticketAnalysis")
    @GetMapping("/getCouponStatistics")
    public R<List<CouponStatisticsVo>> getCouponStatistics(CouponStatisticsQueryBo bo) {
        return R.ok(ticketService.getCouponStatistics(bo));
    }

    /**
     * 导出用券统计
     */
    @Log(title = "用券统计", businessType = BusinessType.EXPORT)
    @SaCheckPermission("mall:ticket:export")
    @PostMapping("/exportCouponStatistics")
    public void exportCouponStatistics(CouponStatisticsQueryBo bo, HttpServletResponse response) {
        List<CouponStatisticsVo> couponStatistics = ticketService.getCouponStatistics(bo);
        couponStatistics.forEach(e -> {
            e.setSchoolName(remoteSchoolService.selectNameById(e.getSchoolId()));
        });
        ExcelUtil.exportExcel(couponStatistics, "行程分析", CouponStatisticsVo.class, response);
    }

    /**
     * 本周统计
     */
    @SaCheckPermission("mall:ticket:weeklyCount")
    @GetMapping("/getWeeklyCount")
    public R<TodayCountVo> getWeeklyCount(){
        return R.ok(ticketService.getWeeklyCount());
    }

    /**
     * 获取站点热力图数据
     */
    @SaCheckPermission("mall:ticket:boardingAnalysis")
    @GetMapping("/getStationPassengerHeatmap")
    public R<List<StationPassengerHeatMapVo>> getStationPassengerHeatMap(StationPassengerHeatMapBo bo) {
        List<StationPassengerHeatMapVo> result = ticketService.getStationPassengerHeatMap(bo);
        return R.ok(result);
    }

    /**
     * 获取供应商售票明细
     */
    @GetMapping("/getSupplierTicketSalesDetail")
    public R<SupplierTicketDetailsSalesVo> getSupplierTicketSalesDetail(SupplierTicketSalesBo bo) {
        return R.ok(ticketService.getSupplierTicketSalesDetail(bo));
    }

    /**
     * 导出供应商售票明细
     */
    @Log(title = "供应商售票明细", businessType = BusinessType.EXPORT)
    @SaCheckPermission("mall:ticket:export")
    @PostMapping("/exportSupplierTicketSalesDetail")
    public void exportSupplierTicketSalesDetail(SupplierTicketSalesBo bo, HttpServletResponse response) {
        var res = ticketService.getSupplierTicketSalesDetail(bo);
        res.getRows().forEach(e -> e.setSupplierName(remoteSupplierService.selectNameById(e.getSupplierId())));
        res.getRows().add(SupplierTicketSalesVo.builder()
            .supplierName("合计" + res.getSupplierCount() + "个供应商")
            .ticketCount(res.getTicketCount())
            .scheduleCount(res.getScheduleCount())
            .ticketAmount(res.getTicketAmount())
            .build());
        ExcelUtil.exportExcel(res.getRows(), "供应商售票明细", SupplierTicketSalesVo.class, response);
    }
}
