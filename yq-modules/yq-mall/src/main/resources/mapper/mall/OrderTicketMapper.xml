<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.mall.mapper.OrderTicketMapper">

    <select id="getUnSchedulingTicketCount" resultType="yq.mall.domain.vo.TicketCountVo">
        SELECT
            COUNT(CASE WHEN status = 0 THEN 1 END) AS count,
        date_at,
        school_id
        FROM order_ticket
        WHERE date_at >= #{dateAt}
        AND pay_status = 1
        GROUP BY school_id, date_at
    </select>


    <select id="getTicketSalesDetail" resultType="yq.mall.controller.admin.mall.vo.TicketSalesVo">
        SELECT _tmp.date_at as dateAt,
               _tmp.line_kind as lineKind,
               _tmp.school_id as schoolId,
                count( CASE WHEN _tmp.settlement_flag = 1 THEN 1 END ) AS scheduleCount,
               sum(_tmp.sold_number) as soldCount,
               (sum(_tmp.seat_number) - sum(_tmp.sold_number)) as restSeatNumber,
               CONCAT(FLOOR((sum(_tmp.sold_number) / sum(_tmp.seat_number)) * 100), '%') as loadRate,
               line_template_name as lineName,
               sum(_tmp.pay_price) as soldAmount,
               ROUND((sum(_tmp.pay_price) / count(1)), 3) as avgAmount
        FROM (SELECT tk.date_at,
                     DATE_FORMAT(ls.time_at, '%H:%i') time_at,
                     tk.line_kind,
                     tk.vehicle_id,
                     COUNT(1) sold_number,
                     MAX(ls.seat_number) seat_number,
                     lt.`name` line_template_name,
                     ROUND(SUM(tk.pay_price), 2) pay_price,
                     lt.school_id school_id,
                    ls.settlement_flag
        FROM order_ticket tk
                       INNER JOIN `xbus-bus`.`bus_line_schedule` ls ON tk.schedule_id = ls.id
                       INNER JOIN `xbus-bus`.bus_line_template lt on ls.line_template_id = lt.id
              WHERE 1 = 1
                AND (tk.pay_status = 1 or tk.post_pay_flag = 1)
                AND tk.refund_status = 0
                AND tk.STATUS != 4
                AND tk.schedule_id IS NOT NULL
        <if test="startDate != null and endDate != null">
            AND tk.date_at BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="lineKind != null">
            AND tk.line_kind = #{lineKind}
        </if>
        <if test="schoolIds != null and schoolIds.size() > 0">
            AND tk.school_id IN
            <foreach item="schoolId" collection="schoolIds" open="(" separator="," close=")">
                #{schoolId}
            </foreach>
        </if>
        <if test="lineIds != null and lineIds.size() > 0">
            AND lt.id IN
            <foreach item="lineId" collection="lineIds" open="(" separator="," close=")">
                #{lineId}
            </foreach>
        </if>
        <if test="status != null and status.size() > 0">
            AND tk.status IN
            <foreach item="stat" collection="status" open="(" separator="," close=")">
                #{stat}
            </foreach>
        </if>
              GROUP BY
                  tk.date_at,
                  time_at,
                  tk.line_kind,
                  tk.vehicle_id,
                  lt.`name`,
                  lt.school_id,
                  ls.settlement_flag ) _tmp
        GROUP BY _tmp.date_at,
                 _tmp.line_kind,
                 _tmp.line_template_name,
                 _tmp.school_id
        ORDER BY _tmp.date_at
    </select>

    <select id="getCouponStatistics" resultType="yq.mall.controller.admin.mall.vo.CouponStatisticsVo">
        SELECT
            ot.school_id AS schoolId,
            COUNT(ot.id) AS ticketCount,
            SUM(ot.pay_price) AS paymentAmount,
            SUM(ri.refund_amount) AS refundAmount,
            SUM(ot.coupon_price) AS discountAmount,
            COUNT(DISTINCT bl.id) AS lineCount,
            COUNT(DISTINCT tro.coupon_id) AS discountCount
        FROM
            `xbus-mall`.order_ticket ot
                LEFT JOIN
            `xbus-mall`.refund_item ri ON ot.id = ri.order_item_id -- 关联订单与退款信息
                LEFT JOIN
            `xbus-bus`.bus_depart_plan bdp ON ot.depart_plan_id = bdp.id -- 关联订单与发车计划
                LEFT JOIN
            `xbus-bus`.bus_line_template bl ON bdp.line_template_id = bl.id -- 关联发车计划与线路模版
                left JOIN
            `xbus-mall`.trade_order tro on tro.id = ot.order_id
        WHERE
            ot.pay_status = 1
          AND
           ot.date_at BETWEEN #{startDate} AND #{endDate} -- 时间范围
        GROUP BY
            ot.school_id
    </select>

    <select id="getSupplierTicketSalesDetail" resultType="yq.mall.controller.admin.mall.vo.SupplierTicketSalesVo">
        SELECT
            _tmp.date_at as dateAt,
            _tmp.supplier_id AS supplierId,
            count( CASE WHEN _tmp.settlement_flag = 1 THEN 1 END ) AS scheduleCount,
            SUM(_tmp.ticket_count) AS ticketCount,
            SUM(_tmp.ticket_amount) AS ticketAmount
        FROM
            (SELECT
                tk.date_at AS date_at,
                DATE_FORMAT(ls.time_at, '%H:%i') AS time_at,
                ls.supplier_id AS supplier_id,
                COUNT(tk.id) AS ticket_count,
                SUM(tk.pay_price) AS ticket_amount,
                ls.settlement_flag
        FROM
                order_ticket tk
            LEFT JOIN `xbus-bus`.bus_line_schedule ls ON tk.schedule_id = ls.id
            WHERE
                1 = 1
            AND (tk.pay_status = 1 OR tk.post_pay_flag = 1)
            AND tk.refund_status = 0
            AND tk.status != 4
            AND tk.schedule_id IS NOT NULL
            <if test="startDate != null and endDate != null">
                AND tk.date_at BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="supplierIds != null and supplierIds.size() > 0">
                AND ls.supplier_id IN
                <foreach item="supplierId" collection="supplierIds" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            GROUP BY
                date_at,
                time_at,
                ls.supplier_id,
                tk.line_kind,
                tk.vehicle_id,
                ls.line_template_id,
                ls.settlement_flag ) _tmp
        GROUP BY
            dateAt,
            supplierId
        ORDER BY
            dateAt,
            supplierId
    </select>


</mapper>
