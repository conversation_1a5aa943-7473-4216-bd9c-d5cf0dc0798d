<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="yq.mall.mapper.FundFlowBillMapper">

    <select id="getFundFlowBillDetailsList" resultType="yq.mall.controller.admin.mall.vo.FundFlowBillDetailsVo">
        SELECT * FROM (
                          SELECT success_time AS trade_time, id AS pay_order_id, channel_order_no, '订单收入' AS trade_type, total_amount AS amount, -channel_fee_amount as channel_fee_amount
                               ,merchant_order_id as order_id
                          FROM pay_order
                          WHERE success_time BETWEEN #{startDate} AND #{endDate}

                          UNION ALL

                          SELECT apply_time AS trade_time, pay_order_id, channel_order_no, '订单退款' AS trade_type,-refund_amount AS amount, channel_fee_amount as channel_fee_amount
                               ,merchant_order_id as order_id
                          FROM pay_refund
                          WHERE apply_time BETWEEN #{startDate} AND #{endDate}

                          UNION ALL

                          SELECT pps.finish_time AS trade_time, pps.pay_order_id, pps.channel_order_no, '平台服务费' AS trade_type, -pps.profit_sharing_amount AS amount, 0 AS channel_fee_amount,
                              po.merchant_order_id as order_id
                          FROM pay_profit_sharing as pps
                              inner join pay_order as po
                              on pps.pay_order_id = po.id
                          WHERE pps.`status` = 30 and pps.finish_time BETWEEN #{startDate} AND #{endDate}

                          UNION ALL

                          SELECT ppsr.finish_time AS trade_time, ppsr.pay_order_id, ppsr.channel_order_no, '平台服务费退回' AS trade_type, ppsr.return_amount AS amount, 0 AS channel_fee_amount,
                              po.merchant_order_id as order_id
                          FROM pay_profit_sharing_return as ppsr
                              inner join pay_order as po
                              on ppsr.pay_order_id = po.id
                          WHERE ppsr.`status` = 10 and ppsr.finish_time BETWEEN #{startDate} AND #{endDate}
                      ) _tmp
        ORDER BY _tmp.trade_time DESC
    </select>

    <!--
        查询指定范围内的冻结账单记录，
        因为查询条件是范围是根据 finish_time 来的
        但是success_time 和 finish_time 会存在跨月/年的问题
        所以在between时也需要把跨月的记录查询出来
       -->
    <select id="getFreezeBillList" resultType="yq.mall.controller.admin.mall.vo.FreezeBillVo">
        SELECT
            po.merchant_order_id,
            po.total_amount,
            po.channel_fee_amount,
            DATE(po.success_time) AS success_time,
            DATE(ps.finish_time) AS finish_time
        FROM
            pay_profit_sharing ps
            INNER JOIN
            pay_order po
        ON po.id = ps.pay_order_id
        WHERE ps.`status` != -1
          AND DATEDIFF(ps.finish_time, po.success_time) > 0
          AND (
            DATE(ps.finish_time) BETWEEN #{startDate} AND #{endDate}
            OR (
                YEAR(po.success_time) = YEAR(#{startDate})
                AND MONTH(po.success_time) = MONTH(#{startDate})
                AND ps.finish_time > #{endDate}
            )
            OR (
                po.success_time &lt;= #{startDate} and ps.finish_time > #{startDate}
            )
          )
        ORDER BY
            po.success_time;
    </select>
</mapper>
