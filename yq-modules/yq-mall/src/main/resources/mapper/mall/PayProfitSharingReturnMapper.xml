<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="yq.mall.mapper.PayProfitSharingReturnMapper">

    <select id="getProfitSharingReturnAmount" resultType="yq.mall.controller.admin.mall.vo.FundFlowBillVo">
        SELECT
            DATE(finish_time) AS dateAt,
            SUM(return_amount) AS profitSharingReturnAmount
        FROM pay_profit_sharing_return
        WHERE `status` = 10 and  DATE(finish_time) BETWEEN #{startDate} AND #{endDate}
        GROUP BY DATE(finish_time)
    </select>

</mapper>
