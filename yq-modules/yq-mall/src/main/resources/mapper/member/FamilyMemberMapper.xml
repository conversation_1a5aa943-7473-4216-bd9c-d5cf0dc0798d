<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.mall.mapper.MemberFamilyMemberMapper">

    <select id="getFamilyIdByUserId" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT
        family_id
        FROM
        member_family_member
        WHERE
        user_id = #{userId} and del_flag = '0'
        limit 1
    </select>
</mapper>
