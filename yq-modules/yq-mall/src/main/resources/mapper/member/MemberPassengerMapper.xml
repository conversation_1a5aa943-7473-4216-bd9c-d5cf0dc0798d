<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.mall.mapper.MemberPassengerMapper">



    <select id="countAllPassengersByUserId" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT COUNT(*)
        FROM member_passenger
        WHERE user_id = #{userId}
    </select>

    <select id="listMemberPassengerPage" resultType="yq.mall.domain.vo.MemberPassengerVo"
            parameterType="yq.mall.domain.bo.PassengerSearchBo">
        SELECT
        mu.mobile AS userMobile,
        mp.`name`,
        mp.mobile,
        mp.id_card,
        mp.school_id,
        mp.back_date,
        mp.address,
        mp.valid_code
        FROM
        member_passenger mp
        INNER JOIN member_user mu ON mp.user_id = mu.id
        WHERE mp.del_flag = '0' and mu.del_flag = '0'
        <if test="bo.userMobile != null and bo.userMobile != ''">
            AND mu.mobile LIKE CONCAT('%', #{bo.userMobile}, '%')
        </if>
        <if test="bo.name != null and bo.name != ''">
            AND mp.`name` LIKE CONCAT('%', #{bo.name}, '%')
        </if>
        <if test="bo.passengerMobile != null and bo.passengerMobile != ''">
            AND mp.mobile LIKE CONCAT('%', #{bo.mobile}, '%')
        </if>
        <if test="bo.schoolId != null and bo.schoolId != ''">
            AND mp.school_id = #{bo.schoolId}
        </if>
        <if test="bo.backDateStart != null and bo.backDateEnd != null">
            AND mp.back_date BETWEEN #{bo.backDateStart} AND #{bo.backDateEnd}
        </if>
    </select>

    <select id="getEnrollmentList" resultType="yq.mall.controller.admin.member.vo.EnrollmentVo">
        SELECT
            school_id AS schoolId,
            COUNT(id) AS backNumber,
            back_date AS backDate
        FROM
            member_passenger
        WHERE
            back_date BETWEEN #{backDateStart} AND #{backDateEnd}
        and del_flag = '0'
        GROUP BY
            school_id, back_date
    </select>

</mapper>
