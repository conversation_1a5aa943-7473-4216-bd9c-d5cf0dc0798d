#FROM ibm-semeru-runtimes:open-21-jre
FROM bellsoft/liberica-openjdk-debian:21.0.3-cds

MAINTAINER Lion Li

RUN mkdir -p /yq/job/logs \
    /yq/job/temp \
    /yq/skywalking/agent

WORKDIR /yq/job

ENV SERVER_PORT=9203 LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS=""

EXPOSE ${SERVER_PORT}

ADD ./target/yq-job.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -Dserver.port=${SERVER_PORT} \
           #-Dskywalking.agent.service_name=yq-job \
           #-javaagent:/yq/skywalking/agent/skywalking-agent.jar \
           -XX:+HeapDumpOnOutOfMemoryError -XX:+UseZGC ${JAVA_OPTS} \
           -jar app.jar
