package yq.job.snailjob.bus;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.bus.api.RemoteDepartPlanService;
import yq.bus.api.RemoteLineScheduleService;

import javax.annotation.Resource;

/**
 * 发车计划自动启售
 * <AUTHOR>
 */

@Component
@JobExecutor(name = "planAutoSaleJobExecutor")
public class PlanAutoSaleJobExecutor {

    @Resource
    @DubboReference
    private RemoteDepartPlanService remoteDepartPlanService;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try{
            int count = remoteDepartPlanService.autoSaleDepartPlan();
            return ExecuteResult.success("计划自动启售任务成功, 执行计划数：" + count);
        } catch (Exception e) {
            return ExecuteResult.failure("计划自动启售任务失败");
        }
    }
}
