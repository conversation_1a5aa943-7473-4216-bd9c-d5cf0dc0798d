package yq.job.snailjob.mall;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.mall.api.RemotePayRefundService;

import javax.annotation.Resource;

/**
 * 订单退款失败后重试任务
 * @author：fish
 */
@Component
@JobExecutor(name = "payRefundRetryJobExecutor")
public class PayRefundRetryJobExecutor {

    @Resource
    @DubboReference
    private RemotePayRefundService remotePayRefundService;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            remotePayRefundService.payRefundRetryTask();
            return ExecuteResult.success("订单退款失败后重试任务执行成功");
        } catch (Exception e) {
            return ExecuteResult.failure("订单退款失败后重试任务执行失败");
        }
    }

}
