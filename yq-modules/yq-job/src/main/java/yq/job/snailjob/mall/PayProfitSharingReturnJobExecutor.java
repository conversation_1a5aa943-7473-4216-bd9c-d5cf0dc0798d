package yq.job.snailjob.mall;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.mall.api.RemotePayProfitSharingService;

import javax.annotation.Resource;

/**
 * 分账退回任务
 * <AUTHOR>
 */
@Component
@JobExecutor(name = "payProfitSharingReturnJobExecutor")
public class PayProfitSharingReturnJobExecutor {

    @Resource
    @DubboReference
    private RemotePayProfitSharingService remotePayProfitSharingService;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            remotePayProfitSharingService.profitSharingReturnTask();
            return ExecuteResult.success("分账退回任务执行成功");
        } catch (Exception e) {
            return ExecuteResult.failure("分账退回任务执行失败");
        }
    }

}
