package yq.job.snailjob.mall;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.mall.api.RemoteOrderService;

import javax.annotation.Resource;

/**
 * 支付超时任务
 * <AUTHOR>
 */
@Component
@JobExecutor(name = "orderPayExpireJobExecutor")
public class OrderPayExpireJobExecutor {

    @Resource
    @DubboReference
    private RemoteOrderService remoteOrderService;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            var count = remoteOrderService.cancelOrderBySystem();
            return ExecuteResult.success("支付超时任务成功, 取消订单数：" + count);
        } catch (Exception e) {
            return ExecuteResult.failure("支付超时任务失败");
        }
    }

}
