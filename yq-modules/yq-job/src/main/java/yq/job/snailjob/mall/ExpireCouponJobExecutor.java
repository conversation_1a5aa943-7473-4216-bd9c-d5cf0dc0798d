package yq.job.snailjob.mall;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.mall.api.RemotePromotionCouponService;

import javax.annotation.Resource;

/**
 * 过期优惠券任务执行器
 * <AUTHOR>
 */
@Component
@JobExecutor(name = "expireCouponJobExecutor")
public class ExpireCouponJobExecutor {

    @Resource
    @DubboReference(timeout = 60000)
    private RemotePromotionCouponService remotePromotionCouponService;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            remotePromotionCouponService.expireCoupon();
            return ExecuteResult.success("过期优惠券任务成功");
        } catch (Exception e) {
            return ExecuteResult.failure("过期优惠券任务数量过多，等待下次处理");
        }
    }
}
