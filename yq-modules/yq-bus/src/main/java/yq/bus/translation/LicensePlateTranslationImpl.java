package yq.bus.translation;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.service.IBusVehicleService;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;

/**
 * 车牌号翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.VEHICLE_ID_TO_License_Plate)
public class LicensePlateTranslationImpl implements TranslationInterface<String> {
    private final IBusVehicleService busVehicleService;
    @Override
    public String translation(Object key, String other) {
        return busVehicleService.selectLicensePlateById(Long.parseLong(key.toString()));
    }
}
