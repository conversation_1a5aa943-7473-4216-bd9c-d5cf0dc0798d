package yq.bus.translation;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.service.IBusLineTemplateService;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;

/**
 * 线路名称翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.LINE_TEMPLATE_ID_TO_NAME)
public class LineTemplateNameTranslationImpl implements TranslationInterface<String> {

    private final IBusLineTemplateService busLineTemplateService;
    @Override
    public String translation(Object key, String other) {
        return busLineTemplateService.selectNameById(Long.parseLong(key.toString()));
    }
}
