package yq.bus.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import yq.common.core.validate.EditGroup;

@Data
public class BusDriverStatusBo {
    /**
     * 司机唯一标识
     */
    @NotNull(message = "司机唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机状态
     */
    @NotNull(message = "司机状态不能为空", groups = { EditGroup.class })
    private Boolean status;
}
