package yq.bus.domain.bo;

import org.springframework.format.annotation.DateTimeFormat;
import yq.bus.domain.BusSchoolCalendar;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.sql.Time;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 校历管理业务对象 bus_school_calendar
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusSchoolCalendar.class, reverseConvertGenerate = false)
public class BusSchoolCalendarBo extends BaseEntity {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校唯一标识
     */
    @NotNull(message = "学校唯一标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 类型 离校或返校
     */
    @NotBlank(message = "类型 离校或返校不能为空", groups = { AddGroup.class, EditGroup.class })
    private String eventType;

    /**
     * 离校或返校日期
     */
    @NotNull(message = "离校或反校日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;


    /**
     * 高一时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Time grade1TimeAt;

    /**
     * 高二时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Time grade2TimeAt;

    /**
     * 高三时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Time grade3TimeAt;


}
