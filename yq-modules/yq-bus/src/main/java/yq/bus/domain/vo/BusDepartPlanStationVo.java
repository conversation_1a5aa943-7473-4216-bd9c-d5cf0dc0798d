package yq.bus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.domain.BusDepartPlanStation;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;


/**
 * 发车计划站点视图对象 bus_depart_plan_station
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusDepartPlanStation.class)
public class BusDepartPlanStationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 发车计划id
     */
    @ExcelProperty(value = "发车计划id")
    private Long departPlanId;



    /**
     * 站点主键id
     */
    @ExcelProperty(value = "站点主键id")
    private Long stationId;

    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点名称")
    private String stationName;

    /**
     * 站点经度
     */
    @ExcelProperty(value = "站点经度")
    private BigDecimal stationLongitude;

    /**
     * 站点纬度
     */
    @ExcelProperty(value = "站点纬度")
    private BigDecimal stationLatitude;

    /**
     * 学校id
     */
    @ExcelProperty(value = "学校id")
    private Long schoolId;

    /**
     * 间隔时间
     */
    @ExcelProperty(value = "间隔时间")
    private Long interval;

    /**
     * 到站时间
     */
    @ExcelProperty(value = "到站时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time arrivalTime;

    /**
     * 上架票数
     */
    @ExcelProperty(value = "上架票数")
    private Long upperLimit;

    /**
     * 已售票数
     */
    @ExcelProperty(value = "已售票数")
    private Long soldNumber;

    /**
     * 锁定票数
     */
    private Integer lockNumber;

    /**
     * 排序号
     */
    @ExcelProperty(value = "排序号")
    private Long orderNo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @Translation(type = BusTransConstant.STATION_ID_TO_DESCRIPTION, mapper = "stationId")
    private String memo;


}
