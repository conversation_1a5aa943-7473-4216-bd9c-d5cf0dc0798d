package yq.bus.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.domain.BusSupplierBill;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

/**
 * 供应商账单业务对象 supplier_bill
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusSupplierBill.class, reverseConvertGenerate = false)
public class BusSupplierBillBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 账期
     */
    @NotBlank(message = "账期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billCycle;

    /**
     * 供应商Id
     */
    @NotNull(message = "供应商Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 执行班次数
     */
    @NotNull(message = "执行班次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long scheduleCount;

    /**
     * 是否核验
     */
    @NotNull(message = "是否核验不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean verifyFlag;

    /**
     * 核验用户
     */
    @NotNull(message = "核验用户不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long verifyUserId;

    /**
     * 核验用户名称
     */
    @NotBlank(message = "核验用户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String verifyUserName;

    /**
     * 核验账单时间
     */
    @NotNull(message = "核验账单时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verifyDate;


}
