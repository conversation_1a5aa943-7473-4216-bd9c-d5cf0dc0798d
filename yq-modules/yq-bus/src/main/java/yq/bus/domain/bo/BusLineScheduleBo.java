package yq.bus.domain.bo;

import org.springframework.format.annotation.DateTimeFormat;
import yq.bus.domain.BusLineSchedule;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 排班业务对象 bus_line_schedule
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusLineSchedule.class, reverseConvertGenerate = false)
public class BusLineScheduleBo extends BaseEntity {

    /**
     * Id
     */
    @NotNull(message = "Id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校ID
     */
    @NotNull(message = "学校ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 线路模板Id
     */
    @NotNull(message = "线路模板Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineTemplateId;

    /**
     * 发车计划Id
     */
    @NotNull(message = "发车计划Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departPlanId;

    /**
     * 线路方向
     */
    @NotBlank(message = "线路方向不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lineKind;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 发车时间
     */
    @NotNull(message = "发车时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "HH:mm")
    private Time timeAt;

    /**
     * 供应商Id
     */
    @NotNull(message = "承运供应商Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 车属供应商ID
     */
    @NotNull(message = "车属供应商ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vehicleSupplierId;

    /**
     * 车辆Id
     */
    @NotNull(message = "车辆Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vehicleId;

    /**
     * 司机Id
     */
    @NotNull(message = "司机Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 启用或停用
     */
//    @NotBlank(message = "启用或停用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 座位数
     */
    @NotNull(message = "座位数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long seatNumber;

    /**
     * 已卖出座位数
     */
    private Long soldNumber;

    /**
     * 剩余座位数
     */
    private Long restSeatNumber;

    /**
     * 报班标识 0-未报班 1-已报班 2-已签退
     */
    private String signInFlag;

    /**
     * 报班时间
     */
    private Date signInTime;

    /**
     * 订单行程Ids
     */
    private List<Long> ticketIds = new ArrayList<>();

    /**
     * 候补行程ids
     */
    private List<Long> waitlistTicketIds = new ArrayList<>();

    /**
     * 未满座
     */
    private Boolean notFullSeatFlag;

    /**
     * 是否是跨线排班
     */
    private Boolean crossLineFlag;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 签退用户类型
     */
    private String signOutUserType;
}
