package yq.bus.domain.bo;

import org.springframework.format.annotation.DateTimeFormat;
import yq.bus.domain.BusDepartPlan;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;


import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 发车计划业务对象 bus_depart_plan
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusDepartPlan.class, reverseConvertGenerate = false)
public class BusDepartPlanBo extends BaseEntity {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校id
     */
    @NotNull(message = "学校id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 线路模板id
     */
    @NotNull(message = "线路模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineTemplateId;

    /**
     * 发车日期
     */
    @NotNull(message = "发车日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 发车时间
     */
    @NotNull(message = "发车时间不能为空", groups = { EditGroup.class })
    @JsonFormat(pattern = "HH:mm")
    private Time timeAt;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal price;

    /**
     * 优惠价
     */
    @NotNull(message = "优惠价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal discountPrice;

    /**
     * 上架票数
     */
    private Long upperLimit;

    /**
     * 状态 0已停售 1生效中
     */
    private String status;

    /**
     * 类别 0-离校或 1-返校  字典值
     */
    private String lineKind;

//    /**
//     * 离校属性: 0 常规线 1 加班线
//     */
//    private String leaveProperty;

    /**
     * 发车类型
     */
    private String leaveType;

    /**
     * 出发站点
     */
    private Long fromStationId;

    /**
     * 终点站点
     */
    private Long toStationId;

    /**
     * 站点列表
     */
    private List<BusDepartPlanStationBo> stations;

    /**
     * 是否查看候补人数
     */
    private Boolean showWaitlist = false;

    /**
     * 自动起售时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date autoSaleStartTime;
}

