package yq.bus.domain.convert;

import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import yq.bus.api.domain.vo.RemoteBusDriverVo;
import yq.bus.domain.vo.BusDriverVo;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BusDriverVoConvert extends BaseMapper<BusDriverVo, RemoteBusDriverVo> {
}
