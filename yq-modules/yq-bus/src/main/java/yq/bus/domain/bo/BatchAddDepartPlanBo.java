package yq.bus.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import yq.common.core.validate.AddGroup;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.List;

/**
 * @author：fish
 * @date：2024/8/5
 */
@Data
public class BatchAddDepartPlanBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学校id
     */
    @NotNull(message = "学校id不能为空", groups = { AddGroup.class })
    private Long schoolId;

    /**
     * 线路模板id
     */
    @NotNull(message = "线路模板id不能为空", groups = { AddGroup.class })
    private Long lineTemplateId;

    /**
     * 价格
     */
//    @NotNull(message = "价格不能为空", groups = { AddGroup.class })
//    private BigDecimal price;

    /**
     * 类别 0-离校或 1-返校
     */
    private String lineKind;

    /**
     * 发车日期列表
     */
    @NotNull(message = "发车日期不能为空", groups = { AddGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<Date> dateAts;


    @NotNull(message = "发车时间不能为空", groups = { AddGroup.class })
    private List<BatchAddDepartPlanBo.Types> types;

    @Data
    public static class Types {
        private String leaveType;
        @JsonFormat(pattern = "HH:mm")
        private Time timeAt;
    }
}
