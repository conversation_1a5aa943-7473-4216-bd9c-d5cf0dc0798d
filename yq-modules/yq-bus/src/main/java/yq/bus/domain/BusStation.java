package yq.bus.domain;

import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

import java.io.Serial;

/**
 * 站点管理对象 bus_station
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_station")
public class BusStation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 站点唯一标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 站点名称
     */
    private String name;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 站点描述
     */
    private String description;

    /**
     * 站点地址
     */
    private String address;

    /**
     * 图片OssId
     */
    private String imgOssId;

    /**
     * 是否为学校站点  0否  1是
     */
    private String schoolFlag;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 记录是否被逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;

    /**
     * 高德地图id
     */
    private String amapId;

    /**
     * 站点状态 1正常 0停用
     */
    private String status;

    /**
     * 站点方向  -1 同站点,  0 上车点 ，1 下车点
     */
    private String direction;

    /**
     * 是否弃用
     */
    private Boolean disableFlag;
}
