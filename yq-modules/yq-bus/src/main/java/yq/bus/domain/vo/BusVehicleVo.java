package yq.bus.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.domain.BusVehicle;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 车辆管理视图对象 bus_vehicle
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusVehicle.class)
public class BusVehicleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车辆唯一标识
     */
    @ExcelProperty(value = "车辆唯一标识")
    private Long id;

    /**
     * 所属供应商id
     */
    @ExcelProperty(value = "所属供应商id")
    private Long supplierId;

    /**
     * 所属供应商名称
     */
    @Translation(type = BusTransConstant.SUPPLIER_ID_TO_NAME, mapper = "supplierId")
    private String supplierName;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String licensePlate;

    /**
     * 座位数
     */
    @ExcelProperty(value = "座位数")
    private Long seatNumber;

    /**
     * 购买日期
     */
    @ExcelProperty(value = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date buyAt;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String memo;

    /**
     * GPS设备IMEI号
     */
    private Long gpsImei;

    /**
     * 车载设备ID
     */
    private Long trunkId;

    /**
     * 是否绑定GPS
     */
    private Boolean bindGps;

    /**
     * 车辆行驶证有效期
     */
    @ExcelProperty(value = "道路运输证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date drivingLicenseExpiry;

    /**
     * 道路运输证有效期
     */
    @ExcelProperty(value = "道路运输证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date roadTransportExpiry;

    /**
     * 保险单开始日期
     */
    @ExcelProperty(value = "保险单开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartDate;

    /**
     * 保险单结束日期
     */
    @ExcelProperty(value = "保险单结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndDate;

    /**
     * 车辆是否停用
     */
    private Boolean disabled;
}
