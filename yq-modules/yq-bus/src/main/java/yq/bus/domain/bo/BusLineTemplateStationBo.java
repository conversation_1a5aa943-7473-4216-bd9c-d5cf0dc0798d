package yq.bus.domain.bo;

import yq.bus.domain.BusLineTemplateStation;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 线路模板站点业务对象 bus_line_template_station
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusLineTemplateStation.class, reverseConvertGenerate = false)
public class BusLineTemplateStationBo extends BaseEntity {

    /**
     * 线路模板站点唯一标识
     */
    @NotNull(message = "线路模板站点唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 线路模板id
     */
    @NotNull(message = "线路模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineTemplateId;

    /**
     * 站点主键id
     */
    @NotNull(message = "站点主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stationId;

    /**
     * 学校id
     */
    @NotNull(message = "学校id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 间隔时间
     */
    @NotNull(message = "间隔时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long interval;

    /**
     * 上架票数
     */
    @NotNull(message = "上架票数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long upperLimit;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNo;

    /**
     * 备注
     */
    private String memo;


}
