package yq.bus.domain.bo;

import yq.bus.domain.BusStation;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 站点管理业务对象 bus_station
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusStation.class, reverseConvertGenerate = false)
public class BusStationBo extends BaseEntity {

    /**
     * 站点唯一标识
     */
    @NotNull(message = "站点唯一标识不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站点名称
     */
    @NotBlank(message = "站点名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal latitude;

    /**
     * 站点描述
     */
    private String description;

    /**
     * 站点地址
     */
    private String address;

    /**
     * 图片OssId
     */
    private String imgOssId;

    /**
     * 是否为学校站点  0否  1是
     */
    private String schoolFlag;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 站点状态
     */
    private String status;

    /**
     * 站点方向  -1 同站点,  0 上车点 ，1 下车点
     */
    private String direction;
}
