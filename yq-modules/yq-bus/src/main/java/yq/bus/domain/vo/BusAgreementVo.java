package yq.bus.domain.vo;

import yq.bus.domain.BusAgreement;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户协议视图对象 bus_agreement
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusAgreement.class)
public class BusAgreementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    /**
     * 协议的key
     */
    @ExcelProperty(value = "协议的key")
    private String agreementKey;

    /**
     * 协议标题
     */
    @ExcelProperty(value = "协议标题")
    private String title;

    /**
     * 协议内容
     */
    @ExcelProperty(value = "协议内容")
    private String content;

    /**
     * 协议类型 1客户端  2司机端
     */
    @ExcelProperty(value = "协议类型 1客户端  2司机端")
    private String agreementType;


}
