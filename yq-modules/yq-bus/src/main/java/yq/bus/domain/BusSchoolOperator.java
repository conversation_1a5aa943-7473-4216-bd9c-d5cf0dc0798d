package yq.bus.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学校维护人
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@TableName("bus_school_operator")
public class BusSchoolOperator implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    private Long schoolId;
    private Long userId;

    private LocalDateTime createTime;
    private Long createBy;

    @TableLogic
    private String delFlag;

}
