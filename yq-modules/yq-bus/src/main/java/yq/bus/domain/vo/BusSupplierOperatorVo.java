package yq.bus.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.bus.domain.BusSupplierOperator;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商维护人
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusSupplierOperator.class)
public class BusSupplierOperatorVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    private Long supplierId;

    private Long userId;

    private LocalDateTime createTime;

    private Long createBy;

    @TableLogic
    private String delFlag;
}
