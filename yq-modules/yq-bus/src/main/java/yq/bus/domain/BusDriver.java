package yq.bus.domain;

import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 司机对象 bus_driver
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_driver")
public class BusDriver extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机唯一标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 司机姓名
     */
    private String name;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 所属供应商id
     */
    private Long supplierId;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 性别（0男 1女 2未知）
     */
    private String gendar;

    /**
     * 驾照编号
     */
    private String driveLicense;

    /**
     * 驾龄
     */
    private Long driverYear;

    /**
     * 备注
     */
    private String memo;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    @TableLogic
    private String delFlag;

    /**
     * 微信公众号openId
     */
    private String openId;

    /**
     * 是否禁用
     */
    private Boolean status;

    /**
     * 从业资格证有效期限
     */
    private Date practitionerCertExpiry;

    /**
     * 驾驶证期限
     */
    private Date driverLicenseExpiry;

}
