package yq.bus.domain;

    import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
    import java.math.BigDecimal;

import java.io.Serial;

/**
 * 学校管理对象 bus_school
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_school")
public class BusSchool extends BaseEntity {

@Serial
private static final long serialVersionUID = 1L;

        /**
         * 学校唯一标识
         */
        @TableId(value = "id")
        private Long id;

        /**
         * 学校名称
         */
        private String schoolName;

        /**
         * 学校地址
         */
        private String address;

        /**
         * 经度
         */
        private BigDecimal longitude;

        /**
         * 纬度
         */
        private BigDecimal latitude;

        /**
         * 备注
         */
        private String memo;

        /**
         * 状态 启用或停用
         */
        private String status;

        /**
         * 记录是否被逻辑删除. 0代表存在 2代表删除

         */
        @TableLogic
        private String delFlag;

        /**
         * 是否区分上下车站点
         */
        private Boolean upDownFlag;


}
