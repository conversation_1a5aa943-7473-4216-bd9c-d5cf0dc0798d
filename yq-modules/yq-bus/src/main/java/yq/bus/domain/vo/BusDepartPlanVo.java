package yq.bus.domain.vo;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.domain.BusDepartPlan;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 发车计划视图对象 bus_depart_plan
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusDepartPlan.class)
public class BusDepartPlanVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    /**
     * 学校id
     */
    @ExcelProperty(value = "学校id")
    private Long schoolId;

    /**
     * 学校名称
     */
    @Translation(type = BusTransConstant.SCHOOL_ID_TO_NAME, mapper = "schoolId")
    private String schoolName;

    /**
     * 线路模板id
     */
    @ExcelProperty(value = "线路模板id")
    private Long lineTemplateId;

    /**
     * 线路模板名称
     */
    @Translation(type = BusTransConstant.LINE_TEMPLATE_ID_TO_NAME, mapper = "lineTemplateId")
    private String lineTemplateName;

    /**
     * 出发站点
     */
    @ExcelProperty(value = "出发站点")
    private Long fromStationId;

    /**
     * 终点站点
     */
    @ExcelProperty(value = "终点站点")
    private Long toStationId;

    /**
     * 发车日期
     */
    @ExcelProperty(value = "发车日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateAt;

    /**
     * 发车时间
     */
    @ExcelProperty(value = "发车时间")
    @JsonFormat(pattern = "HH:mm")
    private Time timeAt;

    /**
     * 票价
     */
    @ExcelProperty(value = "票价")
    private BigDecimal price;

    /**
     * 优惠价
     */
    @ExcelProperty(value = "优惠价")
    private BigDecimal discountPrice;

    /**
     * 上架票数
     */
    private Integer upperLimit;

    /**
     * 状态 0已停售 1生效中
     */
    @ExcelProperty(value = "状态 0已停售 1生效中")
    private String status;

    /**
     * 类别 0-离校或 1-返校  字典值
     */
    @ExcelProperty(value = "类别 0-离校或 1-返校  字典值")
    private String lineKind;

    /**
     * 离校属性: 0 常规线 1 加班线
     */
    @ExcelProperty(value = "离校属性: 0 常规线 1 加班线")
    private String leaveProperty;

    /**
     * 发车类型
     */
    @ExcelProperty(value = "发车类型")
    private String leaveType;

    /**
     * 已排人数
     */
    private Long scheduledNum;

    /**
     * 待排人数
     */
    private Long waitNum;

    /**
     * 未满座班次数
     */
    private Long notFullNum;

    /**
     * 候补人数
     */
    private Long waitlistNum;

    /**
     * 是否准备好排班
     */
    private Boolean readyScheduleFlag;

    /**
     * 自动起售时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date autoSaleStartTime;
}
