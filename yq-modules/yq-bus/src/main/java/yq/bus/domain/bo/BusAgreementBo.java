package yq.bus.domain.bo;

import yq.bus.domain.BusAgreement;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用户协议业务对象 bus_agreement
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusAgreement.class, reverseConvertGenerate = false)
public class BusAgreementBo extends BaseEntity {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 协议的key
     */
    @NotBlank(message = "协议的key不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agreementKey;

    /**
     * 协议标题
     */
    @NotBlank(message = "协议标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 协议类型 1客户端  2司机端
     */
    @NotBlank(message = "协议类型 1客户端  2司机端不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agreementType;


}
