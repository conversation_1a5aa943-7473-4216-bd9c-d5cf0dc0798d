package yq.bus.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

/**
 * 发车计划对象 bus_depart_plan
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_depart_plan")
public class BusDepartPlan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 线路模板id
     */
    private Long lineTemplateId;

    /**
     * 发车日期
     */
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 出发站点
     */
    private Long fromStationId;

    /**
     * 终点站点
     */
    private Long toStationId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 优惠价
     */
    private BigDecimal discountPrice;

    /**
     * 上架票数
     */
    private Integer upperLimit;

    /**
     * 状态 0已停售 1生效中
     */
    private String status;

    /**
     * 类别 0-离校或 1-返校  字典值
     */
    private String lineKind;

    /**
     * 离校属性: 0 常规线 1 加班线
     */
//    private String leaveProperty;

    /**
     * 发车类型
     */
    private String leaveType;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    @TableLogic
    private String delFlag;

    /**
     * 是否准备好排班
     */
    private Boolean readyScheduleFlag;

    /**
     * 自动起售时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date autoSaleStartTime;


}
