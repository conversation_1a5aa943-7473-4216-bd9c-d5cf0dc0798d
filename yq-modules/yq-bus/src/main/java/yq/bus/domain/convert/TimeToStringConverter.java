package yq.bus.domain.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.sql.Time;
import java.text.SimpleDateFormat;

public class TimeToStringConverter implements Converter<Time> {

    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");

    @Override
    public Class<Time> supportJavaTypeKey() {
        return Time.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Time value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        // 将 Time 格式化为字符串
        if (value != null) {
            return new WriteCellData<>(TIME_FORMAT.format(value));
        }
        return new WriteCellData<>("");
    }
}
