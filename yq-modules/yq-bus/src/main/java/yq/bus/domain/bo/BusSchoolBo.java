package yq.bus.domain.bo;

import yq.bus.domain.BusSchool;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 学校管理业务对象 bus_school
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusSchool.class, reverseConvertGenerate = false)
public class BusSchoolBo extends BaseEntity {

    /**
     * 学校唯一标识
     */
    @NotNull(message = "学校唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校名称
     */
    @NotBlank(message = "学校名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String schoolName;

    /**
     * 学校地址
     */
    private String address;

    /**
     * 经度
     */
//    @NotNull(message = "经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal longitude;

    /**
     * 纬度
     */
//    @NotNull(message = "纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal latitude;

    /**
     * 备注
     */
    private String memo;

    /**
     * 状态 启用或停用
     */
    private String status;

    /**
     * 是否区分上下车站点
     */
    private Boolean upDownFlag;

    /**
     * 上车站点
     */
    private BusStationBo up;

    /**
     * 下车站点
     */
    private BusStationBo down;

    /**
     * 上下车站点
     */
    private BusStationBo upAndDown;
}
