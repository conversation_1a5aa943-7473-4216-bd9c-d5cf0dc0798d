package yq.bus.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import yq.bus.domain.BusDriver;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 司机业务对象 bus_driver
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusDriver.class, reverseConvertGenerate = false)
public class BusDriverBo extends BaseEntity {

    /**
     * 司机唯一标识
     */
    @NotNull(message = "司机唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 手机
     */
    @NotBlank(message = "手机不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mobile;

    /**
     * 所属供应商id
     */
    @NotNull(message = "所属供应商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 性别（0男 1女 2未知）
     */
    private String gendar;

    /**
     * 驾照编号
     */
    private String driveLicense;

    /**
     * 驾龄
     */
    private Long driverYear;

    /**
     * 备注
     */
    private String memo;

    private String openId;

    private Boolean status;

    /**
     * 从业资格证到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date practitionerCertExpiry;

    /**
     * 驾驶证到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date driverLicenseExpiry;
}
