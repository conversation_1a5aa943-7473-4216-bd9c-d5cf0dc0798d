package yq.bus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.domain.BusDriver;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;

import yq.bus.api.translation.constant.BusTransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 司机视图对象 bus_driver
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusDriver.class)
public class BusDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String name;

    /**
     * 手机
     */
    @ExcelProperty(value = "手机")
    private String mobile;

    /**
     * 所属供应商id
     */
    @ExcelProperty(value = "所属供应商id")
    private Long supplierId;

    /**
     * 所属供应商名称
     */
    @Translation(type = BusTransConstant.SUPPLIER_ID_TO_NAME, mapper = "supplierId")
    private String supplierName;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String idCard;

    /**
     * 性别（0男 1女 2未知）
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String gendar;

    /**
     * 驾照编号
     */
    @ExcelProperty(value = "驾照编号")
    private String driveLicense;

    /**
     * 驾龄
     */
    @ExcelProperty(value = "驾龄")
    private Long driverYear;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String memo;

    /**
     * 微信公众号openId
     */
    private String openId;

    /**
     * 是否禁用
     */
    private Boolean status;

    /**
     * 从业资格证有效期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date practitionerCertExpiry;

    /**
     * 驾驶证期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date driverLicenseExpiry;
}
