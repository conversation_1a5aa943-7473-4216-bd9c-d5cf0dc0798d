package yq.bus.domain.vo;

import java.sql.Time;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.domain.BusSchoolCalendar;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 校历管理视图对象 bus_school_calendar
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusSchoolCalendar.class)
public class BusSchoolCalendarVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    /**
     * 学校唯一标识
     */
    @ExcelProperty(value = "学校唯一标识")
    private Long schoolId;

    /**
     * 类型 离校或返校
     */
    @ExcelProperty(value = "类型 离校或返校")
    private String eventType;

    /**
     * 离校或返校日期
     */
    @ExcelProperty(value = "离校或返校日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateAt;

    /**
     * 高一时间
     */
    @ExcelProperty(value = "高一时间")
    private Time grade1TimeAt;

    /**
     * 高二时间
     */
    @ExcelProperty(value = "高二时间")
    private Time grade2TimeAt;

    /**
     * 高三时间
     */
    @ExcelProperty(value = "高三时间")
    private Time grade3TimeAt;


}
