package yq.bus.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

import java.io.Serial;

/**
 * 供应商账单对象 supplier_bill
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_supplier_bill")
public class BusSupplierBill extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 账期
     */
    private String billCycle;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 执行班次数
     */
    private Long scheduleCount;

    /**
     * 是否核验
     */
    private Boolean verifyFlag;

    /**
     * 核验用户
     */
    private Long verifyUserId;

    /**
     * 核验用户名称
     */
    private String verifyUserName;

    /**
     * 核验账单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime verifyDate;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    @TableLogic
    private String delFlag;

}
