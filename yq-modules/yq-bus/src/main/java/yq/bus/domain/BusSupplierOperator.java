package yq.bus.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商维护人
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@TableName("bus_supplier_operator")
public class BusSupplierOperator implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    private Long supplierId;

    private Long userId;

    private LocalDateTime createTime;

    private Long createBy;

    @TableLogic
    private String delFlag;
}
