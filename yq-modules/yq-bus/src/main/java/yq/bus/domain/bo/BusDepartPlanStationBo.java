package yq.bus.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import yq.bus.domain.BusDepartPlanStation;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.sql.Time;

/**
 * 发车计划站点业务对象 bus_depart_plan_station
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BusDepartPlanStation.class, reverseConvertGenerate = false)
public class BusDepartPlanStationBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 发车计划id
     */
    @NotNull(message = "发车计划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departPlanId;

    /**
     * 线路模板id
     */
//    @NotNull(message = "线路模板id不能为空", groups = { AddGroup.class, EditGroup.class })
//    private Long lineTemplateId;

    /**
     * 站点主键id
     */
    @NotNull(message = "站点主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stationId;

    /**
     * 学校id
     */
    @NotNull(message = "学校id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 间隔时间
     */
    @NotNull(message = "间隔时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long interval;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Time arrivalTime;

    /**
     * 上架票数
     */
    @NotNull(message = "上架票数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long upperLimit;


    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNo;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { EditGroup.class })
    private String memo;


}
