package yq.bus.domain.vo;

import java.math.BigDecimal;

import yq.bus.api.translation.constant.BusTransConstant;
import yq.bus.domain.BusLineTemplate;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 线路模板视图对象 bus_line_template
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusLineTemplate.class)
public class BusLineTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线路模板唯一标识
     */
    @ExcelProperty(value = "线路模板唯一标识")
    private Long id;

    /**
     * 学校id
     */
    @ExcelProperty(value = "学校id")
    private Long schoolId;

    /**
     * 线路名称
     */
    @ExcelProperty(value = "线路名称")
    private String name;

    /**
     * 类别 0-离校或 1-返校  字典值
     */
    @ExcelProperty(value = "类别 0-离校或 1-返校  字典值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "bus_line_kind")
    private String lineKind;

//    /**
//     * 离校属性: 0 常规线 1 加班线
//     */
//    @ExcelProperty(value = "离校属性: 0 常规线 1 加班线", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "leave_property")
//    private String leaveProperty;


    /**
     * 始发站主键id
     */
    @ExcelProperty(value = "始发站主键id")
    private Long fromStationId;

    /**
     * 始发站名称
     */
    @Translation(type = BusTransConstant.STATION_ID_TO_NAME, mapper = "fromStationId")
    private String fromStationName;

    /**
     * 终点站主键id
     */
    @ExcelProperty(value = "终点站主键id")
    private Long toStationId;

    /**
     * 终点站名称
     */
    @Translation(type = BusTransConstant.STATION_ID_TO_NAME, mapper = "toStationId")
    private String toStationName;

    /**
     * 票价
     */
    @ExcelProperty(value = "票价")
    private BigDecimal price;

    /**
     * 优惠价
     */
    @ExcelProperty(value = "优惠价")
    private BigDecimal discountPrice;

    /**
     * 上架票数
     */
    private Integer upperLimit;

    /**
     * 排序号
     */
    @ExcelProperty(value = "排序号")
    private Long orderNo;

    /**
     * 状态 0停用 1启用
     */
    @ExcelProperty(value = "状态 0停用 1启用")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String memo;

    /**
     * 途径站数量
     */
    @ExcelProperty(value = "途径站")
    private Long stationCount;

}
