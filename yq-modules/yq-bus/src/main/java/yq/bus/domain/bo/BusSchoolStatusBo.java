package yq.bus.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import yq.common.core.validate.EditGroup;

@Data
public class BusSchoolStatusBo {
    /**
     * 学校唯一标识
     */
    @NotNull(message = "学校唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校状态
     */
    @NotNull(message = "学校状态不能为空", groups = { EditGroup.class })
    private String status;
}
