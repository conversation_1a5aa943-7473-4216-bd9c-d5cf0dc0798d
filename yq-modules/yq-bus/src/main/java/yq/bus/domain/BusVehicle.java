package yq.bus.domain;

import yq.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 车辆管理对象 bus_vehicle
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bus_vehicle")
public class BusVehicle extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车辆唯一标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 所属供应商id
     */
    private Long supplierId;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 座位数
     */
    private Long seatNumber;

    /**
     * 购买日期
     */
    private Date buyAt;

    /**
     * 备注
     */
    private String memo;

    /**
     * 记录是否被逻辑删除. 0代表存在 2代表删除
     */
    @TableLogic
    private String delFlag;

    /**
     * GPS设备IMEI号
     */
    private Long gpsImei;

    /**
     * 车载设备ID
     */
    private Long trunkId;

    /**
     * 是否绑定GPS
     */
    private Boolean bindGps;

    /**
     * 车辆行驶证
     */
    private Date drivingLicenseExpiry;

    /**
     * 道路运输证期限
     */
    private Date roadTransportExpiry;

    /**
     * 保险单开始日期
     */
    private Date insuranceStartDate;

    /**
     * 保险单结束日期
     */
    private Date insuranceEndDate;

    /**
     * 车辆是否停用
     */
    private Boolean disabled;

}
