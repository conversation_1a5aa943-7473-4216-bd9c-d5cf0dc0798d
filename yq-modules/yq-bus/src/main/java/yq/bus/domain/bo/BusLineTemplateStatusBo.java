package yq.bus.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import yq.common.core.validate.EditGroup;


@Data
public class BusLineTemplateStatusBo {
    /**
     * 线路模板唯一标识
     */
    @NotNull(message = "线路模板站点唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 线路模板状态
     */
    @NotNull(message = "线路模板状态不能为空", groups = { EditGroup.class })
    private String status;
}
