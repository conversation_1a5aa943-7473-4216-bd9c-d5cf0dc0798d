package yq.bus.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import yq.bus.domain.BusLineSchedule;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;

import java.sql.Time;
import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = BusLineSchedule.class, reverseConvertGenerate = false)
public class BusCrossLineScheduleAddBo {
    /**
     * Id
     */
    @NotNull(message = "Id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校ID
     */
    @NotNull(message = "学校ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long schoolId;

    /**
     * 线路模板Id
     */
    @NotNull(message = "线路模板Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineTemplateId;

    /**
     * 发车计划Id
     */
    @NotNull(message = "发车计划Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departPlanId;

    /**
     * 线路方向
     */
    @NotBlank(message = "线路方向不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lineKind;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 发车时间
     */
    @NotNull(message = "发车时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "HH:mm")
    private Time timeAt;

    /**
     * 供应商Id
     */
    @NotNull(message = "供应商Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 车属供应商ID
     */
    @NotNull(message = "车属供应商ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vehicleSupplierId;

    /**
     * 车辆Id
     */
    @NotNull(message = "车辆Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vehicleId;

    /**
     * 司机Id
     */
    @NotNull(message = "司机Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 启用或停用
     */
//    @NotBlank(message = "启用或停用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 座位数
     */
    @NotNull(message = "座位数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long seatNumber;

    /**
     * 已卖出座位数
     */
    private Long soldNumber;

    /**
     * 剩余座位数
     */
    private Long restSeatNumber;

    /**
     * 报班标识 0-未报班 1-已报班 2-已签退
     */
    private String signInFlag;

    /**
     * 报班时间
     */
    private Date signInTime;

    /**
     * 其他排班
     */
    private Long otherDepartPlanId;

    /**
     * 订单行程Ids
     */
    @NotNull(message = "乘客不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<Long> ticketIds;

    private List<CrossLineStationBo> crossLineStations;

}
