package yq.bus.domain.vo;

import yq.bus.domain.BusSupplier;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import yq.common.excel.annotation.ExcelDictFormat;
import yq.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 供应商管理视图对象 bus_supplier
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BusSupplier.class)
public class BusSupplierVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商唯一标识
     */
    @ExcelProperty(value = "供应商唯一标识")
    private Long id;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String name;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contact;

    /**
     * 联系人手机
     */
    @ExcelProperty(value = "联系人手机")
    private String mobile;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;


}
