package yq.bus.service;


import org.springframework.transaction.annotation.Transactional;
import yq.bus.domain.bo.BusSupplierBillBo;
import yq.bus.domain.vo.BusSupplierBillVo;
import yq.bus.domain.vo.BusSupplierBillDetailVo;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商账单Service接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface ISupplierBillService {


    TableDataInfo<BusSupplierBillDetailVo> getSupplierBillDetailPage(LocalDate date, Long supplierId, PageQuery page);

    void generateBill(LocalDate date);

    List<BusSupplierBillVo> queryList(LocalDate date);

    BusSupplierBillVo queryById(Long id);

    void regenerateBySupplier(BusSupplierBillBo bo);

    void verifyBill(BusSupplierBillBo bo);

    @Transactional(rollbackFor = Exception.class)
    void cancelVerifyBill(BusSupplierBillBo bo);

    BusSupplierBillVo queryByBillCycleAndSupplierId(String billCycle, Long supplierId);
}
