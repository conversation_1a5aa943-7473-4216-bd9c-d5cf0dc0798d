package yq.bus.service;

import yq.bus.domain.bo.BusDriverStatusBo;
import yq.bus.domain.vo.BusDriverVo;
import yq.bus.domain.bo.BusDriverBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 司机Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IBusDriverService {

    /**
     * 查询司机
     */
    BusDriverVo queryById(Long id);

    /**
     * 查询司机列表
     */
    TableDataInfo<BusDriverVo> queryPageList(BusDriverBo bo, PageQuery pageQuery);

    /**
     * 查询司机列表
     */
    List<BusDriverVo> queryList(BusDriverBo bo);

    /**
     * 新增司机
     */
    Boolean insertByBo(BusDriverBo bo);

    /**
     * 修改司机
     */
    Boolean updateByBo(BusDriverBo bo);

    /**
     * 校验并批量删除司机信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据openId查询司机
     * @param openId openId
     * @return 用户
     */
    BusDriverVo getByOpenId(String openId);

    /**
     * 根据mobile修改司机openId
     */
    BusDriverVo updateOpenIdByMobile(BusDriverBo bo);

    /**
     * 修改司机状态
     */
    Boolean disable(BusDriverStatusBo bo);
}
