package yq.bus.service.impl;


import yq.bus.domain.vo.BusStationVo;
import yq.bus.service.IBusStationService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.vo.BusLineTemplateStationVo;
import yq.bus.domain.BusLineTemplateStation;
import yq.bus.mapper.BusLineTemplateStationMapper;
import yq.bus.service.IBusLineTemplateStationService;
import javax.annotation.Resource;
import java.util.List;
import java.util.Collection;

/**
 * 线路模板站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RequiredArgsConstructor
@Service
public class BusLineTemplateStationServiceImpl implements IBusLineTemplateStationService {
    @Resource
    private final BusLineTemplateStationMapper baseMapper;
    private final IBusStationService busStationService;

    /**
     * 通过线路Id获取站点列表
     *
     * @param lineTemplateId 线路模板id
     * @return 线路站点列表
     */
    @Override
    public List<BusLineTemplateStationVo> queryListByLineTemplateId(Long lineTemplateId) {
        List<BusLineTemplateStationVo> res = baseMapper.selectVoList(Wrappers.<BusLineTemplateStation>lambdaQuery()
            .eq(BusLineTemplateStation::getLineTemplateId, lineTemplateId)
            .orderByAsc(BusLineTemplateStation::getOrderNo));
        res.forEach(station -> {
            BusStationVo busStationVo = busStationService.queryById(station.getStationId());
            station.setStationName(busStationVo.getName());
            station.setStationLongitude(busStationVo.getLongitude());
            station.setStationLatitude(busStationVo.getLatitude());
        });
        return res;
    }

    /**
     * 批量更新
     */
    @Override
    public Boolean updateBatch(List<BusLineTemplateStation> convert) {
        return baseMapper.updateBatchById(convert);
    }

    /**
     * 批量插入
     */
    @Override
    public Boolean insertBatch(List<BusLineTemplateStation> list) {
        return baseMapper.insertBatch(list);
    }

    /**
     * 批量删除线路模板站点
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusLineTemplateStation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

}
