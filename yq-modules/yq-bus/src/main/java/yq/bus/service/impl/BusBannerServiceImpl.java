package yq.bus.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusCacheConstant;
import yq.bus.service.IBusOperatorService;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusBannerBo;
import yq.bus.domain.vo.BusBannerVo;
import yq.bus.domain.BusBanner;
import yq.bus.mapper.BusBannerMapper;
import yq.bus.service.IBusBannerService;
import yq.common.redis.utils.CacheUtils;
import yq.common.satoken.utils.LoginHelper;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Collection;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * banner管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@RequiredArgsConstructor
@Service
public class BusBannerServiceImpl implements IBusBannerService {
    @Resource
    private final BusBannerMapper baseMapper;

    @Resource
    private IBusOperatorService busOperatorService;

    /**
     * 查询banner管理
     */
    @Override
    public BusBannerVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询banner管理列表
     */
    @Override
    public TableDataInfo<BusBannerVo> queryPageList(BusBannerBo bo, PageQuery pageQuery, Boolean isSchool) {
        LambdaQueryWrapper<BusBanner> lqw = buildQueryWrapper(bo);
        IPage<BusBannerVo> result;
        if (isSchool) {
            lqw.eq(bo.getSchoolId() != null, BusBanner::getSchoolId, bo.getSchoolId());
            result = baseMapper.getBannerVoPage(pageQuery.build(), lqw);
        }else {
            lqw.isNull(BusBanner::getSchoolId);
            result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询banner管理列表
     */
    @Override
    public List<BusBannerVo> queryList(BusBannerBo bo) {
        LambdaQueryWrapper<BusBanner> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增banner管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {BusCacheConstant.CLIENT_BANNER,
                        BusCacheConstant.CLIENT_POPWIN,
                        BusCacheConstant.CLIENT_BANNER_CONTENT}, allEntries = true)
    public Boolean insertByBo(BusBannerBo bo) {
        BusBanner add = MapstructUtils.convert(bo, BusBanner.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;

        if (flag) {
            bo.setId(add.getId());
            if (add.getSchoolId() != null) {
                CacheUtils.evict(BusCacheConstant.CLIENT_BANNER_SCHOOL, add.getSchoolId());
            }
        }
        return flag;
    }

    /**
     * 修改banner管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {BusCacheConstant.CLIENT_BANNER,
                        BusCacheConstant.CLIENT_POPWIN,
                        BusCacheConstant.CLIENT_BANNER_CONTENT}, allEntries = true)
    public Boolean updateByBo(BusBannerBo bo) {
        BusBanner update = MapstructUtils.convert(bo, BusBanner.class);
        validEntityBeforeSave(update);
        if (update.getSchoolId() != null) {
            CacheUtils.evict(BusCacheConstant.CLIENT_BANNER_SCHOOL, update.getSchoolId());
        }
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusBanner entity) {
        var hasPermission = busOperatorService.hasPermissionByLoginUser(entity.getSchoolId());
        ValidateUtils.isTrue(hasPermission, "没有权限操作该学校");
    }

    /**
     * 批量删除banner管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {BusCacheConstant.CLIENT_BANNER,
                        BusCacheConstant.CLIENT_POPWIN,
                        BusCacheConstant.CLIENT_BANNER_CONTENT}, allEntries = true)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {

        List<BusBanner> list = baseMapper.selectBatchIds(ids);
        // 判断banner有没有学校id，如果有则删除学校的缓存
        list.forEach(banner -> {
            if (banner.getSchoolId() != null) {
                CacheUtils.evict(BusCacheConstant.CLIENT_BANNER_SCHOOL, banner.getSchoolId());
            }
        });

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取banner
     *
     * @param clientType 客户端类型
     * @return banner列表
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.CLIENT_BANNER, key = "#clientType")
    public List<BusBannerVo> getBannerByClient(String clientType) {
        LambdaQueryWrapper<BusBanner> lqw = Wrappers.lambdaQuery();
        lqw.select(BusBanner.class, info -> !"content_html".equals(info.getColumn()));
        lqw.eq(BusBanner::getClientType, clientType)
            .eq(BusBanner::getEnabled, "1")
            .eq(BusBanner::getKind, "banner")
            .isNull(BusBanner::getSchoolId)
            .orderByAsc(BusBanner::getOrderNo)
            .orderByDesc(BusBanner::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 获取学校banner
     *
     * @param schoolId 学校id
     * @return banner列表
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.CLIENT_BANNER_SCHOOL, key = "#schoolId")
    public List<BusBannerVo> getBannerBySchoolId(Long schoolId){
        LambdaQueryWrapper<BusBanner> lqw = Wrappers.lambdaQuery();
        lqw.select(BusBanner.class, info -> !"content_html".equals(info.getColumn()));
        lqw.eq(BusBanner::getSchoolId, schoolId)
            .eq(BusBanner::getClientType, "client")
            .eq(BusBanner::getEnabled, "1")
            .eq(BusBanner::getKind, "banner")
            .orderByAsc(BusBanner::getOrderNo)
            .orderByDesc(BusBanner::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 获取Pop win
     *
     * @param clientType 客户端类型
     * @return Pop win
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.CLIENT_POPWIN, key = "#clientType")
    public BusBannerVo getPopwinByClient(String clientType) {
        LambdaQueryWrapper<BusBanner> lqw = Wrappers.lambdaQuery();
        lqw.select(BusBanner.class, info -> !"content_html".equals(info.getColumn()));
        lqw.eq(BusBanner::getClientType, clientType)
            .eq(BusBanner::getEnabled, "1")
            .eq(BusBanner::getKind, "popwin")
            .orderByAsc(BusBanner::getOrderNo)
            .orderByDesc(BusBanner::getCreateTime)
            .last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    @Cacheable(cacheNames = BusCacheConstant.CLIENT_BANNER_CONTENT, key = "#id")
    public BusBannerVo getContentById(Long id) {
        LambdaQueryWrapper<BusBanner> lqw = new LambdaQueryWrapper<>();
        lqw.select(BusBanner::getId, BusBanner::getContentHtml)
            .eq(BusBanner::getEnabled, "1")
            .eq(BusBanner::getClientType,"client")
            .eq(BusBanner::getId, id)
            .last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    private LambdaQueryWrapper<BusBanner> buildQueryWrapper(BusBannerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BusBanner> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), BusBanner::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getClientType()), BusBanner::getClientType, bo.getClientType());
        lqw.eq(StringUtils.isNotBlank(bo.getKind()), BusBanner::getKind, bo.getKind());
        lqw.eq(BusBanner::getDelFlag, "0");
        lqw.orderByDesc(BusBanner::getEnabled);
        lqw.orderByAsc(BusBanner::getOrderNo);
        lqw.orderByDesc(BusBanner::getCreateTime);
        return lqw;
    }

}
