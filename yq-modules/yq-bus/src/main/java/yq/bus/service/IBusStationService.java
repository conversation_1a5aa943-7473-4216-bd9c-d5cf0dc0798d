package yq.bus.service;

import org.springframework.cache.annotation.CacheEvict;
import yq.bus.constant.BusCacheConstant;
import yq.bus.domain.mongodb.MongodbStation;
import yq.bus.domain.vo.BusStationVo;
import yq.bus.domain.bo.BusStationBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 站点管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IBusStationService {

    /**
     * 查询站点管理
     */
    BusStationVo queryById(Long id);

    /**
     * 查询站点管理列表
     */
    TableDataInfo<BusStationVo> queryPageList(BusStationBo bo, PageQuery pageQuery);

    /**
     * 查询站点管理列表
     */
    List<BusStationVo> queryList(BusStationBo bo);

    /**
     * 根据ID获取站点名称
     */
    String selectNameById(Long id);


    /**
     * 新增站点管理
     */
    Boolean insertByBo(BusStationBo bo);

    /**
     * 修改站点管理
     */
    Boolean updateByBo(BusStationBo bo);

    /**
     * 修改学校信息时同步到学校站点信息
     */
    Boolean updateSchoolStation(BusStationBo bo);

    /**
     * 根据学校id查询站点
     */
    List<BusStationVo> getStationBySchoolId(Long schoolId, Integer lineKind);

    /**
     * 校验并批量删除站点管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);



    /**
     * 同步站点
     */
    Boolean syncStation();

    /**
     * 获取学校站点列表
     */
    List<BusStationVo> getSchoolStationList();

    /**
     * 根据经纬度查询站点
     */
    List<MongodbStation> findByLocationNear(double longitude, double latitude);

    /**
     * 修改学校站点状态
     */
    Boolean updateSchoolStationStatus(Long schoolId, String status);

    Boolean updateStationStatus(Long stationId, String status);

    void disableStation(Long stationId);

    /**
     * 通过学校id获取学校站点
     */
    List<BusStationVo> getSchoolStationBySchoolId(Long schoolId);

    /**
     * 通过id获取站点描述
     */
    String selectDescriptionById(Long id);

    BusStationVo findByLocationNearOne(String addressLongitude, String addressLatitude);

    /**
     * 根据学校id和方向查询站点
     */
    BusStationVo getSchoolStationBySchoolIdAndDirection(Long schoolId, String direction);


    List<BusStationVo> selectBatchIds(List<Long> stationIds);

    /**
     *  通过站点名称获取站点id
     */
    List<Long> selectIdsByName(String stationName);
}
