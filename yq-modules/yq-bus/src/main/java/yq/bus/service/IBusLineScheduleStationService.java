package yq.bus.service;

import yq.bus.controller.driver.bo.BusDriverArriveStationBo;
import yq.bus.domain.BusLineScheduleStation;
import yq.bus.domain.vo.BusLineScheduleStationVo;
import yq.bus.domain.bo.BusLineScheduleStationBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 排班站点Service接口
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
public interface IBusLineScheduleStationService {

    /**
     * 新增排班站点
     */
    Boolean insertBatch(List<BusLineScheduleStationBo> bos);

    /**
     * 校验并批量删除排班站点信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据排班id获取站点信息
     */
    List<BusLineScheduleStationVo> getStationByScheduleId(Long id);

    /**
     * 车辆到站
     */
    Integer arrive(BusDriverArriveStationBo bo);

    BusLineScheduleStationVo getStationByScheduleIdAndStationId(Long lineScheduleId, Long stationId);

    /**
     * 通过stationIds和schoolId找到符合的排班id
     */
    List<Long> getScheduleIdsByStationIdsAndSchoolId(List<Long> stationIds, Long schoolId);
}
