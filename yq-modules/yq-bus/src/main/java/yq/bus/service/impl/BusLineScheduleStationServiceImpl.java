package yq.bus.service.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import yq.bus.constant.BusCacheConstant;
import yq.bus.controller.driver.bo.BusDriverArriveStationBo;
import yq.bus.domain.vo.BusStationVo;
import yq.bus.service.IBusStationService;
import yq.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusLineScheduleStationBo;
import yq.bus.domain.vo.BusLineScheduleStationVo;
import yq.bus.domain.BusLineScheduleStation;
import yq.bus.mapper.BusLineScheduleStationMapper;
import yq.bus.service.IBusLineScheduleStationService;
import yq.mall.api.RemoteSendMessageService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Collection;

/**
 * 排班站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@RequiredArgsConstructor
@Service
public class BusLineScheduleStationServiceImpl implements IBusLineScheduleStationService {

    @Resource
    private final BusLineScheduleStationMapper baseMapper;
    @Resource
    @DubboReference
    private RemoteSendMessageService remoteSendMessageService;
    private final IBusStationService busStationService;


    @Override
    public Boolean insertBatch(List<BusLineScheduleStationBo> bos) {
        return baseMapper.insertBatch(MapstructUtils.convert(bos, BusLineScheduleStation.class));
    }

    /**
     * 批量删除排班站点
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Cacheable(value = BusCacheConstant.SCHEDULE_STATION, key = "#id")
    public List<BusLineScheduleStationVo> getStationByScheduleId(Long id) {
        var res = baseMapper.selectVoList(Wrappers.lambdaQuery(BusLineScheduleStation.class)
            .eq(BusLineScheduleStation::getScheduleId, id)
            .orderByAsc(BusLineScheduleStation::getOrderNo));
        res.forEach(item -> {
            BusStationVo busStationVo = busStationService.queryById(item.getStationId());
            item.setStationName(busStationVo.getName());
            item.setStationLatitude(busStationVo.getLatitude());
            item.setStationLongitude(busStationVo.getLongitude());
        });
        return res;
    }

    @Override
    @CacheEvict(value = BusCacheConstant.SCHEDULE_STATION, key = "#bo.scheduleId")
    public Integer arrive(BusDriverArriveStationBo bo) {
        int update = baseMapper.update(null, Wrappers.lambdaUpdate(BusLineScheduleStation.class)
            .eq(BusLineScheduleStation::getScheduleId, bo.getScheduleId())
            .eq(BusLineScheduleStation::getStationId, bo.getStationId())
            .set(BusLineScheduleStation::getRealTime, new Date())
            .set(BusLineScheduleStation::getArriveAddress, bo.getArriveAddress())
            .set(BusLineScheduleStation::getArriveAddressName, bo.getArriveAddressName())
            .set(BusLineScheduleStation::getArriveAddressLatitude, bo.getArriveAddressLatitude())
            .set(BusLineScheduleStation::getArriveAddressLongitude, bo.getArriveAddressLongitude()));
        if (update > 0) {
            //TODO 发送消息
            remoteSendMessageService.sendDownNoticeNotify(bo.getScheduleId(), bo.getStationId());
        }
        return update;
    }

    @Override
    public BusLineScheduleStationVo getStationByScheduleIdAndStationId(Long lineScheduleId, Long stationId) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(BusLineScheduleStation.class)
                .eq(BusLineScheduleStation::getScheduleId, lineScheduleId)
                .eq(BusLineScheduleStation::getStationId, stationId)
                .last("limit 1")
        );
    }

    /**
     * 通过stationIds和schoolId找到符合的排班id
     * @param stationIds
     * @param schoolId
     * @return scheduleIds
     */
    @Override
    public List<Long> getScheduleIdsByStationIdsAndSchoolId(List<Long> stationIds, Long schoolId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(BusLineScheduleStation.class)
                .select(BusLineScheduleStation::getScheduleId)
                .eq(BusLineScheduleStation::getSchoolId, schoolId)
                .in(BusLineScheduleStation::getStationId, stationIds))
                .stream()
                .map(BusLineScheduleStation::getScheduleId)
                .distinct()
                .toList();
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusLineScheduleStation entity) {
        //TODO 做一些数据校验,如唯一约束
    }
}
