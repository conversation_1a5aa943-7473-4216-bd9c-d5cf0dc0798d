package yq.bus.service.impl;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusCacheConstant;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusSupplierBo;
import yq.bus.domain.vo.BusSupplierVo;
import yq.bus.domain.BusSupplier;
import yq.bus.mapper.BusSupplierMapper;
import yq.bus.service.IBusSupplierService;
import javax.annotation.Resource;
import java.util.List;
import java.util.Collection;

/**
 * 供应商管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RequiredArgsConstructor
@Service
public class BusSupplierServiceImpl implements IBusSupplierService {
    @Resource
    private final BusSupplierMapper baseMapper;

    /**
     * 查询供应商管理
     */
    @Override
    public BusSupplierVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据id查询供应商名称
     *
     * @param id 供应商id
     * @return 供应商名称
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.SUPPLIER_NAME, key = "#id")
    public String selectNameById(Long id) {
        var supplier = baseMapper.selectOne(new LambdaQueryWrapper<BusSupplier>()
            .select(BusSupplier::getName)
            .eq(BusSupplier::getId, id)
        );
        return ObjectUtil.isNull(supplier) ? null : supplier.getName();
    }

    /**
     * 查询供应商管理列表
     */
    @Override
    public TableDataInfo<BusSupplierVo> queryPageList(BusSupplierBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BusSupplier> lqw = buildQueryWrapper(bo);
        Page<BusSupplierVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询供应商管理列表
     */
    @Override
    public List<BusSupplierVo> queryList(BusSupplierBo bo) {
        LambdaQueryWrapper<BusSupplier> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增供应商管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(BusSupplierBo bo) {
        BusSupplier add = MapstructUtils.convert(bo, BusSupplier.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改供应商管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = BusCacheConstant.SUPPLIER_NAME, key = "#bo.id")
    public Boolean updateByBo(BusSupplierBo bo) {
        BusSupplier update = MapstructUtils.convert(bo, BusSupplier.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusSupplier entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除供应商管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    private LambdaQueryWrapper<BusSupplier> buildQueryWrapper(BusSupplierBo bo) {
        LambdaQueryWrapper<BusSupplier> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), BusSupplier::getName, bo.getName());
        return lqw;
    }
}
