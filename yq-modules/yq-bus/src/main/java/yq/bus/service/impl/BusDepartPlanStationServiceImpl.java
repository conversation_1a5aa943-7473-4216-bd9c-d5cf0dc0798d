package yq.bus.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusCacheConstant;
import yq.bus.constant.BusLockConstant;
import yq.bus.domain.BusDepartPlan;
import yq.bus.domain.vo.BusStationVo;
import yq.bus.domain.vo.StationInfo;
import yq.bus.mapper.BusDepartPlanMapper;
import yq.bus.service.IBusLineTemplateService;
import yq.bus.service.IBusStationService;
import yq.common.core.exception.AlertException;
import yq.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusDepartPlanStationBo;
import yq.bus.domain.vo.BusDepartPlanStationVo;
import yq.bus.domain.BusDepartPlanStation;
import yq.bus.mapper.BusDepartPlanStationMapper;
import yq.bus.service.IBusDepartPlanStationService;
import yq.common.core.utils.ValidateUtils;

import javax.annotation.Resource;
import java.sql.Time;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发车计划站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@RequiredArgsConstructor
@Service
public class BusDepartPlanStationServiceImpl implements IBusDepartPlanStationService {
    @Resource
    private final BusDepartPlanStationMapper baseMapper;
    @Lazy
    @Resource
    private  IBusStationService busStationService;
    @Resource
    private final BusDepartPlanMapper departPlanMapper;

    @Resource
    @Lazy
    private IBusLineTemplateService busLineTemplateService;


    /**
     * 新增发车计划站点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByDeptPlan(List<BusDepartPlanStationBo> stations, List<BusDepartPlan> departPlans) {

        List<BusDepartPlanStation> busDepartPlanStations = departPlans.stream().flatMap(departPlan -> {
            // 初始发车时间 有多个日期循环时需要重新初始化，否则引用不变,timeAt会累加
            Time timeAt = new Time(departPlan.getTimeAt().getTime());
            return stations.stream().map(station -> {
                timeAt.setTime(timeAt.getTime() + station.getInterval() * 60 * 1000); // 累加到站时间
                BusDepartPlanStation convertedStation = MapstructUtils.convert(station, BusDepartPlanStation.class);
                convertedStation.setId(null);
                convertedStation.setDepartPlanId(departPlan.getId());
                convertedStation.setSchoolId(departPlan.getSchoolId());
                convertedStation.setArrivalTime(new Time(timeAt.getTime())); // 使用新的Time对象以避免修改原始对象
                return convertedStation;
            });
        }).collect(Collectors.toList());

        return baseMapper.insertBatch(busDepartPlanStations);
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusDepartPlanStation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除发车计划站点
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 根据发车计划id获取发车计划站点
     */
    @Override
    @Cacheable(value = BusCacheConstant.DEPART_PLAN_STATION, key = "#id")
    public List<BusDepartPlanStationVo> getStationByDepartPlanId(Long id) {
        List<BusDepartPlanStationVo> res = baseMapper.selectVoList(Wrappers.<BusDepartPlanStation>lambdaQuery()
            .eq(BusDepartPlanStation::getDepartPlanId, id)
            .orderByAsc(BusDepartPlanStation::getOrderNo));
        res.forEach(item -> {
            BusStationVo busStationVo = busStationService.queryById(item.getStationId());
            item.setStationName(busStationVo.getName());
            item.setStationLatitude(busStationVo.getLatitude());
            item.setStationLongitude(busStationVo.getLongitude());
        });
        return res;
    }

    /**
     * 增加对应计划站点的锁定座位数
     * @param planId
     * @param stationId
     * @param num
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = BusCacheConstant.DEPART_PLAN_STATION, key = "#planId")
    @Lock4j(name = BusLockConstant.LOCK_PLAN_STATION, keys = {"#planId", "#stationId"}, expire = 10000, acquireTimeout = 200)
    public Boolean addLockNum(Long planId, Long stationId, Integer num) {
        LambdaUpdateWrapper<BusDepartPlanStation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("lock_number = lock_number + " + num)
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .eq(BusDepartPlanStation::getStationId, stationId);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = BusCacheConstant.DEPART_PLAN_STATION, key = "#planId")
    @Lock4j(name = BusLockConstant.LOCK_PLAN_STATION, keys = {"#planId", "#stationId"}, expire = 10000, acquireTimeout = 200)
    public Boolean reduceLockNumAndAddSoldNum(Long planId, Long stationId, Integer num) {
        //将lock_number还原，增加可售数
        LambdaUpdateWrapper<BusDepartPlanStation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("lock_number = lock_number - " + num + ", sold_number = sold_number + " + num)
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .eq(BusDepartPlanStation::getStationId, stationId);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = BusCacheConstant.DEPART_PLAN_STATION, key = "#planId")
    @Lock4j(name = BusLockConstant.LOCK_PLAN_STATION, keys = {"#planId", "#stationId"}, expire = 10000, acquireTimeout = 200)
    public Boolean reduceSoldNumber(Long planId, Long stationId, Integer num) {
       // 将已售票数soldNumber减少
        LambdaUpdateWrapper<BusDepartPlanStation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("sold_number = sold_number - " + num)
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .eq(BusDepartPlanStation::getStationId, stationId);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = BusCacheConstant.DEPART_PLAN_STATION, key = "#planId")
    @Lock4j(name = BusLockConstant.LOCK_PLAN_STATION, keys = {"#planId", "#stationId"}, expire = 10000, acquireTimeout = 200)
    public Boolean reduceLockNum(Long planId, Long stationId, Integer num) {
        //将lock_number还原
        LambdaUpdateWrapper<BusDepartPlanStation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("lock_number = lock_number - " + num )
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .eq(BusDepartPlanStation::getStationId, stationId);
        return baseMapper.update(updateWrapper) > 0;
    }

    /**
     * 校验计划和计划站点是否超过上架限制
     * @param planId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkPlanAndStationLimit (Long planId, Long upStationId,Integer num, Boolean isVerifySeatNumber) {
        // 先将购买的票数在对应计划站点锁定
        addLockNum(planId, upStationId, num);

        BusDepartPlan departPlan = departPlanMapper.selectById(planId);

        if (departPlan == null){
            ValidateUtils.isTrue(false, "该班次不存在");
        }

        //得到所有站点
        List<BusDepartPlanStationVo> planStationVos = baseMapper.selectVoList(Wrappers.<BusDepartPlanStation>lambdaQuery()
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .orderByAsc(BusDepartPlanStation::getOrderNo));
        // 遍历计划站点中所有的已售票数和锁定票数之和
        Long soldNumber = 0L;
        Long lockNumber = 0L;
        for (BusDepartPlanStationVo planStationVo : planStationVos) {
            soldNumber += planStationVo.getSoldNumber();
            lockNumber += planStationVo.getLockNumber();
        }

        if (departPlan.getUpperLimit() == -1){
            departPlan.setUpperLimit(999999);
        }

        //得到计划限制数
        Integer upperLimit = departPlan.getUpperLimit();

        //找到上车站点
        BusDepartPlanStationVo upStation = planStationVos.stream().filter(station -> station.getStationId().equals(upStationId)).findFirst().orElse(null);
        ValidateUtils.notNull(upStation, "上车站点不存在班次中");

        // 是否校验上架限制 如：现场补票不需要校验
        if (isVerifySeatNumber) {
            // 判断是否超过计划的上架限制
            ValidateUtils.isFalse(soldNumber + lockNumber > upperLimit, "该班次余票不足");
            //判断上车站点是否超过上架限制
            ValidateUtils.isFalse(upStation.getUpperLimit() != -1L
                    && upStation.getSoldNumber() + upStation.getLockNumber() > upStation.getUpperLimit(),
                "上车站点已售罄");
        }
    }

    /**
     * 根据计划id和站点id获取计划站点
     */
    @Override
    public BusDepartPlanStationVo getPlanStationByPlanIdAndStationId(Long planId, Long stationId) {
        return baseMapper.selectVoOne(Wrappers.<BusDepartPlanStation>lambdaQuery()
            .eq(BusDepartPlanStation::getDepartPlanId, planId)
            .eq(BusDepartPlanStation::getStationId, stationId));
    }

    @Override
    public List<BusStationVo.LineTemplateInfo> getLineTemplateInfo(Long schoolId, Integer lineKind, Long stationId) {
        if (schoolId == null || lineKind == null || stationId == null) {
            return new ArrayList<>();
        }

        String dateAt = LocalDate.now().toString();
        String timeAt = LocalTime.now().toString();
        List<StationInfo> stations = baseMapper.selectStationBySchool(schoolId, lineKind, dateAt, timeAt, stationId);
        //遍历所有站点,得到templateId去重
        Set<Long> templateIds = new HashSet<>();
        for (StationInfo station : stations) {
            // 计划在售时取线路模版名称
            if (station.getPlanStatus() == 1){
                templateIds.add(station.getLineTemplateId());
            }
        }
        // 通过模板id获取模班名称
        List<BusStationVo.LineTemplateInfo> list = new ArrayList<>();
        for (Long templateId : templateIds) {
            BusStationVo.LineTemplateInfo lineTemplateInfo = new BusStationVo.LineTemplateInfo();
            String name = busLineTemplateService.selectNameById(templateId);
            lineTemplateInfo.setLineTemplateId(templateId);
            lineTemplateInfo.setLineTemplateName(name);
            list.add(lineTemplateInfo);
        }
        return list;
    }


}
