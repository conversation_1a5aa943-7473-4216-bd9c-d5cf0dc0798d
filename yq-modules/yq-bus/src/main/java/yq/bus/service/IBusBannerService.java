package yq.bus.service;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusCacheConstant;
import yq.bus.domain.BusBanner;
import yq.bus.domain.vo.BusBannerVo;
import yq.bus.domain.bo.BusBannerBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * banner管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface IBusBannerService {

    /**
     * 查询banner管理
     */
    BusBannerVo queryById(Long id);

    /**
     * 查询banner管理列表
     */
    TableDataInfo<BusBannerVo> queryPageList(BusBannerBo bo, PageQuery pageQuery, Boolean isSchool);

    /**
     * 查询banner管理列表
     */
    List<BusBannerVo> queryList(BusBannerBo bo);

    /**
     * 新增banner管理
     */
    Boolean insertByBo(BusBannerBo bo);

    /**
     * 修改banner管理
     */
    Boolean updateByBo(BusBannerBo bo);

    /**
     * 校验并批量删除banner管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取对应客户端的banner
     * @param clientType
     * @return
     */
    List<BusBannerVo> getBannerByClient(String clientType);

    /**
     * 获取对应学校的banner
     * @param schoolId
     * @return
     */
    List<BusBannerVo> getBannerBySchoolId(Long schoolId);

    /**
     * 获取对应客户端的弹窗
     * @param clientType
     * @return
     */
    BusBannerVo getPopwinByClient(String clientType);

    /**
     * 根据id获取对应的富文本内容
     */
    BusBannerVo getContentById(Long id);
}
