package yq.bus.service.impl;


import cn.hutool.core.bean.BeanUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeospatialIndex;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusSystemConfigConstant;
import yq.bus.domain.BusDepartPlanStation;
import yq.bus.domain.BusLineTemplateStation;
import yq.bus.domain.mongodb.MongodbStation;
import yq.bus.domain.vo.StationInfo;
import yq.bus.mapper.BusDepartPlanStationMapper;
import yq.bus.mapper.BusLineTemplateStationMapper;
import yq.bus.mongodbmapper.MongodbStationMapper;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.cache.annotation.Cacheable;
import yq.bus.constant.BusCacheConstant;
import yq.bus.service.IBusLineTemplateService;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusStationBo;
import yq.bus.domain.vo.BusStationVo;
import yq.bus.domain.BusStation;
import yq.bus.mapper.BusStationMapper;
import yq.bus.service.IBusStationService;
import yq.common.redis.utils.CacheUtils;
import yq.system.api.RemoteConfigService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RequiredArgsConstructor
@Service
public class BusStationServiceImpl implements IBusStationService {
    @Resource
    private final BusStationMapper baseMapper;
    @Resource
    private final BusLineTemplateStationMapper busLineTemplateStationMapper;
    private final MongoTemplate mongoTemplate;
    private final MongodbStationMapper mongodbStationMapper;
    @Resource
    private  BusDepartPlanStationMapper busDepartPlanStationMapper;
    @DubboReference
    @Resource
    private final RemoteConfigService remoteConfigService;
    @Lazy
    @Resource
    private IBusStationService self;

    @Lazy
    @Resource
    private IBusLineTemplateService templateService;

    /**
     * 查询站点管理
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.STATION, key = "#id")
    public BusStationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询站点管理列表
     */
    @Override
    public TableDataInfo<BusStationVo> queryPageList(BusStationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BusStation> lqw = buildQueryWrapper(bo);
        Page<BusStationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询站点管理列表
     */
    @Override
    public List<BusStationVo> queryList(BusStationBo bo) {
        LambdaQueryWrapper<BusStation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据id查询站点名称
     *
     * @param id 站点id
     * @return 站点名称
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.STATION_NAME, key = "#id")
    public String selectNameById(Long id) {
        var station = baseMapper.selectOne(new LambdaQueryWrapper<BusStation>()
            .select(BusStation::getName)
            .eq(BusStation::getId, id)
        );
        return ObjectUtil.isNull(station) ? null : station.getName();
    }

    /**
     * 新增站点管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = {
        BusCacheConstant.STATION,
        BusCacheConstant.STATION_NAME
    }, key = "#bo.id")
    public Boolean insertByBo(BusStationBo bo) {
        BusStation add = MapstructUtils.convert(bo, BusStation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        ValidateUtils.isTrue(flag, "新增站点管理失败");
        bo.setId(add.getId());
        add.setStatus("1");
        saveMongodbStation(add);
        return flag;
    }

    /**
     * 修改站点管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = {
        BusCacheConstant.STATION,
        BusCacheConstant.STATION_NAME,
        BusCacheConstant.STATION_DESCRIPTION
    }, key = "#bo.id")
    public Boolean updateByBo(BusStationBo bo) {
        BusStation update = MapstructUtils.convert(bo, BusStation.class);
        validEntityBeforeSave(update);
        saveMongodbStation(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 根据schoolId修改站点管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSchoolStation(BusStationBo bo) {
        // 根据schoolId查询站点
        BusStation busStation = baseMapper.selectOne(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getSchoolId, bo.getSchoolId())
        );
        busStation.setAddress(bo.getAddress());
        busStation.setLatitude(bo.getLatitude());
        busStation.setLongitude(bo.getLongitude());
        busStation.setName(bo.getName());
        ValidateUtils.isTrue(baseMapper.updateById(busStation) > 0, "根据schoolId修改站点失败");
        // 同步到mongodb
        saveMongodbStation(busStation);
        // 清除站点缓存
        CacheUtils.evict(BusCacheConstant.STATION, busStation.getId());
        return true;

    }

    /**
     * 根据学校id查询站点
     */
    @Override
    public List<BusStationVo> getStationBySchoolId(Long schoolId, Integer lineKind) {
        String dateAt = LocalDate.now().toString();
        String timeAt = LocalTime.now().toString();
        // 根据学校id和线路方向查询发车计划站点id集合
        List<StationInfo> list = busDepartPlanStationMapper.selectStationBySchool(schoolId, lineKind, dateAt, timeAt,null);
        // stationId - 模版id集合
        Map<Long, Set<Long>> stationTemplateMap = new HashMap<>();
        // 模版id - 模板对象
        Map<Long, BusStationVo.LineTemplateInfo> lineTemplateMap = new HashMap<>();

        list.forEach(stationInfo -> {
            Long stationId = stationInfo.getStationId();
            if (!stationTemplateMap.containsKey(stationId)) {
                stationTemplateMap.put(stationId, new HashSet<>());
            }
            if (stationInfo.getLineTemplateId() != null  && stationInfo.getPlanStatus() == 1) {
                Long lineTemplateId = stationInfo.getLineTemplateId();
                stationTemplateMap.get(stationId).add(lineTemplateId);
                if (!lineTemplateMap.containsKey(lineTemplateId)) {
                    BusStationVo.LineTemplateInfo lineTemplateInfo = new BusStationVo.LineTemplateInfo();
                    lineTemplateInfo.setLineTemplateId(lineTemplateId);
                    lineTemplateInfo.setLineTemplateName(templateService.selectNameById(lineTemplateId));
                    lineTemplateMap.put(lineTemplateId, lineTemplateInfo);
                }
            }
        });

        // 将对象里的 stationId 取出来
        List<BusStationVo> busStationVoList = new ArrayList<>();

        // 遍历 stationTemplateMap 通过 stationId 获取站点信息，并设置线路模板名称集合
        stationTemplateMap.forEach((stationId, lineTemplateIds) -> {
            BusStationVo busStationVo = self.queryById(stationId);
            List<BusStationVo.LineTemplateInfo> lineTemplateInfos = lineTemplateIds.stream()
                .map(lineTemplateMap::get)
                .collect(Collectors.toList());
            busStationVo.setLineTemplateList(lineTemplateInfos);
            busStationVoList.add(busStationVo);
        });
        return busStationVoList;
    }

    /**
     * 批量删除站点管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    // todo mongodb事务问题
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            StringBuffer msg = new StringBuffer();
            ids.forEach(id -> {
                Long lineStationCount = busLineTemplateStationMapper.selectCount(new LambdaQueryWrapper<BusLineTemplateStation>()
                    .select(BusLineTemplateStation::getId)
                    .eq(BusLineTemplateStation::getStationId, id)
                );
                if (lineStationCount > 0) {
                    msg.append("站点已被线路模板使用，无法删除").append("\n");
                }
                Long count = busDepartPlanStationMapper.selectCount(new LambdaQueryWrapper<BusDepartPlanStation>()
                    .select(BusDepartPlanStation::getId)
                    .eq(BusDepartPlanStation::getStationId, id)
                );
                if (count > 0) {
                    msg.append("站点已被发车计划使用，无法删除").append("\n");
                }
                // TODO 其他关联关系
            });
            ValidateUtils.isTrue(msg.isEmpty(), msg.toString());

        }
        ValidateUtils.isTrue(baseMapper.deleteBatchIds(ids) > 0, "删除站点失败");
        mongodbStationMapper.deleteAllById(ids);
        // 根据ids循环删除对应站点缓存
        ids.forEach(id -> {
            CacheUtils.evict(BusCacheConstant.STATION, id);
            CacheUtils.evict(BusCacheConstant.STATION_NAME, id);
        });
        return true;
    }


    /**
     * 同步站点 将数据库的站点同步到mongodb
     */
    @Override
    public Boolean syncStation() {
        // 获取数据库所有站点
        List<BusStation> busStations = baseMapper.selectList();
        if (busStations.isEmpty()) {
            return false;
        }
        // 获取mongodb所有站点
        List<MongodbStation> all = mongodbStationMapper.findAll();
        if (!all.isEmpty()) {
            mongodbStationMapper.deleteAll(all);
        }

        ArrayList<MongodbStation> mongodbStations = new ArrayList<>();
        busStations.forEach(busStation -> {
            mongodbStations.add(stationToMongodbStation(busStation));
        });
        List<MongodbStation> mongodbStationList = mongodbStationMapper.saveAll(mongodbStations);
        // 创建索引
        IndexOperations indexOps = mongoTemplate.indexOps(MongodbStation.class);
        IndexDefinition indexDefinition = new GeospatialIndex("location").typed(GeoSpatialIndexType.GEO_2DSPHERE);
        indexOps.ensureIndex(indexDefinition);
        return !mongodbStationList.isEmpty();
    }

    /**
     * 获取对应的学校站点
     *
     * @return 学校站点视图
     */
    @Override
    public List<BusStationVo> getSchoolStationList() {

        // 查询所有的学校站点id
        LambdaQueryWrapper<BusStation> lqw = Wrappers.lambdaQuery();
        lqw.select(BusStation::getId)
            .eq(BusStation::getStatus, "1")
            .eq(BusStation::getSchoolFlag, "1")
            .orderByAsc(BusStation::getName);

        List<BusStationVo> list = baseMapper.selectVoList(lqw);

        List<BusStationVo> busStationVoList = new ArrayList<>();
        list.forEach(station -> {
                BusStationVo busStationVo = self.queryById(station.getId());
                busStationVoList.add(busStationVo);
            });

        return busStationVoList;
    }

    /**
     * 根据经纬度查找范围内站点
     */
    @Override
    public List<MongodbStation> findByLocationNear(double longitude, double latitude) {
        // 获取系统中的参数来当半径
        String s = remoteConfigService.selectConfigByKey(BusSystemConfigConstant.STATION_SEARCH_RADIUS);
        double distance = Double.parseDouble(s);
        // 创建中心点
        Point point = new Point(longitude, latitude);
        // 创建半径
        Distance radius = new Distance(distance, Metrics.KILOMETERS);
        return mongodbStationMapper.findByLocationNearAndStatus(point, radius, "1");
    }

    /**
     * 修改学校站点状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSchoolStationStatus(Long schoolId, String status) {
        List<BusStation> list = baseMapper.selectList(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getSchoolId, schoolId)
            .eq(BusStation::getDisableFlag, false)
        );
        list.forEach(busStation -> {
            busStation.setStatus(status);
            // 删除站点缓存
            CacheUtils.evict(BusCacheConstant.STATION, busStation.getId());
            // 同步到mongodb
            saveMongodbStation(busStation);

            baseMapper.updateById(busStation);
        });
        return true;
    }

    /**
     * 修改站点状态
     */
    @Override
    @CacheEvict(cacheNames = BusCacheConstant.STATION, key = "#stationId")
    public Boolean updateStationStatus(Long stationId, String status) {
        BusStation busStation = baseMapper.selectOne(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getId, stationId)
            .eq(BusStation::getDisableFlag, false)
        );
        busStation.setStatus(status);
        // 同步到mongodb
        saveMongodbStation(busStation);
        return baseMapper.updateById(busStation) > 0;
    }

    @Override
    @CacheEvict(cacheNames = BusCacheConstant.STATION, key = "#stationId")
    public void disableStation(Long stationId) {
        BusStation busStation = baseMapper.selectOne(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getId, stationId)
        );
        busStation.setDisableFlag(true);
        // 删除mongodb站点
        mongodbStationMapper.deleteById(stationId);

        baseMapper.updateById(busStation);
    }

    @Override
    public List<BusStationVo> getSchoolStationBySchoolId(Long schoolId) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getSchoolId, schoolId)
            .eq(BusStation::getSchoolFlag, "1")
//            .eq(BusStation::getStatus, "1")
            .eq(BusStation::getDisableFlag, false)
        );

    }

    @Override
    @Cacheable(cacheNames = BusCacheConstant.STATION_DESCRIPTION, key = "#id")
    public String selectDescriptionById(Long id) {
        var station = baseMapper.selectOne(new LambdaQueryWrapper<BusStation>()
            .select(BusStation::getDescription)
            .eq(BusStation::getId, id)
        );
        return ObjectUtil.isNull(station) ? null : station.getDescription();
    }

    /**
     * 根据经纬度查询最近的一个站点
     */
    @Override
    public BusStationVo findByLocationNearOne(String addressLongitude, String addressLatitude) {
        double longitude = Double.parseDouble(addressLongitude);
        double latitude = Double.parseDouble(addressLatitude);
        Point point = new Point(longitude, latitude);

        MongodbStation nearestStation = mongodbStationMapper.findFirstByLocationNear(point);
        return self.queryById(nearestStation.getId());
    }

    @Override
    public BusStationVo getSchoolStationBySchoolIdAndDirection(Long schoolId, String direction) {
        BusStationVo busStationVo = baseMapper.selectVoOne(new LambdaQueryWrapper<BusStation>()
            .eq(BusStation::getSchoolId, schoolId)
            .eq(BusStation::getSchoolFlag, "1")
            .eq(BusStation::getDirection, direction)
            .eq(BusStation::getStatus, "1")
        );
        if (busStationVo == null) {
            return baseMapper.selectVoOne(new LambdaQueryWrapper<BusStation>()
                .eq(BusStation::getSchoolId, schoolId)
                .eq(BusStation::getSchoolFlag, "1")
                .eq(BusStation::getDirection, "-1")
                .eq(BusStation::getStatus, "1")
            );
        }
        return busStationVo;
    }

    /**
     * 批量查询站点信息
     */
    @Override
    public List<BusStationVo> selectBatchIds(List<Long> stationIds) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<BusStation>()
            .in(BusStation::getId, stationIds)
        );
    }

    /**
     * 根据站点名称查询站点id
     */
    @Override
    public List<Long> selectIdsByName(String stationName) {
        return baseMapper.selectList(new LambdaQueryWrapper<BusStation>()
            .select(BusStation::getId)
            .like(BusStation::getName, stationName)
        ).stream().map(BusStation::getId).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<BusStation> buildQueryWrapper(BusStationBo bo) {
        LambdaQueryWrapper<BusStation> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSchoolFlag()), BusStation::getSchoolFlag, bo.getSchoolFlag());
        lqw.like(StringUtils.isNotBlank(bo.getName()), BusStation::getName, bo.getName());
        lqw.orderByAsc(BusStation::getName);

        return lqw;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusStation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 数据转换
     */
    private MongodbStation stationToMongodbStation(BusStation station) {
        MongodbStation mongodbStation = new MongodbStation();
        BeanUtil.copyProperties(station, mongodbStation);
        GeoJsonPoint geoJsonPoint = new GeoJsonPoint(station.getLongitude().doubleValue(), station.getLatitude().doubleValue());
        mongodbStation.setLocation(geoJsonPoint);
        return mongodbStation;
    }

    /**
     * 将站点保存到mongodb
     */
    private void saveMongodbStation(BusStation station) {
        MongodbStation mongodbStation = stationToMongodbStation(station);
        mongodbStationMapper.save(mongodbStation);
    }
}
