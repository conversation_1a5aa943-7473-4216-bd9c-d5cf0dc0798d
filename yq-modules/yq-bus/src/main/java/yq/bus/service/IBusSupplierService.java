package yq.bus.service;

import yq.bus.domain.BusSupplier;
import yq.bus.domain.vo.BusSupplierVo;
import yq.bus.domain.bo.BusSupplierBo;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IBusSupplierService {

    /**
     * 查询供应商管理
     */
    BusSupplierVo queryById(Long id);

    /**
     * 根据ID获取供应商名字
     * @param id
     * @return
     */
    String selectNameById(Long id);

    /**
     * 查询供应商管理列表
     */
    TableDataInfo<BusSupplierVo> queryPageList(BusSupplierBo bo, PageQuery pageQuery);

    /**
     * 查询供应商管理列表
     */
    List<BusSupplierVo> queryList(BusSupplierBo bo);

    /**
     * 新增供应商管理
     */
    Boolean insertByBo(BusSupplierBo bo);

    /**
     * 修改供应商管理
     */
    Boolean updateByBo(BusSupplierBo bo);

    /**
     * 校验并批量删除供应商管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
