package yq.bus.service;

import java.util.List;
import java.util.Set;

/**
 * 学校维护人Service接口
 * <AUTHOR>
 */
public interface IBusOperatorService {

    /**
     * 添加学校维护人
     * @param schoolId 学校id
     * @param userIds 用户id
     */
    void addUsers(Long schoolId, Set<Long> userIds , Long createBy);

    /**
     * 删除学校维护人
     * @param schoolId 学校id
     * @param userId 用户id
     */
    void deleteBySchoolIdAndUserId(Long schoolId, Long userId);

    /**
     * 删除用户维护的学校
     * @param userId
     */
    void deleteByUserId(Long userId);

    /**
     * 根据学校删除全部维护人
     * @param schoolId
     */
    void deleteBySchoolId(Long schoolId);

    /**
     * 根据学校删除多个维护人
     */
    void deleteBySchoolIdAndUserIds(Long schoolId, List<Long> userIds);

    /**
     * 【数据权限】根据用户id和角色id获取维护的学校id集合
     * see {@link yq.common.mybatis.enums.DataScopeType}
     * @param userId 用户id
     * @return 学校id集合
     */
    Set<Long> getSchoolIdsByUser(Long userId);
//    Set<Long> getAndValidSchoolIdsByUser(Long userId);

    /**
     * 校验当前登录用户是否有权限操作某学校
     * @param schoolId 学校id
     * @return 是否有权限
     */
    boolean hasPermissionByLoginUser(Long schoolId);

    /**
     * 根据学校id获取维护人id集合
     * @param schoolId 学校id
     * @return 维护人id集合
     */
    Set<Long> getUserIdsBySchool(Long schoolId);

}
