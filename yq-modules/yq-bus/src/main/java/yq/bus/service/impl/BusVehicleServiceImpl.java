package yq.bus.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import yq.bus.constant.BusCacheConstant;
import yq.bus.constant.BusSystemConfigConstant;
import yq.bus.controller.admin.bo.BusVehicleSyncBo;
import yq.bus.controller.admin.vo.BusVehicleListVo;
import yq.bus.domain.BusLineSchedule;
import yq.bus.domain.vo.BusVehicleLocationVo;
import yq.bus.enums.VehicleGpsSyncStatus;
import yq.bus.mapper.BusLineScheduleMapper;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.core.utils.ValidateUtils;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.bus.domain.bo.BusVehicleBo;
import yq.bus.domain.vo.BusVehicleVo;
import yq.bus.domain.BusVehicle;
import yq.bus.mapper.BusVehicleMapper;
import yq.bus.service.IBusVehicleService;
import yq.common.redis.utils.CacheUtils;
import yq.resource.api.RemoteGpsService;
import yq.resource.api.domain.vo.RemoteVehicleGpsDeviceVo;
import yq.resource.api.domain.vo.RemoteVehicleGpsLocationVo;
import yq.system.api.RemoteConfigService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 车辆管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BusVehicleServiceImpl implements IBusVehicleService {
    @Resource
    private final BusVehicleMapper baseMapper;

    @Resource
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    @Resource
    @DubboReference
    private RemoteGpsService remoteGpsService;

    @Lazy
    @Resource
    private IBusVehicleService self;

    @Resource
    private final BusLineScheduleMapper busLineScheduleMapper;

    /**
     * 查询车辆管理
     */
    @Override
    public BusVehicleVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询车辆管理列表
     */
    @Override
    public TableDataInfo<BusVehicleVo> queryPageList(BusVehicleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BusVehicle> lqw = buildQueryWrapper(bo);
        Page<BusVehicleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询车辆管理列表
     */
    @Override
    public List<BusVehicleVo> queryList(BusVehicleBo bo) {
        LambdaQueryWrapper<BusVehicle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增车辆管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(BusVehicleBo bo) {
        BusVehicle add = MapstructUtils.convert(bo, BusVehicle.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        ValidateUtils.isTrue(flag, "新增车辆管理数据失败");
        // 同步 GPS 数据
        try {
            var gpsData = remoteGpsService.getVehicleBaseData(1, 10, add.getLicensePlate());
            if (gpsData.getRows() != null && !gpsData.getRows().isEmpty()) {
                var gpsDeviceVo = gpsData.getRows().getFirst();
                // 更新数据库中该车辆的 GPS 信息
                updateVehicleGpsInfo(gpsDeviceVo);
            }
        } catch (Exception e) {
            log.error("新增车辆时同步 GPS 数据失败: {}", e.getMessage());
        }

        return flag;
    }

    /**
     * 修改车辆管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(BusVehicleBo bo) {
        BusVehicleVo busVehicleVo = queryById(bo.getId());
        BusVehicle update = MapstructUtils.convert(bo, BusVehicle.class);
        validEntityBeforeSave(update);
        // 重置为gps未绑定状态
        update.setBindGps(false);
        update.setGpsImei(null);
        update.setTrunkId(null);
        boolean flag = baseMapper.updateById(update) == 1;
        ValidateUtils.isTrue(flag, "修改车辆管理数据失败");

        // 是否变更了车牌号
        if (!busVehicleVo.getLicensePlate().equals(update.getLicensePlate())) {
            try { // 尝试重新绑定车辆的GPS设备
                bindVehicleGpsByLicensePlate(update.getLicensePlate());
            }catch (Exception ignored){
            }
        }

        return flag;
    }


    /**
     * 批量删除车辆管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            ids.forEach(id -> {
                Long scheduleCount = busLineScheduleMapper.selectCount(new LambdaQueryWrapper<BusLineSchedule>()
                    .eq(BusLineSchedule::getVehicleId, id));
                ValidateUtils.isFalse(scheduleCount > 0, "有排班引用无法删除");
            });
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询车牌号码
     */
    @Override
    @Cacheable(cacheNames = BusCacheConstant.VEHICLE_LICENSE_PLATE, key = "#id")
    public String selectLicensePlateById(long id) {
        var licensePlate = baseMapper.selectOne(new LambdaQueryWrapper<BusVehicle>()
            .select(BusVehicle::getLicensePlate)
            .eq(BusVehicle::getId, id)
        );
        return ObjectUtil.isNull(licensePlate) ? null : licensePlate.getLicensePlate();
    }


    /**
     * 获取车辆的GPS位置信息
     *
     * @param vehicleId 车辆的本地唯一标识符
     */
    public BusVehicleLocationVo getVehicleLocation(Long vehicleId) {
        BusVehicleVo busVehicleVo = self.queryById(vehicleId);
        if (busVehicleVo == null) {
            return null;
        }

        if (busVehicleVo.getTrunkId() != null) {
            try{
                var locationVo = remoteGpsService.getVehicleLocation(busVehicleVo.getTrunkId());
                // 校验 GPS 数据的有效性
                BusVehicleLocationVo validatedLocation = validateAndConvertGpsData(locationVo, vehicleId);
                if (validatedLocation != null) {
                    // 转换坐标系
                    CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(validatedLocation.getLongitude().doubleValue(), validatedLocation.getLatitude().doubleValue());
                    validatedLocation.setLongitude(BigDecimal.valueOf(coordinate.getLng()));
                    validatedLocation.setLatitude(BigDecimal.valueOf(coordinate.getLat()));
                    return validatedLocation;
                }
            }catch (Exception e){
                log.error("获取车辆GPS位置信息失败: {}", e.getMessage());
            }
        }

        // 如果远程 GPS 数据无效，则从缓存中获取
        return CacheUtils.get(BusCacheConstant.VEHICLE_LOCATION, vehicleId);
    }

    /**
     * 如果 GPS 数据的上点时间与当前时间差距过大
     */
    private BusVehicleLocationVo validateAndConvertGpsData(RemoteVehicleGpsLocationVo locationVo, Long vehicleId) {
        Date tUpTime = locationVo.getTUpTime();
        if (tUpTime != null) {
            // 获取当前时间
            Date now = new Date();

            // 计算两个时间点之间的差距，分钟为单位
            long diffMinutes = DateUtil.between(tUpTime, now, DateUnit.MINUTE);

            // 获取gps超时时间配置
            long maxAllowedDiff = Convert.toLong(remoteConfigService.selectConfigByKey(BusSystemConfigConstant.GPS_TIME_OUT));

            // 如果上点时间与当前时间差距太大，则不使用 GPS 的时间数据
            if (diffMinutes > maxAllowedDiff) {
                return null;
            }
        }

        BusVehicleLocationVo location = new BusVehicleLocationVo();
        location.setVehicleId(vehicleId);
        location.setLatitude(locationVo.getLatitude());
        location.setLongitude(locationVo.getLongitude());
        return location;
    }


    /**
     * 获取车辆gps数据信息
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public TableDataInfo<BusVehicleListVo> getVehicleGpsData(Integer pageNum, Integer pageSize) {

        var remoteVehicleGpsVo = remoteGpsService.getVehicleBaseData(pageNum, pageSize, null);

        // 判断车辆是否在系统中，并标记同步状态
        List<BusVehicleListVo> collect = remoteVehicleGpsVo.getRows().stream()
            .map(this::convertToVehicleItem)
            .collect(Collectors.toList());

        TableDataInfo<BusVehicleListVo> tableDataInfo = TableDataInfo.build(collect);
        tableDataInfo.setTotal(remoteVehicleGpsVo.getTotal());
        return tableDataInfo;
    }

    /**
     * 确定车辆的同步状态
     *
     * @param existingVehicle          数据库中已存在的车辆信息，如果不存在则为null
     * @param remoteVehicleGpsDeviceVo 从远程API获取的车辆信息
     * @return 车辆的同步状态
     */
    private VehicleGpsSyncStatus determineVehicleSyncStatus(BusVehicle existingVehicle, RemoteVehicleGpsDeviceVo.GpsInfo remoteVehicleGpsDeviceVo) {
        // 如果车辆不在系统中，标记为非库车辆
        if (existingVehicle == null) {
            return VehicleGpsSyncStatus.NOT_IN_SYSTEM;
        }

        // 如果车辆未绑定GPS，标记为未同步
        if (!existingVehicle.getBindGps()) {
            return VehicleGpsSyncStatus.NOT_SYNCED;
        }

        // 比较IMEI号是否匹配
        boolean imeiMatches = Objects.equals(remoteVehicleGpsDeviceVo.getImei(), existingVehicle.getGpsImei());
        // 如果IMEI匹配，则标记为已同步；否则标记为未同步
        return imeiMatches ? VehicleGpsSyncStatus.SYNCED : VehicleGpsSyncStatus.NOT_SYNCED;
    }

    /**
     * 将远程API返回的车辆信息转换为本地使用的车辆列表项
     *
     * @param remoteVehicleGpsDeviceVo 从远程API获取的车辆信息
     * @return 转换后的车辆列表项
     */
    private BusVehicleListVo convertToVehicleItem(RemoteVehicleGpsDeviceVo.GpsInfo remoteVehicleGpsDeviceVo) {
        BusVehicleListVo item = new BusVehicleListVo();
        item.setId(remoteVehicleGpsDeviceVo.getTruckId());
        item.setPlateNo(remoteVehicleGpsDeviceVo.getPlateNo());
        item.setImei(remoteVehicleGpsDeviceVo.getImei());

        // 查询数据库中是否存在该车牌号的车辆
        BusVehicle existingVehicle = baseMapper.selectOne(
            new LambdaQueryWrapper<BusVehicle>()
                .eq(BusVehicle::getLicensePlate, remoteVehicleGpsDeviceVo.getPlateNo())
                .last("limit 1")
        );

        // 确定车辆的同步状态
        VehicleGpsSyncStatus syncStatus = determineVehicleSyncStatus(existingVehicle, remoteVehicleGpsDeviceVo);
        item.setSyncStatus(syncStatus.getCode());

        return item;
    }

    /**
     * 通过车牌号绑定车辆的GPS设备
     *
     * @param licensePlate 车牌号
     * @return 是否绑定成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindVehicleGpsByLicensePlate(String licensePlate) {
        // 获取远程车辆 GPS 数据，匹配车牌号
        var gpsData = remoteGpsService.getVehicleBaseData(1, 10, licensePlate);

        ValidateUtils.isTrue(CollUtil.isNotEmpty(gpsData.getRows()), "未找到对应的GPS设备数据");
        // 默认选择第一个设备来绑定
        var gpsDeviceVo = gpsData.getRows().getFirst();
        // 更新数据库中的车辆信息
        updateVehicleGpsInfo(gpsDeviceVo);

        return true;
    }


    /**
     * 同步车辆gps数据
     * @param syncBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncVehicleData(BusVehicleSyncBo syncBo) {
        // 获取远程车辆数据
        var remoteVehicleGpsVo = remoteGpsService.getVehicleBaseData(syncBo.getPageNum(), syncBo.getPageSize(), null);

        // 遍历未同步的车牌号，更新数据库中的车辆信息
        for (String plateNo : syncBo.getUnsyncedPlateNoList()) {
            remoteVehicleGpsVo.getRows().stream()
                .filter(vehicle -> vehicle.getPlateNo().equals(plateNo))
                .findFirst()
                .ifPresent(this::updateVehicleGpsInfo);
        }

        return true;
    }

    /**
     * 维护车辆是否停用状态
     */
    @Override
    public Boolean isVehicleDisabled(Long vehicleId, Boolean disabled) {
        BusVehicle vehicle = baseMapper.selectById(vehicleId);
        ValidateUtils.notNull(vehicle, "车辆不存在");

        LambdaUpdateWrapper<BusVehicle> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(BusVehicle::getId, vehicleId);
        updateWrapper.set(BusVehicle::getDisabled, disabled);
        return baseMapper.update(null, updateWrapper) > 0;
    }

    //更新数据库中的车辆gps信息
    private void updateVehicleGpsInfo(RemoteVehicleGpsDeviceVo.GpsInfo remoteVehicleGpsDeviceVo) {
        List<BusVehicle> busVehicles = baseMapper.selectList(
            new LambdaQueryWrapper<BusVehicle>()
                .eq(BusVehicle::getLicensePlate, remoteVehicleGpsDeviceVo.getPlateNo())
        );

        if (busVehicles != null) {
            for (BusVehicle busVehicle : busVehicles) {
                // 更新车辆信息
                busVehicle.setTrunkId(remoteVehicleGpsDeviceVo.getTruckId());
                busVehicle.setGpsImei(remoteVehicleGpsDeviceVo.getImei());
                busVehicle.setBindGps(true);
                baseMapper.updateById(busVehicle);
                CacheUtils.evict(BusCacheConstant.VEHICLE_LOCATION, busVehicle.getId());

            }
        }
    }


    private LambdaQueryWrapper<BusVehicle> buildQueryWrapper(BusVehicleBo bo) {
        LambdaQueryWrapper<BusVehicle> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSupplierId() != null && bo.getSupplierId() > 0, BusVehicle::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getLicensePlate()), BusVehicle::getLicensePlate, bo.getLicensePlate());
        lqw.eq(bo.getDisabled() != null, BusVehicle::getDisabled, bo.getDisabled());
        return lqw;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusVehicle entity) {
        // 判断是否有重复的车牌号,排除当前vehicleId
        Long count = baseMapper.selectCount(
            new LambdaQueryWrapper<BusVehicle>()
                .ne(entity.getId() != null, BusVehicle::getId, entity.getId())
                .eq(BusVehicle::getLicensePlate, entity.getLicensePlate())
        );
        ValidateUtils.isFalse(count > 0, "车牌号已存在");
    }
}
