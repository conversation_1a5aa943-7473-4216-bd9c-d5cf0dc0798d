package yq.bus.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;
import yq.bus.enums.SessionType;
import yq.bus.holder.BusWebSocketSessionHolder;
import yq.common.websocket.utils.WebSocketUtils;
import java.util.*;
import static yq.bus.constant.BusWebSocketConstants.PATH_PARAM;


@Slf4j
public class BusWebSocketUtils {

    /**
     * 获取路径参数
     *
     * @param session WebSocket会话
     * @return 路径参数
     */
    public static Long getPathParam(WebSocketSession session) {
        if (session.getAttributes().get(PATH_PARAM) != null) {
            return (Long) session.getAttributes().get(PATH_PARAM);
        }
        String path = Objects.requireNonNull(session.getUri()).getPath();
        // 获取路径参数中的ID
        Long id = Long.parseLong(path.substring(path.lastIndexOf("/") + 1));
        session.getAttributes().put(PATH_PARAM, id);
        return id;
    }

    /**
     * 向指定的WebSocket会话发送消息
     */
    public static void sendMessage(Long pathParam, SessionType type, String message) {
        Set<WebSocketSession> sessionMap = BusWebSocketSessionHolder.getSessionsByTypeAndPathParam(type, pathParam);
        sessionMap.forEach(session -> WebSocketUtils.sendMessage(session, message));
    }
}
