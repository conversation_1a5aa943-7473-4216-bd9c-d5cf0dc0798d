package yq.bus.websocket;


import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;
import yq.bus.domain.vo.BusLineScheduleVo;
import yq.bus.enums.SessionType;
import yq.bus.holder.BusWebSocketSessionHolder;
import yq.bus.service.IBusLineScheduleService;
import yq.bus.utils.BusWebSocketUtils;
import yq.common.core.utils.SpringUtils;
import yq.common.websocket.constant.WebSocketConstants;
import yq.common.websocket.utils.WebSocketUtils;
import yq.system.api.model.LoginUser;


import static yq.common.websocket.constant.WebSocketConstants.LOGIN_USER_KEY;


/**
 * 司机端乘客核验WebSocket处理器
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Component
@ServerEndpoint("/driver/schedule/ticket/{scheduleId}")
public class BusScheduleTicketHandler extends AbstractWebSocketHandler {

    private final IBusLineScheduleService busLineScheduleService;

    public BusScheduleTicketHandler() {
        // 手动获取Spring管理的Bean
        this.busLineScheduleService = SpringUtils.getBean(IBusLineScheduleService.class);
    }

    /**
     * 连接建立后
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session){
        LoginUser loginUser = (LoginUser) session.getAttributes().get(LOGIN_USER_KEY);
        Long pathParam = BusWebSocketUtils.getPathParam(session);
        BusLineScheduleVo busLineScheduleVo = busLineScheduleService.queryById(pathParam);
        if (! busLineScheduleVo.getDriverId().equals(loginUser.getUserId())) {
            // 如果当前用户不是该排班的司机，则不允许连接
            try {
                session.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
        BusWebSocketSessionHolder.addSession(SessionType.SCHEDULE, pathParam, session);
    }

    /**
     * 处理消息
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message){
        if (message.getPayload() instanceof String) {
            if (WebSocketConstants.PING.equals(message.getPayload())) {
                WebSocketUtils.sendMessage(session, WebSocketConstants.PONG);
                // 心跳消息
                return;
            }
        }
    }

    /**
     * 连接错误
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception){

    }

    /**
     * 连接关闭
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        BusWebSocketSessionHolder.removeSession(SessionType.SCHEDULE, session);
    }

    /**
     * 是否支持处理部分消息
     */
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

}
