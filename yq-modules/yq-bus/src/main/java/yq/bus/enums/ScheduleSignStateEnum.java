package yq.bus.enums;

import lombok.Getter;

@Getter
public enum ScheduleSignStateEnum {
    /**
     * 未签到
     */
    NOT_SIGN("0", "未签到"),
    /**
     * 已签到
     */
    SIGN_IN("1", "已签到"),
    /**
     * 已发车
     */
    DEPART("2", "已发车"),
    /**
     * 已签退
     */
    SIGN_OUT("3", "已签退");

    private final String code;
    private final String state;

    ScheduleSignStateEnum(String code, String state) {
        this.code = code;
        this.state = state;
    }
}
