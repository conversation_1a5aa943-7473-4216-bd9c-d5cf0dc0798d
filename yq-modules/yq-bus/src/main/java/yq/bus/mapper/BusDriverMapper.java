package yq.bus.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import yq.bus.domain.BusDriver;
import yq.bus.domain.bo.BusDriverBo;
import yq.bus.domain.vo.BusDriverVo;
import yq.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 司机Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface BusDriverMapper extends BaseMapperPlus<BusDriver, BusDriverVo> {
    IPage<BusDriverVo> selectPageList(IPage<BusDriverVo> page, BusDriverBo bo);
}
