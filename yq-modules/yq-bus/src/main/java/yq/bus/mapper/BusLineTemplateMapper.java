package yq.bus.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import yq.bus.domain.BusLineTemplate;
import yq.bus.domain.vo.BusLineTemplateVo;
import yq.common.mybatis.annotation.DataColumn;
import yq.common.mybatis.annotation.DataPermission;
import yq.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 线路模板Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface BusLineTemplateMapper extends BaseMapperPlus<BusLineTemplate, BusLineTemplateVo> {

    @DataPermission({
        @DataColumn(key = "schoolId", value = "school_id"),
    })
    IPage<BusLineTemplateVo> getLineVoPage(IPage<BusLineTemplate> page,  @Param(Constants.WRAPPER) Wrapper<BusLineTemplate> queryWrapper);
}
