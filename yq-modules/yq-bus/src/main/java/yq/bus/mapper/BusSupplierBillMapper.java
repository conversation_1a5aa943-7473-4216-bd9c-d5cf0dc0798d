package yq.bus.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import yq.bus.domain.BusSupplierBill;
import yq.bus.domain.vo.BusSupplierBillRecordVo;
import yq.bus.domain.vo.BusSupplierBillVo;
import yq.common.mybatis.core.mapper.BaseMapperPlus;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface BusSupplierBillMapper extends BaseMapperPlus<BusSupplierBill, BusSupplierBillVo> {
    IPage<BusSupplierBillRecordVo> getSupplierBillRecordVo(@Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate,
                                                           @Param("supplierId") Long supplierId,
                                                           @Param("page") IPage<BusSupplierBillRecordVo> page);

    List<BusSupplierBill> getScheduleCountBySupplier (@Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate,
                                                      @Param("supplierId") Long supplierId);
}
