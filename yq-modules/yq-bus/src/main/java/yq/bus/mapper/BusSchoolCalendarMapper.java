package yq.bus.mapper;


import yq.bus.domain.BusSchoolCalendar;
import yq.bus.domain.vo.BusSchoolCalendarVo;
import yq.common.mybatis.annotation.DataColumn;
import yq.common.mybatis.annotation.DataPermission;
import yq.common.mybatis.core.mapper.BaseMapperPlus;


/**
 * 校历管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@DataPermission({
    @DataColumn(key = "schoolId", value = "school_id"),
})
public interface BusSchoolCalendarMapper extends BaseMapperPlus<BusSchoolCalendar, BusSchoolCalendarVo> {

}
