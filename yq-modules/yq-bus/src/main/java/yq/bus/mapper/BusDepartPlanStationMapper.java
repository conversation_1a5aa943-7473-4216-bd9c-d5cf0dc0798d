package yq.bus.mapper;

import yq.bus.domain.BusDepartPlanStation;
import yq.bus.domain.bo.BusSearchDepartPlanBo;
import yq.bus.domain.vo.BusDepartPlanStationVo;
import yq.bus.domain.vo.BusDepartPlanVo;
import yq.bus.domain.vo.StationInfo;
import yq.common.mybatis.core.mapper.BaseMapperPlus;
import java.util.List;


/**
 * 发车计划站点Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-17
 */

public interface BusDepartPlanStationMapper extends BaseMapperPlus<BusDepartPlanStation, BusDepartPlanStationVo> {

    List<BusDepartPlanVo> selectDepartPlan(BusSearchDepartPlanBo bo);

    List<StationInfo> selectStationBySchool(Long schoolId, Integer lineKind, String dateAt, String timeAt,Long stationId);
}
