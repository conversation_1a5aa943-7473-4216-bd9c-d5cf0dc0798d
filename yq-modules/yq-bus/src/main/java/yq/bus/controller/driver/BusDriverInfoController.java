package yq.bus.controller.driver;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.bus.controller.driver.vo.BusDriverInfoVo;
import yq.bus.domain.vo.BusDriverVo;
import yq.bus.service.IBusDriverService;
import yq.common.core.domain.R;

import javax.xml.namespace.QName;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * @author：fish
 * @date：2024/8/6
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/")
public class BusDriverInfoController {

    private final IBusDriverService busDriverService;

    /**
     * 查询司机信息
     */
    @GetMapping("/info")
    public R<BusDriverInfoVo> getInfo() {
        BusDriverVo busDriverVo = busDriverService.queryById(getUserId());
        BusDriverInfoVo vo = new BusDriverInfoVo();
        vo.setId(busDriverVo.getId());
        vo.setName(busDriverVo.getName());
        vo.setMobile(busDriverVo.getMobile());
        return R.ok(vo);
    }

}
