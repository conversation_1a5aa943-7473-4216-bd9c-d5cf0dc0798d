package yq.bus.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.bus.controller.admin.bo.BusVehicleSyncBo;
import yq.bus.controller.admin.vo.BusVehicleListVo;
import yq.bus.domain.vo.BusVehicleLocationVo;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.log.enums.BusinessType;
import yq.common.excel.utils.ExcelUtil;
import yq.bus.domain.vo.BusVehicleVo;
import yq.bus.domain.bo.BusVehicleBo;
import yq.bus.service.IBusVehicleService;
import yq.common.mybatis.core.page.TableDataInfo;

/**
 * 车辆管理
 * 前端访问路由地址为:/bus/vehicle
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/vehicle")
public class BusVehicleController extends BaseController {

    private final IBusVehicleService busVehicleService;

    /**
     * 查询车辆管理列表
     */
    @SaCheckPermission("bus:vehicle:list")
    @GetMapping("/list")
    public TableDataInfo<BusVehicleVo> list(BusVehicleBo bo, PageQuery pageQuery) {
        return busVehicleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出车辆管理列表
     */
    @SaCheckPermission("bus:vehicle:export")
    @Log(title = "车辆管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusVehicleBo bo, HttpServletResponse response) {
        List<BusVehicleVo> list = busVehicleService.queryList(bo);
        ExcelUtil.exportExcel(list, "车辆管理", BusVehicleVo.class, response);
    }

    /**
     * 获取车辆管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("bus:vehicle:query")
    @GetMapping("/{id}")
    public R<BusVehicleVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(busVehicleService.queryById(id));
    }

    /**
     * 新增车辆管理
     */
    @SaCheckPermission("bus:vehicle:add")
    @Log(title = "车辆管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BusVehicleBo bo) {
        return toAjax(busVehicleService.insertByBo(bo));
    }

    /**
     * 修改车辆管理
     */
    @SaCheckPermission("bus:vehicle:edit")
    @Log(title = "车辆管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BusVehicleBo bo) {
        return toAjax(busVehicleService.updateByBo(bo));
    }

    /**
     * 删除车辆管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("bus:vehicle:remove")
    @Log(title = "车辆管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(busVehicleService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取车辆GPS数据
     */
    @SaCheckPermission("bus:vehicle:list")
    @GetMapping("/gpsData")
    public R<TableDataInfo<BusVehicleListVo>> getVehicleGpsData(
        @RequestParam(defaultValue = "1") Integer pageNum,
        @RequestParam(defaultValue = "10") Integer pageSize) {
        return R.ok(busVehicleService.getVehicleGpsData(pageNum, pageSize));
    }

    /**
     * 同步车辆GPS数据
     */
    @SaCheckPermission("bus:vehicle:edit")
    @Log(title = "车辆GPS同步", businessType = BusinessType.UPDATE)
    @PostMapping("/syncGpsData")
    public R<Boolean> syncVehicleGpsData(@RequestBody BusVehicleSyncBo syncBo) {
        return R.ok(busVehicleService.syncVehicleData(syncBo));
    }


    /**
     * 获取车辆实时位置
     */
    @GetMapping("/location/{id}")
    public R<BusVehicleLocationVo> getVehicleLocation(@PathVariable Long id) {
        return R.ok(busVehicleService.getVehicleLocation(id));
    }

    /**
     * 通过车牌号绑定车辆的GPS设备
     * @param licensePlate 车牌号
     */
    @SaCheckPermission("bus:vehicle:edit")
    @Log(title = "绑定车辆GPS", businessType = BusinessType.UPDATE)
    @PostMapping("/bindGpsByLicensePlate")
    public R<Boolean> bindGpsByLicensePlate(@NotBlank(message = "车牌号不能为空")
                                            @RequestParam String licensePlate) {
        return R.ok(busVehicleService.bindVehicleGpsByLicensePlate(licensePlate));
    }

    /**
     * 维护车辆停用状态
     */
    @SaCheckPermission("bus:vehicle:edit")
    @Log(title = "维护车辆停用状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateVehicleDisabled")
    public R<Boolean> updateVehicleDisabled( Long id, Boolean disabled) {
        return R.ok(busVehicleService.isVehicleDisabled(id, disabled));
    }
}
