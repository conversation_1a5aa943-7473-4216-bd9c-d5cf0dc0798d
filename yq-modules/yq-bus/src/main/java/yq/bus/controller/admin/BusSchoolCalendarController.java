package yq.bus.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.log.enums.BusinessType;
import yq.common.excel.utils.ExcelUtil;
import yq.bus.domain.vo.BusSchoolCalendarVo;
import yq.bus.domain.bo.BusSchoolCalendarBo;
import yq.bus.service.IBusSchoolCalendarService;
import yq.common.mybatis.core.page.TableDataInfo;

/**
 * 校历管理
 * 前端访问路由地址为:/bus/schoolCalendar
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/schoolCalendar")
public class BusSchoolCalendarController extends BaseController {

    private final IBusSchoolCalendarService busSchoolCalendarService;

/**
 * 查询校历管理列表
 */
@SaCheckPermission("bus:schoolCalendar:list")
@GetMapping("/list")
    public TableDataInfo<BusSchoolCalendarVo> list(BusSchoolCalendarBo bo, PageQuery pageQuery) {
        return busSchoolCalendarService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出校历管理列表
     */
    @SaCheckPermission("bus:schoolCalendar:export")
    @Log(title = "校历管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusSchoolCalendarBo bo, HttpServletResponse response) {
        List<BusSchoolCalendarVo> list = busSchoolCalendarService.queryList(bo);
        ExcelUtil.exportExcel(list, "校历管理", BusSchoolCalendarVo.class, response);
    }

    /**
     * 获取校历管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("bus:schoolCalendar:query")
    @GetMapping("/{id}")
    public R<BusSchoolCalendarVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(busSchoolCalendarService.queryById(id));
    }

    /**
     * 新增校历管理
     */
    @SaCheckPermission("bus:schoolCalendar:add")
    @Log(title = "校历管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BusSchoolCalendarBo bo) {
        return toAjax(busSchoolCalendarService.insertByBo(bo));
    }

    /**
     * 批量新增校历信息
     */
    @SaCheckPermission("bus:schoolCalendar:add")
    @Log(title = "批量新增校历信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batchAdd")
    public R<Void> batchAdd(@Validated(AddGroup.class) @RequestBody List<BusSchoolCalendarBo> listBo) {
        return toAjax(busSchoolCalendarService.insertBatch(listBo));
    }

    /**
     * 修改校历管理
     */
    @SaCheckPermission("bus:schoolCalendar:edit")
    @Log(title = "校历管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BusSchoolCalendarBo bo) {
        return toAjax(busSchoolCalendarService.updateByBo(bo));
    }

    /**
     * 删除校历管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("bus:schoolCalendar:remove")
    @Log(title = "校历管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(busSchoolCalendarService.deleteWithValidByIds(List.of(ids), true));
    }
}
