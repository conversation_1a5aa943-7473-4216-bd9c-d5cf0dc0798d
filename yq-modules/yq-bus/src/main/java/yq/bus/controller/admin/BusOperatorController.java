package yq.bus.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import yq.bus.constant.BusSystemConfigConstant;
import yq.bus.controller.admin.bo.BusOperatorBo;
import yq.bus.service.IBusOperatorService;
import yq.common.core.domain.R;
import yq.common.web.core.BaseController;
import yq.system.api.RemoteConfigService;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 供应商管理
 * 前端访问路由地址为:/bus/supplier
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/operator")
public class BusOperatorController extends BaseController {

    private final IBusOperatorService busOperatorService;

    @DubboReference
    @Resource
    private RemoteConfigService remoteConfigService;

    /**
     * 获取学校维护人角色id
     */
    @GetMapping("/roleId")
    public R<String> getSchoolOperatorRoleId() {
        String roleId = remoteConfigService.selectConfigByKey(BusSystemConfigConstant.SCHOOL_OPERATOR);
        return R.ok("OK", roleId);
    }


    /**
     * 添加学校维护人
     */
    @SaCheckPermission("bus:operator:add")
    @PostMapping("/add")
    public R<Void> addUsers(@RequestBody BusOperatorBo bo) {
        Long createBy = getUserId();
        HashSet<Long> userIdSet = new HashSet<>(bo.getSysUserIds());
        busOperatorService.addUsers(bo.getSchoolId(),  userIdSet, createBy);
        return R.ok();
    }

    /**
     * 删除学校维护人
     */
    @SaCheckPermission("bus:operator:delete")
    @PostMapping("/delete")
    public R<Void> deleteBySchoolIdAndUserId(@RequestBody BusOperatorBo bo) {
        busOperatorService.deleteBySchoolIdAndUserIds(bo.getSchoolId(), bo.getSysUserIds());
        return R.ok();
    }

    /**
     * 根据学校id获取维护人id集合
     */
    @SaCheckPermission("bus:operator:list")
    @GetMapping("/list")
    public R<Set<Long>> getUserIdsBySchool(@NotNull Long schoolId) {
        return R.ok(busOperatorService.getUserIdsBySchool(schoolId));
    }


}
