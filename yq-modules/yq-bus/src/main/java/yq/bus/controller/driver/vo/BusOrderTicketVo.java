package yq.bus.controller.driver.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import yq.bus.api.domain.vo.RemoteBusStationVo;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.Translation;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

@Data
public class BusOrderTicketVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 发车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 发车计划id
     */
    private Long departPlanId;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 出发站点id
     */
    private Long fromId;

    /**
     * 出发站点
     */
    @Translation(type = BusTransConstant.STATION_ID_TO_NAME, mapper = "fromId")
    private String fromName;

    @Translation(type = BusTransConstant.STATION_ID_TO_INFO, mapper = "fromId")
    private RemoteBusStationVo fromStation;

    /**
     * 下车站点
     */
    private Long toId;

    /**
     * 下车站点
     */
    @Translation(type = BusTransConstant.STATION_ID_TO_NAME, mapper = "toId")
    private String toName;

    @Translation(type = BusTransConstant.STATION_ID_TO_INFO, mapper = "toId")
    private RemoteBusStationVo toStation;


    /**
     * 状态
     */
    private String status;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;


    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 排班id
     */
    private Long scheduleId;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 实际乘坐车辆
     */
    private String actualLicensePlate;

    /**
     * 上车时间
     */
    private Date boardingTime;

    /**
     * 核验排班id
     */
    private Long verifyScheduleId;

    /**
     * 是否可以核销
     */
    private Boolean canCheck;

}
