package yq.bus.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.common.core.utils.ValidateUtils;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.log.enums.BusinessType;
import yq.common.excel.utils.ExcelUtil;
import yq.bus.domain.vo.BusBannerVo;
import yq.bus.domain.bo.BusBannerBo;
import yq.bus.service.IBusBannerService;
import yq.common.mybatis.core.page.TableDataInfo;

/**
 * banner管理
 * 前端访问路由地址为:/bus/banner
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/banner")
public class BusBannerController extends BaseController {

    private final IBusBannerService busBannerService;

    /**
     * 查询banner管理列表
     */
    @SaCheckPermission("bus:banner:list")
    @GetMapping("/list")
    public TableDataInfo<BusBannerVo> list(BusBannerBo bo, PageQuery pageQuery) {
        return busBannerService.queryPageList(bo, pageQuery, false);
    }

    /**
     * 学校管理者查询banner管理列表
     */
    @SaCheckPermission("bus:banner:listBySchool")
    @GetMapping("/listBySchool")
    public TableDataInfo<BusBannerVo> listBySchool(BusBannerBo bo, PageQuery pageQuery) {
        return busBannerService.queryPageList(bo, pageQuery, true);
    }

    /**
     * 导出banner管理列表
     */
    @SaCheckPermission("bus:banner:export")
    @Log(title = "banner管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusBannerBo bo, HttpServletResponse response) {
        List<BusBannerVo> list = busBannerService.queryList(bo);
        ExcelUtil.exportExcel(list, "banner管理", BusBannerVo.class, response);
    }

    /**
     * 获取banner管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("bus:banner:query")
    @GetMapping("/{id}")
    public R<BusBannerVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(busBannerService.queryById(id));
    }

    /**
     * 新增banner管理
     */
    @SaCheckPermission("bus:banner:add")
    @Log(title = "banner管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BusBannerBo bo) {
        return toAjax(busBannerService.insertByBo(bo));
    }

    /**
     * 修改banner管理
     */
    @SaCheckPermission("bus:banner:edit")
    @Log(title = "banner管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BusBannerBo bo) {
        return toAjax(busBannerService.updateByBo(bo));
    }

    /**
     * 删除banner管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("bus:banner:remove")
    @Log(title = "banner管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(busBannerService.deleteWithValidByIds(List.of(ids), true));
    }
}
