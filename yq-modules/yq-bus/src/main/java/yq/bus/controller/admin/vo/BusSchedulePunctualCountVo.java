package yq.bus.controller.admin.vo;

import lombok.Data;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.Translation;

@Data
public class BusSchedulePunctualCountVo {

    private Long schoolId;

    @Translation(type = BusTransConstant.SCHOOL_ID_TO_NAME, mapper = "schoolId")
    private String schoolName;

    /**
     * 准点数量
     */
    private Integer punctualCount;

    /**
     * 迟到数量
     */
    private Integer lateCount;
}
