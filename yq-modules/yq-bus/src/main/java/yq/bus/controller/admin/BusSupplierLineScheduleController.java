package yq.bus.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.bus.domain.bo.BusLineScheduleBo;
import yq.bus.domain.bo.BusVehicleBo;
import yq.bus.domain.vo.BusDriverVo;
import yq.bus.domain.vo.BusLineScheduleVo;
import yq.bus.domain.vo.BusVehicleVo;
import yq.bus.service.*;
import yq.common.excel.utils.ExcelUtil;
import yq.common.log.annotation.Log;
import yq.common.log.enums.BusinessType;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.common.web.core.BaseController;
import java.util.List;

import static yq.common.satoken.utils.LoginHelper.getUserId;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("supplier/schedule")
public class BusSupplierLineScheduleController extends BaseController {

    private final IBusLineScheduleService busLineScheduleService;
    private final IBusSupplierOperatorService busSupplierOperatorService;
    private final IBusDriverService busDriverService;
    private final IBusLineTemplateService busLineTemplateService;
    private final IBusVehicleService busVehicleService;
    private final IBusSchoolService busSchoolService;

    /**
     * 查询排班列表
     */
    @SaCheckPermission("bus:supplierLineSchedule:list")
    @GetMapping("/list")
    public TableDataInfo<BusLineScheduleVo> list(BusLineScheduleBo bo, PageQuery pageQuery) {
        // 获取当前用户的供应商id
        Long supplierIdByUser = busSupplierOperatorService.getSupplierIdByUser(getUserId());
        bo.setSupplierId(supplierIdByUser);

        return busLineScheduleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出班次管控列表
     */
    @SaCheckPermission("bus:supplierLineSchedule:export")
    @Log(title = "供应商班次管控", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusLineScheduleBo bo, HttpServletResponse response) {
        // 获取当前用户的供应商id
        Long supplierIdByUser = busSupplierOperatorService.getSupplierIdByUser(getUserId());
        bo.setSupplierId(supplierIdByUser);

        List<BusLineScheduleVo> list = busLineScheduleService.queryList(bo);
        list.forEach(vo -> {
            BusDriverVo busDriverVo = busDriverService.queryById(vo.getDriverId());
            if (busDriverVo != null) {
                vo.setDriverName(busDriverVo.getName());
                vo.setDriverPhone(busDriverVo.getMobile());
            }
            vo.setSchoolName(busSchoolService.selectNameById(vo.getSchoolId()));
            vo.setLineTemplateName(busLineTemplateService.selectNameById(vo.getLineTemplateId()));
            vo.setLicensePlate(busVehicleService.selectLicensePlateById(vo.getVehicleId()));
        });
        ExcelUtil.exportExcel(list, "班次管控", BusLineScheduleVo.class, response);
    }

    /**
     * 查询车辆管理列表
     */
    @GetMapping("/vehicle/list")
    public TableDataInfo<BusVehicleVo> vehicleList(BusVehicleBo bo, PageQuery pageQuery) {
        // 获取当前用户的供应商id
        Long supplierIdByUser = busSupplierOperatorService.getSupplierIdByUser(getUserId());
        bo.setSupplierId(supplierIdByUser);
        return busVehicleService.queryPageList(bo, pageQuery);
    }

}
