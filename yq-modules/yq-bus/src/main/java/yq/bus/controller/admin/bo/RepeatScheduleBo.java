package yq.bus.controller.admin.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author：fish
 * @date：2025/3/19
 */
@Data
public class RepeatScheduleBo {

    /**
     * 查询类型  driver / vehicle
     */
    private String type;

    private Long driverId;

    private Long vehicleId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 排除的排班id
     */
    private Long scheduleId;
}
