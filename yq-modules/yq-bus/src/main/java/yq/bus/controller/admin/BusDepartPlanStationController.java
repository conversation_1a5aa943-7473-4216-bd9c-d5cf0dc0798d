package yq.bus.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.common.idempotent.annotation.RepeatSubmit;
import yq.common.log.annotation.Log;
import yq.common.web.core.BaseController;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.core.domain.R;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;
import yq.common.log.enums.BusinessType;
import yq.common.excel.utils.ExcelUtil;
import yq.bus.domain.vo.BusDepartPlanStationVo;
import yq.bus.domain.bo.BusDepartPlanStationBo;
import yq.bus.service.IBusDepartPlanStationService;
import yq.common.mybatis.core.page.TableDataInfo;

/**
 * 发车计划站点
 * 前端访问路由地址为:/bus/departPlanStation
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/departPlanStation")
public class BusDepartPlanStationController extends BaseController {

    private final IBusDepartPlanStationService busDepartPlanStationService;

    /**
     * 根据发车计划Id查询发车计划站点列表
     */
    @SaCheckPermission("bus:departPlan:query")
    @GetMapping("/getStationByDepartPlanId/{id}")
    public R<List<BusDepartPlanStationVo>> getStationByDepartPlanId(@PathVariable Long id) {
        return R.ok(busDepartPlanStationService.getStationByDepartPlanId(id));
    }
}
