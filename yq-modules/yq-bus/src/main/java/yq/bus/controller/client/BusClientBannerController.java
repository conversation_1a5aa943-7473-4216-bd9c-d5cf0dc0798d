package yq.bus.controller.client;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yq.bus.domain.vo.BusBannerVo;
import yq.bus.service.IBusBannerService;
import yq.common.core.domain.R;
import yq.mall.api.RemoteMemberPassengerService;

import javax.annotation.Resource;
import java.util.List;

import static yq.common.satoken.utils.LoginHelper.getUserId;

/**
 * 客户端Banner
 *
 * @author：fish
 * @date：2024/7/19
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/client")
@Validated
public class BusClientBannerController {

    private final IBusBannerService busBannerService;
    @DubboReference
    @Resource
    private RemoteMemberPassengerService remoteMemberPassengerService;

    /**
     * 获取客户端Banner
     * @return Banner
     */
    @GetMapping("/banner")
    public R<List<BusBannerVo>> getBanner() {
        return R.ok(busBannerService.getBannerByClient("client"));
    }

    /**
     * 获取学校Banner
     * @param schoolId 学校id
     * @return Banner
     */
    @GetMapping("/getBannerBySchoolId")
    public R<List<BusBannerVo>> getBannerBySchoolId(Long schoolId) {
        return R.ok(busBannerService.getBannerBySchoolId(schoolId));
    }

    /**
     * 获取客户端Popwin
     * @return Popwin
     * */
    @GetMapping("/popwin")
    public R<BusBannerVo> getPopwin() {
        return R.ok(busBannerService.getPopwinByClient("client"));
    }

    /**
     * 根据id获取banner或popwin的富文本内容
     * @param id
     * @return
     */
    @GetMapping("/banner/content")
    public R<BusBannerVo> getContentById(@NotNull(message = "主键不能为空") Long id) {
        return R.ok(busBannerService.getContentById(id));
    }

}
