package yq.bus.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import yq.common.web.core.BaseController;
import yq.common.core.domain.R;
import yq.bus.domain.vo.BusLineTemplateStationVo;
import yq.bus.service.IBusLineTemplateStationService;

/**
 * 线路模板站点
 * 前端访问路由地址为:/bus/lineTemplateStation
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/lineTemplateStation")
public class BusLineTemplateStationController extends BaseController {

    private final IBusLineTemplateStationService busLineTemplateStationService;

    /**
     * 根据线路id获取线路站点
     * @param id
     * @return
     */
    @SaCheckPermission("bus:lineTemplate:query")
    @GetMapping("/queryListByLineTemplateId/{id}")
    public R<List<BusLineTemplateStationVo>> queryListByLineTemplateId(@PathVariable Long id) {
        return R.ok(busLineTemplateStationService.queryListByLineTemplateId(id));
    }
}
