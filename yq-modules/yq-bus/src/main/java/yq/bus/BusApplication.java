package yq.bus;



import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

import javax.annotation.PostConstruct;


/**
 * 客户模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication
public class BusApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(BusApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Bus模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
