package yq.bus.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import yq.bus.api.RemoteVehicleService;
import yq.bus.api.domain.vo.RemoteVehicleLocationVo;
import yq.bus.service.IBusVehicleService;
import yq.common.core.utils.MapstructUtils;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteVehicleServiceImpl implements RemoteVehicleService {

    @Resource
    private IBusVehicleService busVehicleService;

    @Override
    public RemoteVehicleLocationVo getVehicleLocation(Long vehicleId) {
        return MapstructUtils.convert(busVehicleService.getVehicleLocation(vehicleId), RemoteVehicleLocationVo.class);
    }

    @Override
    public String selectLicensePlateById(Long vehicleId) {
        return busVehicleService.selectLicensePlateById(vehicleId);
    }
}
