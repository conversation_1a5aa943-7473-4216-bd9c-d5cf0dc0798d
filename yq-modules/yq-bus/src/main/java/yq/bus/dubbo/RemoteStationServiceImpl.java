package yq.bus.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import yq.bus.api.RemoteStationService;
import yq.bus.api.domain.vo.RemoteBusStationVo;
import yq.bus.domain.vo.BusStationVo;
import yq.bus.service.IBusStationService;
import yq.common.core.utils.MapstructUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteStationServiceImpl implements RemoteStationService {

    @Resource
    private IBusStationService busStationService;

    @Override
    public String selectNameById(Long stationId) {
        return busStationService.selectNameById(stationId);
    }

    @Override
    public RemoteBusStationVo selectById(Long stationId) {
        BusStationVo busStationVo = busStationService.queryById(stationId);
        return MapstructUtils.convert(busStationVo, RemoteBusStationVo.class);
    }

    @Override
    public RemoteBusStationVo getSchoolStationBySchoolId(Long schoolId, String direction) {
        return MapstructUtils.convert(busStationService.getSchoolStationBySchoolIdAndDirection(schoolId, direction), RemoteBusStationVo.class);
    }

    @Override
    public RemoteBusStationVo findByLocationNearOne(String addressLongitude, String addressLatitude) {
        return MapstructUtils.convert(busStationService.findByLocationNearOne(addressLongitude, addressLatitude), RemoteBusStationVo.class);
    }

    /**
     * 批量查询站点信息
     * @param stationIds
     * @return
     */
    @Override
    public List<RemoteBusStationVo> selectBatchIds(List<Long> stationIds) {
        return MapstructUtils.convert(busStationService.selectBatchIds(stationIds), RemoteBusStationVo.class);
    }

}
