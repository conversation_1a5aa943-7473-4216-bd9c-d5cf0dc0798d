<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.bus.mapper.BusDepartPlanStationMapper">

    <select id="selectDepartPlan" resultType="yq.bus.domain.vo.BusDepartPlanVo">
        SELECT
            bdp.id,
            bdp.date_at,
            bdp.time_at,
            bdp.price,
            bdp.school_id,
            bdp.leave_type,
            bdp.discount_price,
            bdp.upper_limit,
            bdp.status,
            bdp.line_template_id,
            bdp.line_kind,
            bdp.ready_schedule_flag
        FROM
            bus_depart_plan_station bdps
                JOIN bus_depart_plan bdp
                     ON bdps.depart_plan_id = bdp.id
        WHERE
          bdps.station_id = #{stationId}
          AND bdps.school_id = #{schoolId}
          AND bdp.line_kind = #{lineKind}
          AND bdp.date_at >= #{dateAt}
          AND bdp.status = 1
          AND bdp.del_flag = 0
    </select>
    <select id="selectStationBySchool" resultType="yq.bus.domain.vo.StationInfo">
        SELECT
            bdps.station_id,
            bdp.line_template_id,
            bdp.`status`
        FROM
            bus_depart_plan_station bdps
                LEFT JOIN bus_depart_plan bdp ON bdps.depart_plan_id = bdp.id
        WHERE
            bdp.line_kind = #{lineKind}
            AND bdps.school_id = #{schoolId}
            AND bdp.date_at >= #{dateAt}
            AND bdps.del_flag = 0
            AND bdp.del_flag = 0
        <if test="stationId != null">
            AND bdps.station_id = #{stationId}
        </if>
        group by bdps.station_id, bdp.line_template_id, bdp.`status`
    </select>


</mapper>
