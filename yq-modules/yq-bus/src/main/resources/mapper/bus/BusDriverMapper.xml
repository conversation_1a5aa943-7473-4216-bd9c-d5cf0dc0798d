<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.bus.mapper.BusDriverMapper">

    <resultMap id="BusDriverResultMap" type="yq.bus.domain.vo.BusDriverVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="gendar" column="gendar"/>
        <result property="idCard" column="id_card"/>
        <result property="driveLicense" column="drive_license"/>
        <result property="driverYear" column="driver_year"/>
        <result property="memo" column="memo"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
    </resultMap>

    <select id="selectPageList" resultMap="BusDriverResultMap" parameterType="yq.bus.domain.bo.BusDriverBo">
        SELECT
            d.id,
            d.name,
            d.mobile,
            d.gendar,
            d.id_card,
            d.drive_license,
            d.driver_year,
            d.memo,
            d.supplier_id,
            s.name as supplier_name
        FROM bus_driver as d
        LEFT JOIN bus_supplier as s ON d.supplier_id = s.id
        where d.del_flag = '0'
        <if test="bo.name != null and bo.name != ''">
            AND d.name LIKE CONCAT('%', #{bo.name}, '%')
        </if>
        <if test="bo.mobile != null and bo.mobile != ''">
            AND d.mobile LIKE CONCAT('%', #{bo.mobile}, '%')
        </if>
        <if test="bo.supplierId != -1 and bo.supplierId != null">
            AND d.supplier_id = #{bo.supplierId}
        </if>
    </select>

</mapper>
