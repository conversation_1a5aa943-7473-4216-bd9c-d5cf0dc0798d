<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.bus.mapper.BusLineScheduleMapper">

    <select id="getScheduleVoPage" resultType="yq.bus.domain.vo.BusLineScheduleVo">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            *
        </if>
        from bus_line_schedule ${ew.getCustomSqlSegment}
    </select>

    <select id="getScheduleVoList" resultType="yq.bus.domain.BusLineSchedule">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            *
        </if>
        from bus_line_schedule ${ew.getCustomSqlSegment}
    </select>
</mapper>
