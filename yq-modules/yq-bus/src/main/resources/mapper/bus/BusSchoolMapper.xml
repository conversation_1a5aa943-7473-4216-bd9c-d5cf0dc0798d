<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yq.bus.mapper.BusSchoolMapper">

    <resultMap type="yq.bus.domain.vo.BusSchoolVo" id="BusSchoolResult" />

    <select id="pageSchool" resultMap="BusSchoolResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            *
        </if>
        from bus_school ${ew.getCustomSqlSegment}
    </select>

</mapper>
