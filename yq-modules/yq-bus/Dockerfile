#FROM ibm-semeru-runtimes:open-21-jre
FROM bellsoft/liberica-openjdk-debian:21.0.3-cds

MAINTAINER Lion Li

RUN mkdir -p /yq/bus/logs \
    /yq/bus/temp \
    /yq/skywalking/agent

WORKDIR /yq/bus

ENV SERVER_PORT=9095 LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS=""

EXPOSE ${SERVER_PORT}

ADD ./target/yq-bus.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -Dserver.port=${SERVER_PORT} \
           #-Dskywalking.agent.service_name=yq-bus \
           #-javaagent:/yq/skywalking/agent/skywalking-agent.jar \
           -XX:+HeapDumpOnOutOfMemoryError -XX:+UseZGC ${JAVA_OPTS} \
           -jar app.jar
