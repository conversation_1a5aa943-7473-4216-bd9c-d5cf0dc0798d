<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>yq</groupId>
        <artifactId>yq-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yq-bus</artifactId>

    <description>
        yq-bus模块
    </description>

    <dependencies>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-elasticsearch</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-encrypt</artifactId>
        </dependency>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dict</artifactId>
        </dependency>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-translation</artifactId>
        </dependency>
<!--        引入mongodb-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-bus</artifactId>
        </dependency>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-mall</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>${wxjava.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-websocket</artifactId>-->
<!--        </dependency>-->


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
