package yq.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import yq.common.core.utils.MapstructUtils;
import yq.common.core.utils.StringUtils;
import yq.common.mybatis.core.page.PageQuery;
import yq.common.mybatis.core.page.TableDataInfo;
import yq.system.constant.SystemCacheConstant;
import yq.system.domain.SysNotice;
import yq.system.domain.SysUser;
import yq.system.domain.bo.SysNoticeBo;
import yq.system.domain.vo.SysNoticeVo;
import yq.system.domain.vo.SysUserVo;
import yq.system.mapper.SysNoticeMapper;
import yq.system.mapper.SysUserMapper;
import yq.system.service.ISysNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 公告 服务层实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {

    private final SysNoticeMapper baseMapper;
    private final SysUserMapper userMapper;

    @Override
    public TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo notice, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNotice> lqw = buildQueryWrapper(notice);
        Page<SysNoticeVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNoticeVo selectNoticeById(Long noticeId) {
        return baseMapper.selectVoById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNoticeVo> selectNoticeList(SysNoticeBo notice) {
        LambdaQueryWrapper<SysNotice> lqw = buildQueryWrapper(notice);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysNotice> buildQueryWrapper(SysNoticeBo bo) {
        LambdaQueryWrapper<SysNotice> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNoticeTitle()), SysNotice::getNoticeTitle, bo.getNoticeTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeType()), SysNotice::getNoticeType, bo.getNoticeType());
        if (StringUtils.isNotBlank(bo.getCreateByName())) {
            SysUserVo sysUser = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, bo.getCreateByName()));
            lqw.eq(SysNotice::getCreateBy, ObjectUtil.isNotNull(sysUser) ? sysUser.getUserId() : null);
        }
        lqw.orderByAsc(SysNotice::getNoticeId);
        return lqw;
    }

    /**
     * 新增公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    @Override
    @CacheEvict(value = {SystemCacheConstant.CLIENT_SCROLLBAR, SystemCacheConstant.CLIENT_SCROLLBAR_CONTENT}, allEntries = true)
    public int insertNotice(SysNoticeBo bo) {
        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        return baseMapper.insert(notice);
    }

    /**
     * 修改公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    @Override
    @CacheEvict(value = {SystemCacheConstant.CLIENT_SCROLLBAR, SystemCacheConstant.CLIENT_SCROLLBAR_CONTENT}, allEntries = true)
    public int updateNotice(SysNoticeBo bo) {
        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        return baseMapper.updateById(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    @CacheEvict(value = {SystemCacheConstant.CLIENT_SCROLLBAR, SystemCacheConstant.CLIENT_SCROLLBAR_CONTENT}, allEntries = true)
    public int deleteNoticeById(Long noticeId) {
        return baseMapper.deleteById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    @CacheEvict(value = {SystemCacheConstant.CLIENT_SCROLLBAR, SystemCacheConstant.CLIENT_SCROLLBAR_CONTENT}, allEntries = true)
    public int deleteNoticeByIds(Long[] noticeIds) {
        return baseMapper.deleteBatchIds(Arrays.asList(noticeIds));
    }

    @Override
    @Cacheable(cacheNames = SystemCacheConstant.CLIENT_SCROLLBAR, key = "#noticeType")
    public SysNoticeVo getNoticeByNoticeType(String noticeType) {
        LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<>();
        lqw.select(SysNotice.class, info -> !"notice_content".equals(info.getColumn()));
        lqw.eq(SysNotice::getNoticeType, noticeType);
        lqw.eq(SysNotice::getStatus, "0");
        lqw.orderByDesc(SysNotice::getCreateTime);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    @Cacheable(cacheNames = SystemCacheConstant.CLIENT_SCROLLBAR_CONTENT, key = "#noticeId")
    public SysNoticeVo getNoticeContentById(Long noticeId) {
        LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<>();
        lqw.select(SysNotice::getNoticeId,SysNotice::getNoticeContent)
            .eq(SysNotice::getStatus, "0")
            .eq(SysNotice::getNoticeId, noticeId);
        return baseMapper.selectVoOne(lqw);
    }
}
