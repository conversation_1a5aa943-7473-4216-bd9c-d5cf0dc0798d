<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>yq</groupId>
        <artifactId>yq-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yq-resource</artifactId>

    <description>
        yq-resource资源服务
    </description>

    <dependencies>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-sms</artifactId>
        </dependency>

        <!-- 短信 用哪个导入哪个依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java-sms</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-mybatis</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-websocket</artifactId>
        </dependency>

        <!-- RuoYi Api System -->
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-resource</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
