package yq.resource.service.impl;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import yq.common.core.exception.AlertException;
import yq.common.core.utils.ValidateUtils;
import yq.resource.api.domain.vo.RemoteVehicleGpsDeviceVo;
import yq.resource.api.domain.vo.RemoteVehicleGpsLocationVo;
import yq.resource.config.properties.GpsProperties;
import yq.resource.service.IGpsApiService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车辆管理相关的外部API服务
 * 封装第三方接口的请求逻辑
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class GpsApiServiceImpl implements IGpsApiService {

    private final GpsProperties gpsProperties;;

    /**
     * 调用外部API获取车辆信息列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 车辆列表
     */
    @Override
    public RemoteVehicleGpsDeviceVo getVehicleBaseData(int pageNum, int pageSize, String licensePlate) {
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("pageNum", pageNum);
        jsonBody.put("pageSize", pageSize);
        if (licensePlate != null && !licensePlate.isEmpty()) {
            jsonBody.put("licensePlate", licensePlate);
        }

        String result = sendPostRequest("/cxopen/truck/getTruckBase", jsonBody);

        // 解析返回的 JSON 数据
        JSONObject response = JSONUtil.parseObj(result);

        ValidateUtils.isTrue(Boolean.TRUE.equals(response.getBool("success")), "API请求失败");

        JSONObject data = response.getJSONObject("data");
        var vehicleList = data.getJSONArray("list").stream()
            .map(item -> {
                JSONObject vehicleData = (JSONObject) item;
                var vehicleInfo = new RemoteVehicleGpsDeviceVo.GpsInfo();
                vehicleInfo.setTruckId(vehicleData.getLong("truckId"));
                vehicleInfo.setPlateNo(vehicleData.getStr("plateNo").split("\\|")[0]);
                vehicleInfo.setImei(vehicleData.getLong("imei"));
                return vehicleInfo;
            })
            .collect(Collectors.toList());

        return RemoteVehicleGpsDeviceVo.builder()
            .total(data.getInt("total"))
            .rows(vehicleList)
            .build();
    }


    /**
     * 获取车辆位置
     *
     * @param truckId 车辆ID
     * @return 车辆位置信息
     */
    @Override
    public RemoteVehicleGpsLocationVo getVehicleLocation(Long truckId) {
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("truckId", truckId);


        String result = sendPostRequest("/cxopen/truck/getTruckReal", jsonBody);

        // 解析返回的 JSON 数据
        JSONObject response = JSONUtil.parseObj(result);
        if (!Boolean.TRUE.equals(response.getBool("success"))) {
            throw new AlertException("定位数据获取失败，原因：" + response.getStr("msg"));
        }

        JSONObject data = response.getJSONObject("data");
        RemoteVehicleGpsLocationVo locationVo = new RemoteVehicleGpsLocationVo();
        locationVo.setTruckId(truckId);
        locationVo.setLatitude(new BigDecimal(data.getStr("nLat")));
        locationVo.setLongitude(new BigDecimal(data.getStr("nLong")));
        // 解析并设置上点时间
        String tUpStr = (String) data.get("tUpStr");
        if (tUpStr != null) {
            try {
                Date tUpTime = DateUtil.parse(tUpStr, "yyyy-MM-dd HH:mm:ss");
                locationVo.setTUpTime(tUpTime);
            } catch (DateException e) {
                throw new AlertException("解析时间失败，原因: " + e.getMessage());
            }
        }
        return locationVo;
    }

    /**
     * 通用的发送POST请求的方法
     *
     * @param endpoint API路径
     * @param jsonBody 请求体
     * @return 响应结果
     */
    private String sendPostRequest(String endpoint, Map<String, Object> jsonBody) {
        long timestamp = System.currentTimeMillis();
        String sign = generateSign(timestamp);
        String url = buildUrl(endpoint, timestamp, sign);

        System.out.println("请求参数: " + JSONUtil.toJsonStr(jsonBody));
        return HttpRequest.post(url)
            .body(JSONUtil.toJsonStr(jsonBody))
            .contentType("application/json")
            .timeout(gpsProperties.getTimeout())
            .execute()
            .body();
    }

    /**
     * 构建完整的请求URL
     *
     * @param endpoint API接口路径
     * @param timestamp 当前时间戳
     * @param sign 签名
     * @return 完整的URL
     */
    private String buildUrl(String endpoint, long timestamp, String sign) {
        return gpsProperties.getBaseurl() + endpoint + "?appKey=" + URLUtil.encode(gpsProperties.getAppKey()) +
            "&timestamp=" + timestamp +
            "&sign=" + sign;
    }

    /**
     * 生成请求签名
     *
     * @param timestamp 当前时间戳
     * @return 生成的签名
     */
    private String generateSign(long timestamp) {
        String signAppend = gpsProperties.getAppKey() + gpsProperties.getSecretKey() + timestamp + gpsProperties.getSalt();
        return SecureUtil.md5(signAppend).toUpperCase();
    }
}
