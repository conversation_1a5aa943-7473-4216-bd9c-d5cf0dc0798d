package yq.resource.service;

import yq.resource.api.domain.vo.RemoteVehicleGpsDeviceVo;
import yq.resource.api.domain.vo.RemoteVehicleGpsLocationVo;

public interface IGpsApiService {

    /**
     * 获取车辆基础数据
     * @param pageNum
     * @param pageSize
     */
    RemoteVehicleGpsDeviceVo getVehicleBaseData(int pageNum, int pageSize, String licensePlate);

    RemoteVehicleGpsLocationVo getVehicleLocation(Long truckId);
}
