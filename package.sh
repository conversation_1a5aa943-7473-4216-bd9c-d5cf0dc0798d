#!/bin/bash

#git checkout master
#git pull
#
#mvn clean package -Dmaven.test.skip=true -Pprod

# Create dist directory and deploy.sh file
rm -rf target

timestamp=$(date +%Y%m%d%H%M)
mkdir -p "target/${timestamp}"

touch "target/${timestamp}/deploy.sh"
echo "target/" >> "target/${timestamp}/deploy.sh"

# Step 2: Define modules
modules=(
    "yq-modules/yq-bus"
    "yq-modules/yq-mall"
    "yq-modules/yq-resource"
    "yq-modules/yq-system"
    "yq-modules/yq-job"
    "yq-auth"
    "yq-gateway"
    "yq-visual/yq-monitor"
    "yq-visual/yq-nacos"
    "yq-visual/yq-seata-server"
    "yq-visual/yq-sentinel-dashboard"
    "yq-visual/yq-snailjob-server"
)
# Initialize an array to store selected services
selected_services=()

# Loop through each module and ask the user if they want to include it
for module in "${modules[@]}"
do
    read -p "是否打包${module}? (y/n): " include
    if [ "$include" == "y" ]
    then
        # Extract the last part of the module path
        service_name=$(basename "${module}")

        # Create module directory inside dist
        mkdir -p "target/${timestamp}/${service_name}/target"

        # Copy module files to dist
        cp "${module}/Dockerfile" "target/${timestamp}/${service_name}/"
        cp "${module}/target/${service_name}.jar" "target/${timestamp}/${service_name}/target"

        # Append module deploy command to deploy.sh
        echo "docker build -t xbus/${service_name}:2.2.0 ${service_name}" >> "target/${timestamp}/deploy.sh"

        # Add the service name to the selected services array
        selected_services+=("${service_name}")
    fi
done

# Append docker compose up command to deploy.sh with selected services
echo "cd /docker/xbus || exit" >> "target/${timestamp}/deploy.sh"
echo "docker compose up -d ${selected_services[*]}" >> "target/${timestamp}/deploy.sh"

# Create tarball of target directory with current date and time in the filename

tar -czvf "target/target_${timestamp}.tar.gz" -C "target" "${timestamp}"

echo "打包完成，生成的文件为 target/target_${timestamp}.tar.gz"
echo "解压: tar -xzvf target_${timestamp}.tar.gz"
