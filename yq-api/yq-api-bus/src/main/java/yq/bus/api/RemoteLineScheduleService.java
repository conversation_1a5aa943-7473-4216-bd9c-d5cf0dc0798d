package yq.bus.api;

import yq.bus.api.domain.vo.RemoteBusLineScheduleStationVo;
import yq.bus.api.domain.vo.RemoteBusLineScheduleVo;
import yq.common.core.exception.AlertException;

import java.util.List;

public interface RemoteLineScheduleService {

    void addPassenger(String massage, Long scheduleId);

    int systemSignOut() throws AlertException;

    void updateSoldNumber(Long scheduleId, Integer soldNumber, Integer lockNumber);

    RemoteBusLineScheduleVo queryById(Long scheduleId);

    List<RemoteBusLineScheduleStationVo> getScheduleStation(Long id);

    void validateTicket(Long scheduleId, Long stationId, Integer size, Boolean isVerifyTime) throws AlertException;

    int systemSendScheduleNotify();

    List<RemoteBusLineScheduleVo> queryListByPlanId(Long departPlanId);
}
