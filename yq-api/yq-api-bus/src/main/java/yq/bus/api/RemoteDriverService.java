package yq.bus.api;

import yq.bus.api.domain.vo.RemoteBusDriverVo;
import yq.bus.api.model.XcxLoginDriver;
import yq.common.core.exception.AlertException;
import yq.common.core.exception.ServiceException;

public interface RemoteDriverService {

    RemoteBusDriverVo selectById(Long driverId);

    XcxLoginDriver getBySessionCode(String loginCode) throws ServiceException;

    XcxLoginDriver register(String loginCode, String mobileCode) throws ServiceException;

    void vailDriverStatus(Long driverId) throws AlertException;
}
