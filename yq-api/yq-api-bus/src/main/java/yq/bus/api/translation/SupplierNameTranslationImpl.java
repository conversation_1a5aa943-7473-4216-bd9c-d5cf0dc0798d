package yq.bus.api.translation;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.bus.api.RemoteSupplierService;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;

@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.SUPPLIER_ID_TO_NAME)
public class SupplierNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteSupplierService remoteSupplierService;

    @Override
    public String translation(Object key, String other) {
        return remoteSupplierService.selectNameById(Long.parseLong(key.toString()));
    }
}
