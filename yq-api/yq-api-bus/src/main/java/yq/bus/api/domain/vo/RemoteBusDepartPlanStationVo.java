package yq.bus.api.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

/**
 * 远程出发计划站点Vo
 * <AUTHOR>
 */
@Data
public class RemoteBusDepartPlanStationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private Long id;
    private Long departPlanId;
    private Long stationId;
    private String stationName;
    private BigDecimal stationLongitude;
    private BigDecimal stationLatitude;
    private Long schoolId;
    private Long interval;
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Time arrivalTime;
    /**
     * 上架票数
     */
    private Long upperLimit;
    /**
     * 售出票数
     */
    private Long soldNumber;
    /**
     * 锁定票数
     */
    private Long lockNumber;
    private Long orderNo;
    private String memo;

}
