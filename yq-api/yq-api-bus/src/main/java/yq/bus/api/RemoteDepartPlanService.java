package yq.bus.api;

import yq.bus.api.domain.vo.RemoteBusDepartPlan;
import yq.bus.api.domain.vo.RemoteBusDepartPlanStationVo;
import yq.bus.api.domain.vo.RemoteStatisticsVo;

import java.util.List;

/**
 * 远程出发计划服务
 * <AUTHOR>
 */
public interface RemoteDepartPlanService {

    /**
     * 获得出发计划
     * @param id id
     * @return 出发计划
     */
    RemoteBusDepartPlan getDepartPlanById(Long id);

    /**
     * 获取计划中的站点id集合
     */
    List<RemoteBusDepartPlanStationVo> getDepartPlanStationIds(Long id);

    /**
     * 获取发车计划的模板id
     */
    Long getTemplateIdByDepartPlanId(Long id);

    /**
     * 统计今日计划和排班数量
     */
    RemoteStatisticsVo countWeeklyPlanAndSchedule();

    /**
     * 根据线路模板id获取出发计划id集合
     */
    List<Long> getDepartPlanIdsByLineTemplateId(Long lineTemplateId);

    /**
     *  定时任务，自动起售发车计划
     */
    int autoSaleDepartPlan();
}
