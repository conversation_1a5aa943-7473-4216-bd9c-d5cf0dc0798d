package yq.bus.api.translation;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import yq.bus.api.RemoteStationService;
import yq.bus.api.domain.vo.RemoteBusStationVo;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;


/**
 * 站点信息翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.STATION_ID_TO_INFO)
public class StationInfoTranslationImpl implements TranslationInterface<RemoteBusStationVo> {

    @DubboReference
    private RemoteStationService remoteStationService;

    @Override
    public RemoteBusStationVo translation(Object key, String other) {
        return remoteStationService.selectById(Long.parseLong(key.toString()));
    }
}
