package yq.bus.api.domain.vo;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

@Data
public class RemoteBusDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 司机姓名
     */
    private String name;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 所属供应商id
     */
    private Long supplierId;

    /**
     * 所属供应商名称
     */
    private String supplierName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 性别（0男 1女 2未知）
     */
    private String gendar;

    /**
     * 驾照编号
     */
    private String driveLicense;

    /**
     * 驾龄
     */
    private Long driverYear;

    /**
     * 备注
     */
    private String memo;
}
