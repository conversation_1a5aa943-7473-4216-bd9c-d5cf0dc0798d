package yq.bus.api.translation;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.bus.api.RemoteStationService;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;

/**
 * 站点名称翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.STATION_ID_TO_NAME)
public class StationNameTranslationImpl implements TranslationInterface<String>  {

    @DubboReference
    private RemoteStationService remoteStationService;

    @Override
    public String translation(Object key, String other) {
        return remoteStationService.selectNameById(Long.parseLong(key.toString()));
    }
}
