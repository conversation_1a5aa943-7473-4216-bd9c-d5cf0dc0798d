package yq.bus.api.domain.vo;


import lombok.Data;
import yq.common.translation.annotation.Translation;
import yq.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class RemoteBusStationVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 站点id
     */
    private Long id;
    /**
     * 站点名称
     */
    private String name;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 站点描述
     */
    private String description;

    /**
     * 站点地址
     */
    private String address;

    /**
     * 图片OssId
     */
    private String imgOssId;

    /**
     * 站点实景图url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "imgOssId")
    private String imgUrl;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 学校标识
     */
    private String schoolFlag;

    /**
     * 站点状态
     */
    private String status;

    /**
     * 方向
     */
    private String direction;
}
