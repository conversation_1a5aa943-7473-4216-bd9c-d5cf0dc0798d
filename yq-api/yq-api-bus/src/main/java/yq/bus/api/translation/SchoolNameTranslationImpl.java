package yq.bus.api.translation;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import yq.bus.api.RemoteSchoolService;
import yq.bus.api.translation.constant.BusTransConstant;
import yq.common.translation.annotation.TranslationType;
import yq.common.translation.core.TranslationInterface;

/**
 * 学校名称翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = BusTransConstant.SCHOOL_ID_TO_NAME)
public class SchoolNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteSchoolService remoteSchoolService;

    @Override
    public String translation(Object key, String other) {
        return remoteSchoolService.selectNameById(Long.parseLong(key.toString()));
    }

}
