package yq.mall.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Time;
import java.util.Date;
import java.util.List;

@Data
public class RemoteCrossLineScheduleBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * Id
     */
    private Long id;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 线路模板Id
     */
    private Long lineTemplateId;

    /**
     * 发车计划Id
     */
    private Long departPlanId;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 日期
     */
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 车辆Id
     */
    private Long vehicleId;

    /**
     * 司机Id
     */
    private Long driverId;

    /**
     * 启用或停用
     */
    private String status;

    /**
     * 座位数
     */
    private Long seatNumber;

    /**
     * 已卖出座位数
     */
    private Long soldNumber;

    /**
     * 剩余座位数
     */
    private Long restSeatNumber;

    /**
     * 报班标识 0-未报班 1-已报班 2-已签退
     */
    private String signInFlag;

    /**
     * 报班时间
     */
    private Date signInTime;

    /**
     * 其他排班
     */
    private Long otherDepartPlanId;

    /**
     * 订单行程Ids
     */
    private List<Long> ticketIds;

    private List<RemoteCrossLineStationBo> crossLineStations;


}
