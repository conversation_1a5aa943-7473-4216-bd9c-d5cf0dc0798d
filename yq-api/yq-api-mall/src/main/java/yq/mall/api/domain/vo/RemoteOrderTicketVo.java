package yq.mall.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

@Data
public class RemoteOrderTicketVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 发车日期
     */
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 发车计划id
     */
    private Long departPlanId;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 出发站点id
     */
    private Long fromId;

    /**
     * 出发站点
     */
    private String fromName;

    /**
     * 下车站点
     */
    private Long toId;

    /**
     * 下车站点
     */
    private String toName;

    /**
     * 状态
     */
    private String status;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;


    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 排班id
     */
    private Long scheduleId;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 实际乘坐车辆
     */
    private String actualLicensePlate;

    /**
     * 上车时间
     */
    private Date boardingTime;

    /**
     * 核验排班id
     */
    private Long verifyScheduleId;

    private Date createTime;

    /**
     * 是否跨线
     */
    private Boolean crossLineFlag;

    /**
     * 是否后付费
     */
    private Boolean postPayFlag;
}
