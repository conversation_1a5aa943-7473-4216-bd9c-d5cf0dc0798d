package yq.mall.api;

import org.springframework.scheduling.annotation.Async;
import yq.mall.api.domain.bo.RemoteScheduleBo;

/**
 * 发送消息服务
 */
public interface RemoteSendMessageService {

    /**
     * 发送上车通知
     */
    void sendUpNoticeNotify(Long ticketId);

    /**
     * 发送下车通知
     */
    void sendDownNoticeNotify(Long scheduleId, Long stationId);

    @Async
    void sendPlanChangeNotify(Long departPlanId);

    @Async
    void sendScheduleChangeNotify(Long scheduleId);
}
