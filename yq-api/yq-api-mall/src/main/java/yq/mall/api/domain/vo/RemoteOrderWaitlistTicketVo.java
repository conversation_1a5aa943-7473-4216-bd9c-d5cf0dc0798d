package yq.mall.api.domain.vo;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * @author：fish
 * @date：2024/10/24
 */
@Data
public class RemoteOrderWaitlistTicketVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 发车日期
     */
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 发车计划id
     */
    private Long departPlanId;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 出发站点
     */
    private Long fromId;

    /**
     * 出发站点
     */
    private String fromName;

    /**
     * 下车站点
     */
    private Long toId;

    /**
     * 下车站点
     */
    private String toName;

    /**
     * 状态
     */
    private String status;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     *
     */
    private BigDecimal price;

    /**
     *
     */
    private BigDecimal payPrice;

    /**
     *
     */
    private BigDecimal couponPrice;

    /**
     *
     */
    private Long refundId;

    /**
     * 0 未售后 10售后中  20售后成功
     */
    private Long refundStatus;

    /**
     * 是否已支付
     */
    private Integer payStatus;

    /**
     * 入学时间
     */
    private String backDate;

    /**
     * 是否候补成功 0-失败 1-成功
     */
    private Long waitlistStatus;

    /**
     * 车票id
     */
    private Long ticketId;


}
