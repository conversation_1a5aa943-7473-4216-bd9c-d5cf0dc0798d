package yq.mall.api.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import yq.common.core.validate.AddGroup;
import yq.common.core.validate.EditGroup;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

@Data
public class RemoteOrderTicketBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 发车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateAt;

    /**
     * 发车时间
     */
    private Time timeAt;

    /**
     * 发车计划id
     */
    private Long departPlanId;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 出发站点
     */
    private Long fromId;

    /**
     * 下车站点
     */
    private Long toId;

    /**
     * 状态
     */
    private String status;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 排班id
     */
    private Long scheduleId;

    /**
     * 线路方向
     */
    private String lineKind;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 实际乘坐车辆
     */
    private String actualLicensePlate;

    /**
     * 上车时间
     */
    private Date boardingTime;

    /**
     * 核验排班id
     */
    private Long verifyScheduleId;
}
