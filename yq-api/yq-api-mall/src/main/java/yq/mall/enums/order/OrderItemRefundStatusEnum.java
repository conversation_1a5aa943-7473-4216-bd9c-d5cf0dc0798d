package yq.mall.enums.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 订单项售后状态枚举
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum OrderItemRefundStatusEnum {

    NONE(0, "未退款"),
    APPLY(10, "退款中"),
    SUCCESS(20, "退款成功");

    private final Integer type;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values())
        .mapToInt(OrderItemRefundStatusEnum::getType)
        .toArray();

}
