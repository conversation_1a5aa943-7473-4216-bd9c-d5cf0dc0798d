package yq.mall.enums.promotion;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 优惠券有效期类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CouponValidTypeEnum {

    DATE(1, "固定日期"),
    TERM(2, "领取之后"),
    ;

    private final Integer type;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values())
        .mapToInt(CouponValidTypeEnum::getType)
        .toArray();

}
