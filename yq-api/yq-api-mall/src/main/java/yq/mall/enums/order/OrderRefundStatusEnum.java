package yq.mall.enums.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 订单退款状态枚举
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum OrderRefundStatusEnum {

    NONE(0, "未退款"),
    PART(10, "部分退款"),
    ALL(20, "全部退款");

    private final Integer type;
    private final String name;
    public static final int[] ARRAYS = Arrays.stream(values())
        .mapToInt(OrderRefundStatusEnum::getType)
        .toArray();

}
