package yq.mall.enums.refund;

import lombok.Getter;

import java.util.Arrays;

/**
 * 订单退款类型枚举
 * <AUTHOR>
 */
@Getter
public enum RefundStatusEnum {

    NONE(0, "未售后"),
    APPLY(10, "售后中"),
    SUCCESS(20, "售后成功");

    private final Integer type;
    private final String name;

    RefundStatusEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static final int[] ARRAYS = Arrays.stream(values())
        .mapToInt(RefundStatusEnum::getType)
        .toArray();
}
