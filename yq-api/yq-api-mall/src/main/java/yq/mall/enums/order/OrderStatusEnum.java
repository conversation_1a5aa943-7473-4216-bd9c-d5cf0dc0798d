package yq.mall.enums.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 订单状态枚举
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum OrderStatusEnum {

    UNPAID(0, "待支付"),
    PAYED(10, "已支付"),
    COMPLETED(20, "已完成"),
    CANCELED(30, "已取消");

    private final Integer type;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values())
        .mapToInt(OrderStatusEnum::getType)
        .toArray();

}
