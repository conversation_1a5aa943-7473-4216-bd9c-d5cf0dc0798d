package yq.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车票状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TicketStatusEnum {

    /**
     * 未排班
     */
    NOT_SCHEDULED("0", "未排班"),
    /**
     * 待核验
     */
    PENDING_VERIFICATION("1", "待核验"),
    /**
     * 已核验
     */
    VERIFIED("2", "已核验"),
    /**
     * 已完成
     */
    COMPLETED("3", "已完成"),
    /**
     * 已取消
     */
    CANCELLED("4", "已取消");

    private final String code;
    private final String desc;


}
