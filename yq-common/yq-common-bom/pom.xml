<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>yq</groupId>
    <artifactId>yq-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        yq-common-bom common依赖项
    </description>

    <properties>
        <revision>2.2.0-SNAPSHOT</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 字典 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-dict</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-loadbalancer</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-logstash</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-sentinel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-skylog</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-prometheus</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>yq</groupId>
                <artifactId>yq-common-nacos</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
