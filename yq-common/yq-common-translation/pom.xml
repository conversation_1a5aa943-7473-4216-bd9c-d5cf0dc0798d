<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>yq</groupId>
        <artifactId>yq-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yq-common-translation</artifactId>

    <description>
        yq-common-translation 通用翻译功能
    </description>

    <dependencies>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-json</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-resource</artifactId>
        </dependency>

    </dependencies>

</project>
