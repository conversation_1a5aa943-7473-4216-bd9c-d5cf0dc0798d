package yq.common.core.utils;

import cn.hutool.core.util.ObjectUtil;
import yq.common.core.exception.AlertException;

/**
 * (校验判断工具 ， 可以消除部分if判断)
 * <AUTHOR>
 */
public class ValidateUtils {

    // ~ isTrue
    // ==================

    /**
     * 必须为真
     * @param value
     * @param msg
     */
    public static void isTrue(Boolean value, String msg) {
        if (!Boolean.TRUE.equals(value)) {
            throw new AlertException(msg);
        }
    }


    // ~ isFalse
    // ==================

    /**
     * 必须为假
     * @param value
     * @param msg
     */
    public static void isFalse(Boolean value, String msg) {
        if (!Boolean.FALSE.equals(value)) {
            throw new AlertException(msg);
        }
    }

    // ~ notBlank
    // ==================

    public static void notBlank(String value, String msg) {
        if (StringUtils.isBlank(value)) {
            throw new AlertException(msg);
        }
    }

    // ~ notNull
    // ==================

    public static void notNull(Object value, String msg) {
        if (value == null) {
            throw new AlertException(msg);
        }
    }

    public static void isNull(Object value, String msg) {
        if (value != null) {
            throw new AlertException(msg);
        }
    }

    /**
     * 必须相同
     * @param expected
     * @param actual
     * @param msg
     */
    public static void equals(Object expected, Object actual, String msg) {
        if (!ObjectUtil.equals(expected, actual)) {
            throw new AlertException(msg);
        }
    }

    /**
     * 必须不相同
     * @param expected
     * @param actual
     * @param msg
     */
    public static void notEquals(Object expected, Object actual, String msg) {
        if (ObjectUtil.equals(expected, actual)) {
            throw new AlertException(msg);
        }
    }

}
