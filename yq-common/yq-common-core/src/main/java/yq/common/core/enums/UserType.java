package yq.common.core.enums;

import yq.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型
 * 针对多套 用户体系
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserType {

    /**
     * 管理端
     */
    SYS_USER("sys_user"),

    /**
     * 客户端
     */
    MALL_USER("member_user"),

    /**
     * 司机端
     */
    DRIVER_USER("driver_user");

    private final String userType;

    public static UserType getUserType(String str) {
        for (UserType value : values()) {
            if (StringUtils.contains(str, value.getUserType())) {
                return value;
            }
        }
        throw new RuntimeException("'UserType' not found By " + str);
    }
}
