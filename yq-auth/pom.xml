<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>yq</groupId>
        <artifactId>yq-cloud</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yq-auth</artifactId>

    <description>
        yq-auth 认证授权中心
    </description>

    <dependencies>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-sentinel</artifactId>
        </dependency>

        <!-- RuoYi Common Security-->
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-social</artifactId>
        </dependency>

        <!-- RuoYi Common Log -->
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-tenant</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>yq</groupId>
                    <artifactId>yq-common-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-mall</artifactId>
        </dependency>

        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-api-bus</artifactId>
        </dependency>

        <!-- 自定义负载均衡(多团队开发使用) -->
        <dependency>
            <groupId>yq</groupId>
            <artifactId>yq-common-loadbalancer</artifactId>
<!--            <scope>test</scope>-->
        </dependency>

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>yq</groupId>-->
<!--            <artifactId>yq-common-prometheus</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
