package yq.auth.form;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import yq.common.core.domain.model.LoginBody;

/**
 * 三方登录对象
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class XcxLoginBody extends LoginBody {

    /**
     * 小程序id(多个小程序时使用)
     */
    private String appid;

    /**
     * 小程序手机code
     */
    private String mobileCode;

    @NotBlank(message = "sessionCode不能为空")
    private String loginCode;

}
